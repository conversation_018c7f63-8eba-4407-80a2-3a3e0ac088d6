using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Alicres.SerialPort.Tests.TestHelpers;

/// <summary>
/// Mock串口管理器实现，用于测试环境
/// </summary>
public class MockSerialPortManager : ISerialPortManager
{
    private readonly ILogger<MockSerialPortManager> _logger;
    private readonly IServiceProvider _serviceProvider;
    private readonly Dictionary<string, ISerialPortService> _serialPorts;
    private readonly object _lockObject;
    private bool _disposed;

    /// <summary>
    /// 串口添加事件
    /// </summary>
    public event EventHandler<SerialPortAddedEventArgs>? SerialPortAdded;

    /// <summary>
    /// 串口移除事件
    /// </summary>
    public event EventHandler<SerialPortRemovedEventArgs>? SerialPortRemoved;

    /// <summary>
    /// 全局数据接收事件
    /// </summary>
    public event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 全局状态变化事件
    /// </summary>
    public event EventHandler<SerialPortStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 全局错误事件
    /// </summary>
    public event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;



    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="serviceProvider">服务提供程序</param>
    public MockSerialPortManager(ILogger<MockSerialPortManager> logger, IServiceProvider serviceProvider)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        _serialPorts = new Dictionary<string, ISerialPortService>();
        _lockObject = new object();
    }

    /// <summary>
    /// 所有串口服务
    /// </summary>
    public IReadOnlyDictionary<string, ISerialPortService> SerialPorts
    {
        get
        {
            lock (_lockObject)
            {
                return new Dictionary<string, ISerialPortService>(_serialPorts);
            }
        }
    }

    /// <summary>
    /// 创建串口服务
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <returns>串口服务实例</returns>
    public ISerialPortService CreateSerialPort(SerialPortConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        ThrowIfDisposed();

        lock (_lockObject)
        {
            if (_serialPorts.ContainsKey(configuration.PortName))
            {
                _logger.LogWarning("串口 {PortName} 已存在，返回现有实例", configuration.PortName);
                return _serialPorts[configuration.PortName];
            }

            // 创建Mock串口服务
            var mockLogger = _serviceProvider.GetRequiredService<ILogger<MockSerialPortService>>();
            var service = new MockSerialPortService(configuration, mockLogger);
            
            _serialPorts[configuration.PortName] = service;
            _logger.LogInformation("创建Mock串口服务: {PortName}", configuration.PortName);

            // 订阅服务事件
            SubscribeToServiceEvents(service);

            // 触发添加事件
            SerialPortAdded?.Invoke(this, new SerialPortAddedEventArgs(service));

            return service;
        }
    }

    /// <summary>
    /// 移除串口服务
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>如果成功移除返回 true，否则返回 false</returns>
    public bool RemoveSerialPort(string portName)
    {
        ArgumentNullException.ThrowIfNull(portName);
        ThrowIfDisposed();

        lock (_lockObject)
        {
            if (!_serialPorts.TryGetValue(portName, out var service))
            {
                _logger.LogWarning("串口 {PortName} 不存在", portName);
                return false;
            }

            // 取消订阅事件
            UnsubscribeFromServiceEvents(service);

            // 关闭并释放服务
            if (service.IsConnected)
            {
                service.CloseAsync().Wait(TimeSpan.FromSeconds(5));
            }
            service.Dispose();

            _serialPorts.Remove(portName);
            _logger.LogInformation("移除串口服务: {PortName}", portName);

            // 触发移除事件
            SerialPortRemoved?.Invoke(this, new SerialPortRemovedEventArgs(portName));

            return true;
        }
    }

    /// <summary>
    /// 获取指定端口的串口服务
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>串口服务实例，如果不存在返回 null</returns>
    public ISerialPortService? GetSerialPort(string portName)
    {
        ArgumentNullException.ThrowIfNull(portName);
        ThrowIfDisposed();

        lock (_lockObject)
        {
            return _serialPorts.TryGetValue(portName, out var service) ? service : null;
        }
    }

    /// <summary>
    /// 检查端口是否存在
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>如果存在返回 true，否则返回 false</returns>
    public bool ContainsPort(string portName)
    {
        ArgumentNullException.ThrowIfNull(portName);
        ThrowIfDisposed();

        lock (_lockObject)
        {
            return _serialPorts.ContainsKey(portName);
        }
    }

    /// <summary>
    /// 打开所有串口
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功打开的串口数量</returns>
    public async Task<int> OpenAllAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        var services = SerialPorts.Values.ToList();
        var openTasks = services.Select(service => service.OpenAsync(cancellationToken));
        var results = await Task.WhenAll(openTasks);

        var successCount = results.Count(result => result);
        _logger.LogInformation("批量打开串口完成，成功: {SuccessCount}/{TotalCount}", successCount, services.Count);

        return successCount;
    }

    /// <summary>
    /// 关闭所有串口
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功关闭的串口数量</returns>
    public async Task<int> CloseAllAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        var services = SerialPorts.Values.ToList();
        var closeTasks = services.Select(service => service.CloseAsync(cancellationToken));
        var results = await Task.WhenAll(closeTasks);

        var successCount = results.Count(result => result);
        _logger.LogInformation("批量关闭串口完成，成功: {SuccessCount}/{TotalCount}", successCount, services.Count);

        return successCount;
    }

    /// <summary>
    /// 向所有连接的串口广播数据
    /// </summary>
    /// <param name="data">要广播的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功发送的串口数量</returns>
    public async Task<int> BroadcastAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);
        ThrowIfDisposed();

        var connectedServices = SerialPorts.Values.Where(s => s.IsConnected).ToList();
        var sendTasks = connectedServices.Select(async service =>
        {
            try
            {
                await service.SendAsync(data, cancellationToken);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "向串口 {PortName} 广播数据失败", service.Configuration.PortName);
                return false;
            }
        });

        var results = await Task.WhenAll(sendTasks);
        var successCount = results.Count(result => result);

        _logger.LogInformation("广播数据完成，成功: {SuccessCount}/{TotalCount}, 数据长度: {DataLength}", 
            successCount, connectedServices.Count, data.Length);

        return successCount;
    }

    /// <summary>
    /// 向所有连接的串口广播文本数据
    /// </summary>
    /// <param name="text">要广播的文本</param>
    /// <param name="encoding">文本编码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>成功发送的串口数量</returns>
    public async Task<int> BroadcastTextAsync(string text, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(text);
        encoding ??= Encoding.UTF8;

        var data = encoding.GetBytes(text);
        return await BroadcastAsync(data, cancellationToken);
    }

    /// <summary>
    /// 添加现有的串口服务
    /// </summary>
    /// <param name="service">串口服务</param>
    public void AddSerialPort(ISerialPortService service)
    {
        ArgumentNullException.ThrowIfNull(service);
        ThrowIfDisposed();

        lock (_lockObject)
        {
            var portName = service.Configuration.PortName;
            if (_serialPorts.ContainsKey(portName))
            {
                _logger.LogWarning("串口 {PortName} 已存在", portName);
                return;
            }

            _serialPorts[portName] = service;
            _logger.LogInformation("添加串口服务: {PortName}", portName);

            // 订阅服务事件
            SubscribeToServiceEvents(service);

            // 触发添加事件
            SerialPortAdded?.Invoke(this, new SerialPortAddedEventArgs(service));
        }
    }

    /// <summary>
    /// 获取系统中可用的串口列表
    /// </summary>
    /// <returns>可用串口名称数组</returns>
    public string[] GetAvailablePorts()
    {
        return new[] { "COM1", "COM2", "COM3", "COM_TEST", "COM_LOOPBACK", "COM_MOCK" };
    }

    /// <summary>
    /// 获取所有串口的状态
    /// </summary>
    /// <returns>端口状态字典</returns>
    public Dictionary<string, SerialPortStatus> GetAllStatus()
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            return _serialPorts.ToDictionary(
                kvp => kvp.Key,
                kvp => kvp.Value.Status
            );
        }
    }

    /// <summary>
    /// 获取连接统计信息
    /// </summary>
    /// <returns>连接统计</returns>
    public (int Total, int Connected, int Disconnected, int Error) GetConnectionStatistics()
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            var total = _serialPorts.Count;
            var connected = _serialPorts.Values.Count(s => s.Status.ConnectionState == SerialPortConnectionState.Connected);
            var disconnected = _serialPorts.Values.Count(s => s.Status.ConnectionState == SerialPortConnectionState.Disconnected);
            var error = _serialPorts.Values.Count(s => s.Status.ConnectionState == SerialPortConnectionState.Error);

            return (total, connected, disconnected, error);
        }
    }

    /// <summary>
    /// 订阅服务事件
    /// </summary>
    /// <param name="service">串口服务</param>
    private void SubscribeToServiceEvents(ISerialPortService service)
    {
        service.DataReceived += OnServiceDataReceived;
        service.StatusChanged += OnServiceStatusChanged;
        service.ErrorOccurred += OnServiceErrorOccurred;
    }

    /// <summary>
    /// 取消订阅服务事件
    /// </summary>
    /// <param name="service">串口服务</param>
    private void UnsubscribeFromServiceEvents(ISerialPortService service)
    {
        service.DataReceived -= OnServiceDataReceived;
        service.StatusChanged -= OnServiceStatusChanged;
        service.ErrorOccurred -= OnServiceErrorOccurred;
    }

    /// <summary>
    /// 处理服务数据接收事件
    /// </summary>
    private void OnServiceDataReceived(object? sender, SerialPortDataReceivedEventArgs e)
    {
        DataReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 处理服务状态变化事件
    /// </summary>
    private void OnServiceStatusChanged(object? sender, SerialPortStatusChangedEventArgs e)
    {
        StatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 处理服务错误事件
    /// </summary>
    private void OnServiceErrorOccurred(object? sender, SerialPortErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    /// <summary>
    /// 检查是否已释放
    /// </summary>
    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MockSerialPortManager));
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                // 关闭所有串口
                CloseAllAsync().Wait(TimeSpan.FromSeconds(10));

                // 释放所有服务
                lock (_lockObject)
                {
                    foreach (var service in _serialPorts.Values)
                    {
                        try
                        {
                            service.Dispose();
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "释放串口服务时发生异常");
                        }
                    }
                    _serialPorts.Clear();
                }

                _logger.LogInformation("Mock串口管理器已释放");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "释放Mock串口管理器时发生异常");
            }
            finally
            {
                _disposed = true;
            }
        }
    }
}
