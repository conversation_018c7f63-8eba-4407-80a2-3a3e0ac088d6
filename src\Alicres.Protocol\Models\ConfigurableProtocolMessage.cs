using Alicres.Protocol.Configuration;
using Alicres.Protocol.Interfaces;
using System.Collections.Concurrent;

namespace Alicres.Protocol.Models;

/// <summary>
/// 可配置的协议消息基类，支持动态字段定义和解析
/// </summary>
public class ConfigurableProtocolMessage : ProtocolMessage
{
    /// <summary>
    /// 协议配置
    /// </summary>
    public ProtocolConfiguration Configuration { get; }

    /// <summary>
    /// 字段值字典
    /// </summary>
    private readonly ConcurrentDictionary<string, object?> _fieldValues = new();

    /// <summary>
    /// 协议名称
    /// </summary>
    public override string ProtocolName => Configuration.Name;

    /// <summary>
    /// 消息类型
    /// </summary>
    public override string MessageType => "ConfigurableMessage";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">协议配置</param>
    public ConfigurableProtocolMessage(ProtocolConfiguration configuration) : base()
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));

        var (isValid, errors) = configuration.Validate();
        if (!isValid)
        {
            throw new ArgumentException($"协议配置无效: {string.Join(", ", errors)}");
        }

        // 初始化固定值字段
        InitializeFixedValueFields();
    }

    /// <summary>
    /// 从原始数据构造消息
    /// </summary>
    /// <param name="configuration">协议配置</param>
    /// <param name="rawData">原始数据</param>
    public ConfigurableProtocolMessage(ProtocolConfiguration configuration, byte[] rawData) : this(configuration)
    {
        RawData = rawData ?? throw new ArgumentNullException(nameof(rawData));
        ParseFromRawData(rawData);
    }

    /// <summary>
    /// 初始化固定值字段
    /// </summary>
    private void InitializeFixedValueFields()
    {
        foreach (var field in Configuration.Fields.Where(f => f.Type == ProtocolFieldType.FixedValue))
        {
            if (field.FixedValue != null)
            {
                _fieldValues[field.Name] = field.FixedValue;
            }
        }
    }

    /// <summary>
    /// 获取字段值
    /// </summary>
    /// <typeparam name="T">字段值类型</typeparam>
    /// <param name="fieldName">字段名称</param>
    /// <returns>字段值</returns>
    public T? GetFieldValue<T>(string fieldName)
    {
        if (_fieldValues.TryGetValue(fieldName, out var value))
        {
            if (value is T typedValue)
                return typedValue;
            
            // 尝试类型转换
            try
            {
                return (T?)Convert.ChangeType(value, typeof(T));
            }
            catch
            {
                return default(T);
            }
        }
        
        return default(T);
    }

    /// <summary>
    /// 设置字段值
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <param name="value">字段值</param>
    public void SetFieldValue(string fieldName, object? value)
    {
        var field = Configuration.GetField(fieldName);
        if (field == null)
        {
            throw new ArgumentException($"字段不存在: {fieldName}");
        }

        _fieldValues[fieldName] = value;
    }

    /// <summary>
    /// 获取所有字段值
    /// </summary>
    /// <returns>字段值字典</returns>
    public IReadOnlyDictionary<string, object?> GetAllFieldValues()
    {
        return _fieldValues.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
    }

    /// <summary>
    /// 从原始数据解析消息字段
    /// </summary>
    /// <param name="rawData">原始数据</param>
    private void ParseFromRawData(byte[] rawData)
    {
        if (rawData.Length < Configuration.MinFrameLength)
        {
            throw new ArgumentException($"数据长度不足，最小需要 {Configuration.MinFrameLength} 字节，实际 {rawData.Length} 字节");
        }

        var currentOffset = 0;
        var dataLength = rawData.Length;

        foreach (var field in Configuration.Fields)
        {
            if (currentOffset >= rawData.Length)
                break;

            try
            {
                var fieldValue = ParseField(field, rawData, currentOffset, dataLength);
                _fieldValues[field.Name] = fieldValue.Value;
                currentOffset = fieldValue.NextOffset;
            }
            catch (Exception ex)
            {
                throw new InvalidOperationException($"解析字段 '{field.Name}' 时发生错误: {ex.Message}", ex);
            }
        }
    }

    /// <summary>
    /// 解析单个字段
    /// </summary>
    /// <param name="field">字段描述符</param>
    /// <param name="data">数据</param>
    /// <param name="offset">当前偏移量</param>
    /// <param name="totalLength">总数据长度</param>
    /// <returns>解析结果</returns>
    private (object? Value, int NextOffset) ParseField(ProtocolFieldDescriptor field, byte[] data, int offset, int totalLength)
    {
        switch (field.Type)
        {
            case ProtocolFieldType.FixedValue:
                return ParseFixedValue(field, data, offset);

            case ProtocolFieldType.Byte:
                return ParseByte(field, data, offset);

            case ProtocolFieldType.UInt16:
                return ParseUInt16(field, data, offset);

            case ProtocolFieldType.UInt32:
                return ParseUInt32(field, data, offset);

            case ProtocolFieldType.Length:
                return ParseLength(field, data, offset);

            case ProtocolFieldType.VariableData:
                return ParseVariableData(field, data, offset, totalLength);

            case ProtocolFieldType.Checksum:
                return ParseChecksum(field, data, offset);

            case ProtocolFieldType.Custom:
                return ParseCustomField(field, data, offset);

            default:
                throw new NotSupportedException($"不支持的字段类型: {field.Type}");
        }
    }

    /// <summary>
    /// 解析固定值字段
    /// </summary>
    private (object? Value, int NextOffset) ParseFixedValue(ProtocolFieldDescriptor field, byte[] data, int offset)
    {
        if (field.FixedValue == null)
            throw new InvalidOperationException($"固定值字段 '{field.Name}' 的固定值未设置");

        if (offset + field.Length > data.Length)
            throw new ArgumentException($"数据长度不足以解析字段 '{field.Name}'");

        var fieldData = new byte[field.Length];
        Array.Copy(data, offset, fieldData, 0, field.Length);

        // 验证固定值
        if (!fieldData.SequenceEqual(field.FixedValue))
        {
            throw new InvalidDataException($"字段 '{field.Name}' 的值不匹配，期望: {BitConverter.ToString(field.FixedValue)}，实际: {BitConverter.ToString(fieldData)}");
        }

        return (fieldData, offset + field.Length);
    }

    /// <summary>
    /// 解析单字节字段
    /// </summary>
    private (object? Value, int NextOffset) ParseByte(ProtocolFieldDescriptor field, byte[] data, int offset)
    {
        if (offset >= data.Length)
            throw new ArgumentException($"数据长度不足以解析字段 '{field.Name}'");

        return (data[offset], offset + 1);
    }

    /// <summary>
    /// 解析双字节字段
    /// </summary>
    private (object? Value, int NextOffset) ParseUInt16(ProtocolFieldDescriptor field, byte[] data, int offset)
    {
        if (offset + 2 > data.Length)
            throw new ArgumentException($"数据长度不足以解析字段 '{field.Name}'");

        ushort value;
        if (field.ByteOrder == ByteOrder.LittleEndian)
        {
            value = (ushort)(data[offset] | (data[offset + 1] << 8));
        }
        else
        {
            value = (ushort)((data[offset] << 8) | data[offset + 1]);
        }

        return (value, offset + 2);
    }

    /// <summary>
    /// 解析四字节字段
    /// </summary>
    private (object? Value, int NextOffset) ParseUInt32(ProtocolFieldDescriptor field, byte[] data, int offset)
    {
        if (offset + 4 > data.Length)
            throw new ArgumentException($"数据长度不足以解析字段 '{field.Name}'");

        uint value;
        if (field.ByteOrder == ByteOrder.LittleEndian)
        {
            value = (uint)(data[offset] | (data[offset + 1] << 8) | (data[offset + 2] << 16) | (data[offset + 3] << 24));
        }
        else
        {
            value = (uint)((data[offset] << 24) | (data[offset + 1] << 16) | (data[offset + 2] << 8) | data[offset + 3]);
        }

        return (value, offset + 4);
    }

    /// <summary>
    /// 解析长度字段
    /// </summary>
    private (object? Value, int NextOffset) ParseLength(ProtocolFieldDescriptor field, byte[] data, int offset)
    {
        // 长度字段的解析逻辑与对应的数值类型相同
        return field.Length switch
        {
            1 => ParseByte(field, data, offset),
            2 => ParseUInt16(field, data, offset),
            4 => ParseUInt32(field, data, offset),
            _ => throw new NotSupportedException($"不支持的长度字段大小: {field.Length}")
        };
    }

    /// <summary>
    /// 解析变长数据字段
    /// </summary>
    private (object? Value, int NextOffset) ParseVariableData(ProtocolFieldDescriptor field, byte[] data, int offset, int totalLength)
    {
        // 计算数据字段的长度
        var lengthField = Configuration.GetLengthField();
        if (lengthField == null)
        {
            throw new InvalidOperationException("变长数据字段需要配置长度字段");
        }

        var declaredLength = GetFieldValue<int>(lengthField.Name);
        var dataFieldLength = CalculateDataFieldLength(declaredLength, lengthField.LengthMeaning, totalLength);

        if (offset + dataFieldLength > data.Length)
        {
            throw new ArgumentException($"数据长度不足以解析变长字段 '{field.Name}'");
        }

        var fieldData = new byte[dataFieldLength];
        Array.Copy(data, offset, fieldData, 0, dataFieldLength);

        return (fieldData, offset + dataFieldLength);
    }

    /// <summary>
    /// 解析校验字段
    /// </summary>
    private (object? Value, int NextOffset) ParseChecksum(ProtocolFieldDescriptor field, byte[] data, int offset)
    {
        // 校验字段的解析逻辑与对应的数值类型相同
        return field.Length switch
        {
            1 => ParseByte(field, data, offset),
            2 => ParseUInt16(field, data, offset),
            4 => ParseUInt32(field, data, offset),
            _ => throw new NotSupportedException($"不支持的校验字段大小: {field.Length}")
        };
    }

    /// <summary>
    /// 解析自定义字段
    /// </summary>
    private (object? Value, int NextOffset) ParseCustomField(ProtocolFieldDescriptor field, byte[] data, int offset)
    {
        if (field.CustomDeserializer == null)
            throw new InvalidOperationException($"自定义字段 '{field.Name}' 的反序列化函数未设置");

        if (offset + field.Length > data.Length)
            throw new ArgumentException($"数据长度不足以解析自定义字段 '{field.Name}'");

        var fieldData = new byte[field.Length];
        Array.Copy(data, offset, fieldData, 0, field.Length);

        var value = field.CustomDeserializer(fieldData);
        return (value, offset + field.Length);
    }

    /// <summary>
    /// 计算数据字段长度
    /// </summary>
    private int CalculateDataFieldLength(int declaredLength, LengthFieldMeaning meaning, int totalLength)
    {
        return meaning switch
        {
            LengthFieldMeaning.TotalFrameLength => declaredLength - Configuration.GetFixedFieldsLength(),
            LengthFieldMeaning.DataLength => declaredLength,
            LengthFieldMeaning.RemainingBytes => declaredLength,
            _ => throw new NotSupportedException($"不支持的长度字段含义: {meaning}")
        };
    }

    /// <summary>
    /// 验证消息完整性
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    protected override bool ValidateInternal()
    {
        try
        {
            // 验证所有必需字段是否存在
            foreach (var field in Configuration.Fields.Where(f => f.IsRequired))
            {
                if (!_fieldValues.ContainsKey(field.Name))
                {
                    // 对于校验字段，如果没有设置值，可以跳过验证（将在序列化时计算）
                    if (field.Type == ProtocolFieldType.Checksum)
                    {
                        continue;
                    }

                    System.Diagnostics.Debug.WriteLine($"验证失败：缺少必需字段 '{field.Name}'");
                    return false;
                }
            }

            // 验证固定值字段
            foreach (var field in Configuration.Fields.Where(f => f.Type == ProtocolFieldType.FixedValue))
            {
                var value = GetFieldValue<byte[]>(field.Name);
                if (value == null || field.FixedValue == null || !value.SequenceEqual(field.FixedValue))
                {
                    System.Diagnostics.Debug.WriteLine($"验证失败：固定值字段 '{field.Name}' 值不匹配");
                    return false;
                }
            }

            // 执行自定义验证
            foreach (var field in Configuration.Fields.Where(f => f.CustomValidator != null))
            {
                var value = GetFieldValue<byte[]>(field.Name);
                if (value != null && !field.CustomValidator!(value))
                {
                    System.Diagnostics.Debug.WriteLine($"验证失败：自定义验证字段 '{field.Name}' 验证失败");
                    return false;
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"验证异常：{ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 将消息序列化为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public override byte[] ToBytes()
    {
        var result = new List<byte>();

        foreach (var field in Configuration.Fields)
        {
            var fieldBytes = SerializeField(field);
            result.AddRange(fieldBytes);
        }

        return result.ToArray();
    }

    /// <summary>
    /// 序列化单个字段
    /// </summary>
    /// <param name="field">字段描述符</param>
    /// <returns>序列化后的字节数组</returns>
    private byte[] SerializeField(ProtocolFieldDescriptor field)
    {
        var value = _fieldValues.GetValueOrDefault(field.Name);

        return field.Type switch
        {
            ProtocolFieldType.FixedValue => SerializeFixedValue(field),
            ProtocolFieldType.Byte => SerializeByte(field, value),
            ProtocolFieldType.UInt16 => SerializeUInt16(field, value),
            ProtocolFieldType.UInt32 => SerializeUInt32(field, value),
            ProtocolFieldType.Length => SerializeLength(field, value),
            ProtocolFieldType.VariableData => SerializeVariableData(field, value),
            ProtocolFieldType.Checksum => SerializeChecksum(field, value),
            ProtocolFieldType.Custom => SerializeCustomField(field, value),
            _ => throw new NotSupportedException($"不支持的字段类型: {field.Type}")
        };
    }

    /// <summary>
    /// 序列化固定值字段
    /// </summary>
    private byte[] SerializeFixedValue(ProtocolFieldDescriptor field)
    {
        if (field.FixedValue == null)
            throw new InvalidOperationException($"固定值字段 '{field.Name}' 的固定值未设置");

        return field.FixedValue;
    }

    /// <summary>
    /// 序列化单字节字段
    /// </summary>
    private byte[] SerializeByte(ProtocolFieldDescriptor field, object? value)
    {
        if (value == null)
            return new byte[1];

        var byteValue = Convert.ToByte(value);
        return new[] { byteValue };
    }

    /// <summary>
    /// 序列化双字节字段
    /// </summary>
    private byte[] SerializeUInt16(ProtocolFieldDescriptor field, object? value)
    {
        if (value == null)
            return new byte[2];

        var uint16Value = Convert.ToUInt16(value);
        var bytes = new byte[2];

        if (field.ByteOrder == ByteOrder.LittleEndian)
        {
            bytes[0] = (byte)(uint16Value & 0xFF);
            bytes[1] = (byte)((uint16Value >> 8) & 0xFF);
        }
        else
        {
            bytes[0] = (byte)((uint16Value >> 8) & 0xFF);
            bytes[1] = (byte)(uint16Value & 0xFF);
        }

        return bytes;
    }

    /// <summary>
    /// 序列化四字节字段
    /// </summary>
    private byte[] SerializeUInt32(ProtocolFieldDescriptor field, object? value)
    {
        if (value == null)
            return new byte[4];

        var uint32Value = Convert.ToUInt32(value);
        var bytes = new byte[4];

        if (field.ByteOrder == ByteOrder.LittleEndian)
        {
            bytes[0] = (byte)(uint32Value & 0xFF);
            bytes[1] = (byte)((uint32Value >> 8) & 0xFF);
            bytes[2] = (byte)((uint32Value >> 16) & 0xFF);
            bytes[3] = (byte)((uint32Value >> 24) & 0xFF);
        }
        else
        {
            bytes[0] = (byte)((uint32Value >> 24) & 0xFF);
            bytes[1] = (byte)((uint32Value >> 16) & 0xFF);
            bytes[2] = (byte)((uint32Value >> 8) & 0xFF);
            bytes[3] = (byte)(uint32Value & 0xFF);
        }

        return bytes;
    }

    /// <summary>
    /// 序列化长度字段
    /// </summary>
    private byte[] SerializeLength(ProtocolFieldDescriptor field, object? value)
    {
        // 长度字段的序列化逻辑与对应的数值类型相同
        return field.Length switch
        {
            1 => SerializeByte(field, value),
            2 => SerializeUInt16(field, value),
            4 => SerializeUInt32(field, value),
            _ => throw new NotSupportedException($"不支持的长度字段大小: {field.Length}")
        };
    }

    /// <summary>
    /// 序列化变长数据字段
    /// </summary>
    private byte[] SerializeVariableData(ProtocolFieldDescriptor field, object? value)
    {
        if (value is byte[] byteArray)
            return byteArray;

        if (value == null)
            return Array.Empty<byte>();

        throw new InvalidOperationException($"变长数据字段 '{field.Name}' 的值必须是 byte[] 类型");
    }

    /// <summary>
    /// 序列化校验字段
    /// </summary>
    private byte[] SerializeChecksum(ProtocolFieldDescriptor field, object? value)
    {
        // 校验字段的序列化逻辑与对应的数值类型相同
        return field.Length switch
        {
            1 => SerializeByte(field, value),
            2 => SerializeUInt16(field, value),
            4 => SerializeUInt32(field, value),
            _ => throw new NotSupportedException($"不支持的校验字段大小: {field.Length}")
        };
    }

    /// <summary>
    /// 序列化自定义字段
    /// </summary>
    private byte[] SerializeCustomField(ProtocolFieldDescriptor field, object? value)
    {
        if (field.CustomSerializer == null)
            throw new InvalidOperationException($"自定义字段 '{field.Name}' 的序列化函数未设置");

        if (value == null)
            return new byte[field.Length];

        return field.CustomSerializer(value);
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    public override string ToString()
    {
        var fields = _fieldValues.Select(kvp => $"{kvp.Key}={FormatFieldValue(kvp.Value)}");
        return $"[{Timestamp:HH:mm:ss.fff}] {ProtocolName}.{MessageType} ({string.Join(", ", fields)})";
    }

    /// <summary>
    /// 格式化字段值用于显示
    /// </summary>
    private string FormatFieldValue(object? value)
    {
        return value switch
        {
            null => "null",
            byte b => $"0x{b:X2}",
            ushort us => $"0x{us:X4}",
            uint ui => $"0x{ui:X8}",
            byte[] ba => BitConverter.ToString(ba).Replace("-", " "),
            _ => value.ToString() ?? "null"
        };
    }
}
