namespace Alicres.Protocol.Interfaces;

/// <summary>
/// 协议消息接口，定义所有协议消息的基本契约
/// </summary>
public interface IProtocolMessage
{
    /// <summary>
    /// 消息唯一标识符
    /// </summary>
    string MessageId { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    string ProtocolName { get; }

    /// <summary>
    /// 消息类型
    /// </summary>
    string MessageType { get; }

    /// <summary>
    /// 消息时间戳
    /// </summary>
    DateTime Timestamp { get; }

    /// <summary>
    /// 原始数据
    /// </summary>
    byte[] RawData { get; }

    /// <summary>
    /// 消息是否有效
    /// </summary>
    bool IsValid { get; }

    /// <summary>
    /// 验证消息完整性
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    bool Validate();

    /// <summary>
    /// 将消息序列化为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    byte[] ToBytes();

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    string ToString();
}
