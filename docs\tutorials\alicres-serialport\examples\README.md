# Alicres.SerialPort 示例代码集合

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.SerialPort.svg)](https://www.nuget.org/packages/Alicres.SerialPort/)

本目录包含了 Alicres.SerialPort 串口通讯库的各种实际应用示例，帮助您快速理解和使用库的各项功能。

## 📋 示例目录

### 🚀 基础示例
- [基本通讯示例](basic-communication.md) - 最简单的串口收发数据示例

### 🔧 高级示例
- [高级缓冲管理示例](advanced-buffering.md) - 缓冲区管理和数据队列使用
- [流控制示例](flow-control.md) - XON/XOFF 和 RTS/CTS 流控制
- [性能监控示例](performance-monitoring.md) - 状态监控和性能统计
- [错误处理示例](error-handling.md) - 完整的错误处理和恢复机制

### 🌐 集成示例
- [多串口管理示例](multi-port-management.md) - 同时管理多个串口连接
- [协议解析集成示例](protocol-integration.md) - 与 Alicres.Protocol 协同使用
- [WPF 应用示例](wpf-application.md) - 在 WPF 应用中使用串口通讯
- [控制台工具示例](console-tool.md) - 完整的串口调试工具

### 🏭 实际应用示例
- [传感器数据采集](sensor-data-collection.md) - 从传感器采集数据
- [设备控制示例](device-control.md) - 控制外部设备
- [数据记录器示例](data-logger.md) - 串口数据记录和分析
- [AT 指令通讯示例](at-command.md) - 与支持 AT 指令的设备通讯

## 🎯 快速开始

如果您是第一次使用 Alicres.SerialPort，建议按以下顺序学习：

1. **[基本通讯示例](basic-communication.md)** - 了解基础用法
2. **[错误处理示例](error-handling.md)** - 学习正确的错误处理
3. **[高级缓冲管理示例](advanced-buffering.md)** - 掌握高级功能
4. **[多串口管理示例](multi-port-management.md)** - 处理复杂场景

## 📚 相关文档

- [快速入门指南](../getting-started.md) - 基础概念和安装
- [高级功能详解](../advanced-features.md) - 深入了解高级功能
- [故障排除指南](../troubleshooting.md) - 解决常见问题
- [API 参考文档](../../../api/) - 完整的 API 文档

## 🔧 运行示例

### 环境要求
- .NET 8.0 或更高版本
- 可用的串口设备（物理或虚拟）
- Alicres.SerialPort NuGet 包

### 安装依赖
```bash
dotnet add package Alicres.SerialPort --version 1.1.0
```

### 运行步骤
1. 复制示例代码到您的项目
2. 根据实际情况修改串口名称（如 COM1、/dev/ttyUSB0 等）
3. 编译并运行程序
4. 观察输出结果

## 💡 使用提示

### 串口名称
- **Windows**: COM1, COM2, COM3 等
- **Linux**: /dev/ttyUSB0, /dev/ttyACM0 等
- **macOS**: /dev/cu.usbserial-xxx 等

### 常用波特率
- 9600 - 最常用的低速率
- 115200 - 常用的高速率
- 38400, 57600 - 中等速率

### 调试技巧
- 使用虚拟串口工具进行测试
- 启用日志记录查看详细信息
- 使用串口监控工具验证数据传输

## 🤝 贡献示例

如果您有好的示例代码想要分享，欢迎：

1. Fork 项目仓库
2. 创建新的示例文件
3. 添加详细的说明和注释
4. 提交 Pull Request

### 示例代码规范
- 代码简洁易懂，包含详细注释
- 提供完整的使用说明
- 包含错误处理和资源释放
- 遵循 Alicres C# 开发规范

## 📞 获取帮助

如果在使用示例过程中遇到问题：

1. 查看 [故障排除指南](../troubleshooting.md)
2. 检查 [API 文档](../../../api/)
3. 在 [GitHub Issues](https://gitee.com/liam-gitee/alicres/issues) 提问
4. 参考 [最佳实践](../advanced-features.md#最佳实践)

---

**上一篇**: ← [高级功能详解](../advanced-features.md)  
**下一篇**: [基本通讯示例](basic-communication.md) →
