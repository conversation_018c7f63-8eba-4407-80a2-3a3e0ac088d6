using Alicres.Protocol.Framing;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.Protocol.Tests.Framing;

/// <summary>
/// 固定长度帧处理器测试
/// </summary>
public class FixedLengthFramingTests : IDisposable
{
    private readonly Mock<ILogger> _mockLogger;
    private readonly FixedLengthFraming _framing;
    private const int TestFrameLength = 8;

    public FixedLengthFramingTests()
    {
        _mockLogger = new Mock<ILogger>();
        _framing = new FixedLengthFraming(TestFrameLength, _mockLogger.Object);
    }

    [Fact]
    public void Constructor_WithValidFrameLength_ShouldInitializeCorrectly()
    {
        // Assert
        _framing.FramingName.Should().Be("FixedLengthFraming");
        _framing.Mode.Should().Be(Alicres.Protocol.Interfaces.FramingMode.FixedLength);
        _framing.FrameLength.Should().Be(TestFrameLength);
        _framing.AutoPadding.Should().BeTrue();
        _framing.AutoTruncate.Should().BeTrue();
        _framing.AutoTrimPadding.Should().BeTrue();
        _framing.PaddingByte.Should().Be(0x00);
    }

    [Fact]
    public void Constructor_WithInvalidFrameLength_ShouldThrowException()
    {
        // Act & Assert
        var action = () => new FixedLengthFraming(0, _mockLogger.Object);
        action.Should().Throw<ArgumentException>();

        var action2 = () => new FixedLengthFraming(-1, _mockLogger.Object);
        action2.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void HasCompleteFrame_WithSufficientData_ShouldReturnTrue()
    {
        // Arrange
        var data = new byte[TestFrameLength];

        // Act
        var result = _framing.HasCompleteFrame(data);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void HasCompleteFrame_WithInsufficientData_ShouldReturnFalse()
    {
        // Arrange
        var data = new byte[TestFrameLength - 1];

        // Act
        var result = _framing.HasCompleteFrame(data);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void GetExpectedFrameLength_WithSufficientData_ShouldReturnFrameLength()
    {
        // Arrange
        var data = new byte[TestFrameLength];

        // Act
        var result = _framing.GetExpectedFrameLength(data);

        // Assert
        result.Should().Be(TestFrameLength);
    }

    [Fact]
    public void GetExpectedFrameLength_WithInsufficientData_ShouldReturnMinusOne()
    {
        // Arrange
        var data = new byte[TestFrameLength - 1];

        // Act
        var result = _framing.GetExpectedFrameLength(data);

        // Assert
        result.Should().Be(-1);
    }

    [Fact]
    public void FrameMessage_WithExactLength_ShouldReturnOriginal()
    {
        // Arrange
        var message = new byte[TestFrameLength];
        for (int i = 0; i < message.Length; i++)
        {
            message[i] = (byte)(i + 1);
        }

        // Act
        var result = _framing.FrameMessage(message);

        // Assert
        result.Should().BeEquivalentTo(message);
    }

    [Fact]
    public void FrameMessage_WithShorterLength_ShouldPadMessage()
    {
        // Arrange
        var message = new byte[] { 0x01, 0x02, 0x03 };

        // Act
        var result = _framing.FrameMessage(message);

        // Assert
        result.Should().HaveCount(TestFrameLength);
        result.Take(3).Should().BeEquivalentTo(message);
        result.Skip(3).Should().AllBeEquivalentTo((byte)0x00); // 填充字节
    }

    [Fact]
    public void FrameMessage_WithLongerLength_ShouldTruncateMessage()
    {
        // Arrange
        var message = new byte[TestFrameLength + 5];
        for (int i = 0; i < message.Length; i++)
        {
            message[i] = (byte)(i + 1);
        }

        // Act
        var result = _framing.FrameMessage(message);

        // Assert
        result.Should().HaveCount(TestFrameLength);
        result.Should().BeEquivalentTo(message.Take(TestFrameLength));
    }

    [Fact]
    public void FrameMessage_WithLongerLengthAndNoTruncate_ShouldThrowException()
    {
        // Arrange
        _framing.AutoTruncate = false;
        var message = new byte[TestFrameLength + 1];

        // Act & Assert
        var action = () => _framing.FrameMessage(message);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void FrameMessage_WithShorterLengthAndNoPadding_ShouldThrowException()
    {
        // Arrange
        _framing.AutoPadding = false;
        var message = new byte[TestFrameLength - 1];

        // Act & Assert
        var action = () => _framing.FrameMessage(message);
        action.Should().Throw<ArgumentException>();
    }

    [Fact]
    public void UnframeMessage_WithPaddedData_ShouldRemovePadding()
    {
        // Arrange
        var framedData = new byte[TestFrameLength];
        framedData[0] = 0x01;
        framedData[1] = 0x02;
        framedData[2] = 0x03;
        // 其余为默认的0x00填充

        // Act
        var result = _framing.UnframeMessage(framedData);

        // Assert
        result.Should().HaveCount(3);
        result.Should().BeEquivalentTo(new byte[] { 0x01, 0x02, 0x03 });
    }

    [Fact]
    public void UnframeMessage_WithoutAutoTrim_ShouldKeepPadding()
    {
        // Arrange
        _framing.AutoTrimPadding = false;
        var framedData = new byte[TestFrameLength];
        framedData[0] = 0x01;
        framedData[1] = 0x02;
        framedData[2] = 0x03;

        // Act
        var result = _framing.UnframeMessage(framedData);

        // Assert
        result.Should().HaveCount(TestFrameLength);
        result.Should().BeEquivalentTo(framedData);
    }

    [Fact]
    public void ProcessIncomingData_WithSingleFrame_ShouldExtractFrame()
    {
        // Arrange
        var data = new byte[TestFrameLength];
        for (int i = 0; i < data.Length; i++)
        {
            data[i] = (byte)(i + 1);
        }

        // Act
        var result = _framing.ProcessIncomingData(data);

        // Assert
        result.Should().HaveCount(1);
        result[0].Should().BeEquivalentTo(data);
    }

    [Fact]
    public void ProcessIncomingData_WithMultipleFrames_ShouldExtractAllFrames()
    {
        // Arrange
        var data = new byte[TestFrameLength * 2];
        for (int i = 0; i < data.Length; i++)
        {
            data[i] = (byte)(i + 1);
        }

        // Act
        var result = _framing.ProcessIncomingData(data);

        // Assert
        result.Should().HaveCount(2);
        result[0].Should().BeEquivalentTo(data.Take(TestFrameLength));
        result[1].Should().BeEquivalentTo(data.Skip(TestFrameLength));
    }

    [Fact]
    public void ProcessIncomingData_WithIncompleteFrame_ShouldReturnEmpty()
    {
        // Arrange
        var data = new byte[TestFrameLength - 1];

        // Act
        var result = _framing.ProcessIncomingData(data);

        // Assert
        result.Should().BeEmpty();
    }

    [Fact]
    public void ProcessIncomingData_WithPartialThenCompleteFrame_ShouldExtractCompleteFrame()
    {
        // Arrange
        var partialData = new byte[TestFrameLength - 2];
        for (int i = 0; i < partialData.Length; i++)
        {
            partialData[i] = (byte)(i + 1);
        }

        var remainingData = new byte[] { (byte)(TestFrameLength - 1), (byte)TestFrameLength };

        // Act
        var result1 = _framing.ProcessIncomingData(partialData);
        var result2 = _framing.ProcessIncomingData(remainingData);

        // Assert
        result1.Should().BeEmpty();
        result2.Should().HaveCount(1);
        
        var expectedFrame = new byte[TestFrameLength];
        for (int i = 0; i < expectedFrame.Length; i++)
        {
            expectedFrame[i] = (byte)(i + 1);
        }
        result2[0].Should().BeEquivalentTo(expectedFrame);
    }

    [Fact]
    public void SetPaddingConfiguration_ShouldUpdateSettings()
    {
        // Arrange
        const byte newPaddingByte = 0xFF;
        const bool newAutoPadding = false;
        const bool newAutoTruncate = false;
        const bool newAutoTrimPadding = false;

        // Act
        _framing.SetPaddingConfiguration(newPaddingByte, newAutoPadding, newAutoTruncate, newAutoTrimPadding);

        // Assert
        _framing.PaddingByte.Should().Be(newPaddingByte);
        _framing.AutoPadding.Should().Be(newAutoPadding);
        _framing.AutoTruncate.Should().Be(newAutoTruncate);
        _framing.AutoTrimPadding.Should().Be(newAutoTrimPadding);
    }

    [Fact]
    public void ToString_ShouldReturnConfigurationInfo()
    {
        // Act
        var result = _framing.ToString();

        // Assert
        result.Should().Contain("FixedLengthFraming");
        result.Should().Contain(TestFrameLength.ToString());
        result.Should().Contain("0x00"); // 默认填充字节
    }

    public void Dispose()
    {
        // 清理资源
    }
}
