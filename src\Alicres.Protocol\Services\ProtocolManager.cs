using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models.EventArgs;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace Alicres.Protocol.Services;

/// <summary>
/// 协议管理器实现，统一管理协议解析和传输
/// </summary>
public class ProtocolManager : IProtocolManager
{
    private readonly ILogger? _logger;
    private readonly ConcurrentQueue<byte> _receiveBuffer;
    private readonly object _lockObject = new();
    private bool _disposed;
    private bool _isRunning;

    /// <summary>
    /// 协议解析器
    /// </summary>
    public IProtocolParser ProtocolParser { get; }

    /// <summary>
    /// 传输适配器
    /// </summary>
    public ITransportAdapter TransportAdapter { get; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => TransportAdapter.IsConnected;

    /// <summary>
    /// 是否正在运行
    /// </summary>
    public bool IsRunning => _isRunning;

    /// <summary>
    /// 协议消息接收事件
    /// </summary>
    public event EventHandler<ProtocolMessageEventArgs>? MessageReceived;

    /// <summary>
    /// 原始数据接收事件
    /// </summary>
    public event EventHandler<TransportDataEventArgs>? RawDataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<TransportStatusEventArgs>? ConnectionStatusChanged;

    /// <summary>
    /// 错误发生事件
    /// </summary>
    public event EventHandler<ProtocolErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="protocolParser">协议解析器</param>
    /// <param name="transportAdapter">传输适配器</param>
    /// <param name="logger">日志记录器</param>
    public ProtocolManager(
        IProtocolParser protocolParser, 
        ITransportAdapter transportAdapter, 
        ILogger<ProtocolManager>? logger = null)
    {
        ProtocolParser = protocolParser ?? throw new ArgumentNullException(nameof(protocolParser));
        TransportAdapter = transportAdapter ?? throw new ArgumentNullException(nameof(transportAdapter));
        _logger = logger;
        _receiveBuffer = new ConcurrentQueue<byte>();

        // 订阅事件
        TransportAdapter.DataReceived += OnTransportDataReceived;
        TransportAdapter.StatusChanged += OnTransportStatusChanged;
        TransportAdapter.ErrorOccurred += OnTransportErrorOccurred;

        ProtocolParser.MessageParsed += OnProtocolMessageParsed;
        ProtocolParser.ParseError += OnProtocolParseError;

        _logger?.LogDebug("协议管理器已创建: {ProtocolName} + {TransportType}", 
            ProtocolParser.ProtocolName, TransportAdapter.TransportType);
    }

    /// <summary>
    /// 打开连接并开始协议处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功连接返回 true，否则返回 false</returns>
    public async Task<bool> ConnectAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            if (_isRunning)
            {
                _logger?.LogWarning("协议管理器已在运行中");
                return IsConnected;
            }
        }

        try
        {
            _logger?.LogDebug("正在连接协议管理器...");

            var result = await TransportAdapter.OpenAsync(cancellationToken);
            
            if (result)
            {
                lock (_lockObject)
                {
                    _isRunning = true;
                }
                
                _logger?.LogInformation("协议管理器连接成功: {ProtocolName} + {TransportType}", 
                    ProtocolParser.ProtocolName, TransportAdapter.TransportType);
            }
            else
            {
                _logger?.LogWarning("协议管理器连接失败");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "连接协议管理器时发生错误");
            OnErrorOccurred(new ProtocolErrorEventArgs(ex, ProtocolParser.ProtocolName));
            return false;
        }
    }

    /// <summary>
    /// 关闭连接并停止协议处理
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功断开返回 true，否则返回 false</returns>
    public async Task<bool> DisconnectAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            if (!_isRunning)
            {
                _logger?.LogWarning("协议管理器未在运行中");
                return true;
            }
        }

        try
        {
            _logger?.LogDebug("正在断开协议管理器连接...");

            var result = await TransportAdapter.CloseAsync(cancellationToken);
            
            lock (_lockObject)
            {
                _isRunning = false;
                // 清空接收缓冲区
                while (_receiveBuffer.TryDequeue(out _)) { }
            }

            if (result)
            {
                _logger?.LogInformation("协议管理器连接已断开");
            }
            else
            {
                _logger?.LogWarning("协议管理器断开连接失败");
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "断开协议管理器连接时发生错误");
            OnErrorOccurred(new ProtocolErrorEventArgs(ex, ProtocolParser.ProtocolName));
            return false;
        }
    }

    /// <summary>
    /// 发送协议消息
    /// </summary>
    /// <param name="message">要发送的协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功发送返回 true，否则返回 false</returns>
    public async Task<bool> SendMessageAsync(IProtocolMessage message, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(message);

        if (!IsConnected)
        {
            _logger?.LogWarning("尝试在未连接状态下发送消息");
            return false;
        }

        try
        {
            _logger?.LogDebug("发送协议消息: {MessageType}", message.MessageType);

            var data = await ProtocolParser.SerializeAsync(message, cancellationToken);
            var result = await TransportAdapter.SendAsync(data, cancellationToken);

            if (result)
            {
                _logger?.LogDebug("协议消息发送成功: {MessageType}, 长度: {Length} 字节", 
                    message.MessageType, data.Length);
            }
            else
            {
                _logger?.LogWarning("协议消息发送失败: {MessageType}", message.MessageType);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "发送协议消息时发生错误: {MessageType}", message.MessageType);
            OnErrorOccurred(new ProtocolErrorEventArgs(ex, ProtocolParser.ProtocolName));
            return false;
        }
    }

    /// <summary>
    /// 发送原始数据
    /// </summary>
    /// <param name="data">要发送的原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功发送返回 true，否则返回 false</returns>
    public async Task<bool> SendRawDataAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(data);

        if (!IsConnected)
        {
            _logger?.LogWarning("尝试在未连接状态下发送原始数据");
            return false;
        }

        try
        {
            _logger?.LogDebug("发送原始数据，长度: {Length} 字节", data.Length);

            var result = await TransportAdapter.SendAsync(data, cancellationToken);

            if (result)
            {
                _logger?.LogDebug("原始数据发送成功，长度: {Length} 字节", data.Length);
            }
            else
            {
                _logger?.LogWarning("原始数据发送失败，长度: {Length} 字节", data.Length);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "发送原始数据时发生错误");
            OnErrorOccurred(new ProtocolErrorEventArgs(ex, ProtocolParser.ProtocolName));
            return false;
        }
    }

    /// <summary>
    /// 发送消息并等待响应
    /// </summary>
    /// <param name="request">请求消息</param>
    /// <param name="timeout">超时时间</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>响应消息，如果超时或失败返回 null</returns>
    public async Task<IProtocolMessage?> SendAndWaitAsync(IProtocolMessage request, TimeSpan timeout, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(request);

        // 这是一个简化的实现，实际应用中可能需要更复杂的请求-响应匹配逻辑
        var tcs = new TaskCompletionSource<IProtocolMessage?>();
        
        EventHandler<ProtocolMessageEventArgs>? handler = null;
        handler = (sender, e) =>
        {
            // 简单的匹配逻辑：假设第一个收到的消息就是响应
            // 实际应用中需要根据协议特性进行更精确的匹配
            MessageReceived -= handler;
            tcs.TrySetResult(e.Message);
        };

        MessageReceived += handler;

        try
        {
            var sendResult = await SendMessageAsync(request, cancellationToken);
            if (!sendResult)
            {
                MessageReceived -= handler;
                return null;
            }

            using var timeoutCts = new CancellationTokenSource(timeout);
            using var combinedCts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken, timeoutCts.Token);

            combinedCts.Token.Register(() => 
            {
                MessageReceived -= handler;
                tcs.TrySetCanceled();
            });

            return await tcs.Task;
        }
        catch (OperationCanceledException)
        {
            MessageReceived -= handler;
            _logger?.LogWarning("等待响应超时: {MessageType}", request.MessageType);
            return null;
        }
        catch (Exception ex)
        {
            MessageReceived -= handler;
            _logger?.LogError(ex, "发送消息并等待响应时发生错误: {MessageType}", request.MessageType);
            return null;
        }
    }

    /// <summary>
    /// 处理传输数据接收事件
    /// </summary>
    private async void OnTransportDataReceived(object? sender, TransportDataEventArgs e)
    {
        try
        {
            _logger?.LogDebug("接收到传输数据，长度: {Length} 字节", e.Data.Length);

            // 将数据添加到缓冲区
            foreach (var b in e.Data)
            {
                _receiveBuffer.Enqueue(b);
            }

            // 触发原始数据接收事件
            OnRawDataReceived(e);

            // 尝试解析协议消息
            await TryParseMessages();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理传输数据接收事件时发生错误");
            OnErrorOccurred(new ProtocolErrorEventArgs(ex, ProtocolParser.ProtocolName));
        }
    }

    /// <summary>
    /// 尝试从缓冲区解析协议消息
    /// </summary>
    private async Task TryParseMessages()
    {
        var bufferData = _receiveBuffer.ToArray();
        if (bufferData.Length == 0)
            return;

        try
        {
            // 检查是否有完整的消息
            if (ProtocolParser.IsCompleteMessage(bufferData))
            {
                var expectedLength = ProtocolParser.GetExpectedMessageLength(bufferData);
                if (expectedLength > 0 && bufferData.Length >= expectedLength)
                {
                    // 提取完整消息
                    var messageData = new byte[expectedLength];
                    Array.Copy(bufferData, messageData, expectedLength);

                    // 从缓冲区移除已处理的数据
                    var newBuffer = new ConcurrentQueue<byte>();
                    for (int i = expectedLength; i < bufferData.Length; i++)
                    {
                        newBuffer.Enqueue(bufferData[i]);
                    }
                    
                    // 替换缓冲区
                    while (_receiveBuffer.TryDequeue(out _)) { }
                    while (newBuffer.TryDequeue(out var b))
                    {
                        _receiveBuffer.Enqueue(b);
                    }

                    // 解析消息
                    var message = await ProtocolParser.ParseAsync(messageData);
                    if (message != null)
                    {
                        _logger?.LogDebug("成功解析协议消息: {MessageType}", message.MessageType);
                        OnMessageReceived(new ProtocolMessageEventArgs(message, ProtocolParser.ProtocolName));
                    }

                    // 递归处理剩余数据
                    await TryParseMessages();
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "解析协议消息时发生错误");
            OnErrorOccurred(new ProtocolErrorEventArgs(ex, ProtocolParser.ProtocolName, bufferData));
        }
    }

    /// <summary>
    /// 处理传输状态变化事件
    /// </summary>
    private void OnTransportStatusChanged(object? sender, TransportStatusEventArgs e)
    {
        _logger?.LogDebug("传输状态变化: {Previous} -> {Current}", e.PreviousStatus, e.CurrentStatus);
        OnConnectionStatusChanged(e);
    }

    /// <summary>
    /// 处理传输错误事件
    /// </summary>
    private void OnTransportErrorOccurred(object? sender, TransportErrorEventArgs e)
    {
        _logger?.LogError(e.Exception, "传输层发生错误: {Message}", e.ErrorMessage);
        OnErrorOccurred(new ProtocolErrorEventArgs(e.Exception, ProtocolParser.ProtocolName));
    }

    /// <summary>
    /// 处理协议消息解析事件
    /// </summary>
    private void OnProtocolMessageParsed(object? sender, ProtocolMessageEventArgs e)
    {
        _logger?.LogDebug("协议消息解析完成: {MessageType}", e.Message.MessageType);
        OnMessageReceived(e);
    }

    /// <summary>
    /// 处理协议解析错误事件
    /// </summary>
    private void OnProtocolParseError(object? sender, ProtocolErrorEventArgs e)
    {
        _logger?.LogError(e.Exception, "协议解析发生错误: {Message}", e.ErrorMessage);
        OnErrorOccurred(e);
    }

    /// <summary>
    /// 触发消息接收事件
    /// </summary>
    protected virtual void OnMessageReceived(ProtocolMessageEventArgs e)
    {
        MessageReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 触发原始数据接收事件
    /// </summary>
    protected virtual void OnRawDataReceived(TransportDataEventArgs e)
    {
        RawDataReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 触发连接状态变化事件
    /// </summary>
    protected virtual void OnConnectionStatusChanged(TransportStatusEventArgs e)
    {
        ConnectionStatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    protected virtual void OnErrorOccurred(ProtocolErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    /// <summary>
    /// 检查是否已释放
    /// </summary>
    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(ProtocolManager));
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 断开连接
            _ = DisconnectAsync().ConfigureAwait(false);

            // 取消订阅事件
            TransportAdapter.DataReceived -= OnTransportDataReceived;
            TransportAdapter.StatusChanged -= OnTransportStatusChanged;
            TransportAdapter.ErrorOccurred -= OnTransportErrorOccurred;

            ProtocolParser.MessageParsed -= OnProtocolMessageParsed;
            ProtocolParser.ParseError -= OnProtocolParseError;

            // 释放传输适配器
            TransportAdapter.Dispose();

            _logger?.LogDebug("协议管理器已释放");
            _disposed = true;
        }
    }
}
