namespace Alicres.SerialPort.Models;

/// <summary>
/// 缓冲区溢出处理策略
/// </summary>
public enum BufferOverflowStrategy
{
    /// <summary>
    /// 丢弃最旧的数据
    /// </summary>
    DropOldest,

    /// <summary>
    /// 丢弃最新的数据
    /// </summary>
    DropNewest,

    /// <summary>
    /// 阻塞等待缓冲区有空间
    /// </summary>
    Block,

    /// <summary>
    /// 抛出异常
    /// </summary>
    ThrowException,

    /// <summary>
    /// 扩展缓冲区（如果内存允许）
    /// </summary>
    Expand
}
