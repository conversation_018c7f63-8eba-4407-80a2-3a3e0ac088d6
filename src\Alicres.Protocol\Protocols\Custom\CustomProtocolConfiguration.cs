using Alicres.Protocol.Configuration;
using Alicres.Protocol.Validators;

namespace Alicres.Protocol.Protocols.Custom;

/// <summary>
/// 自定义协议配置工厂
/// 用于创建支持原始协议格式的配置：7E F1 05 55 0A 00 00 10 F5 82
/// </summary>
public static class CustomProtocolConfiguration
{
    /// <summary>
    /// 创建标准自定义协议配置
    /// 协议格式：帧头(1字节) + 源地址(1字节) + 目标地址(1字节) + 命令码(1字节) + 数据长度(1字节) + 数据字段(N字节) + CRC16(2字节)
    /// </summary>
    /// <returns>协议配置</returns>
    public static ProtocolConfiguration CreateStandardConfiguration()
    {
        var config = new ProtocolConfiguration
        {
            Name = "CustomProtocol",
            Version = "1.0.0",
            Description = "自定义协议，支持帧头、地址、命令码、数据长度、数据字段和CRC16校验",
            MinFrameLength = 7, // 最小帧：帧头(1) + 源地址(1) + 目标地址(1) + 命令码(1) + 长度(1) + CRC16(2)
            MaxFrameLength = 256
        };

        // 添加字段定义
        config.AddField(ProtocolFieldDescriptor.CreateFixedValue(
            "FrameHeader", 
            new byte[] { 0x7E }, 
            "帧头标识"))
            
        .AddField(ProtocolFieldDescriptor.CreateByte(
            "SourceAddress", 
            "源地址"))
            
        .AddField(ProtocolFieldDescriptor.CreateByte(
            "TargetAddress", 
            "目标地址"))
            
        .AddField(ProtocolFieldDescriptor.CreateByte(
            "CommandCode", 
            "命令码"))
            
        .AddField(ProtocolFieldDescriptor.CreateLength(
            "DataLength", 
            1, 
            LengthFieldMeaning.TotalFrameLength, 
            ByteOrder.LittleEndian, 
            "数据长度字段"))
            
        .AddField(ProtocolFieldDescriptor.CreateVariableData(
            "DataField", 
            "数据字段"))
            
        .AddField(ProtocolFieldDescriptor.CreateChecksum(
            "CRC16", 
            2, 
            ByteOrder.LittleEndian, 
            "CRC16校验码"));

        // 设置特殊字段
        config.SetFrameHeader("FrameHeader")
              .SetLengthField("DataLength")
              .SetDataField("DataField")
              .SetChecksumField("CRC16");

        // 设置校验器和校验范围
        config.SetValidator<CustomCrc16Validator>(
            ChecksumRangeConfiguration.ExcludeFrameHeaderAndChecksum());

        return config;
    }

    /// <summary>
    /// 创建可配置的自定义协议配置
    /// </summary>
    /// <param name="frameHeader">帧头字节数组</param>
    /// <param name="lengthFieldSize">长度字段大小（1、2或4字节）</param>
    /// <param name="lengthMeaning">长度字段含义</param>
    /// <param name="checksumSize">校验字段大小（1、2或4字节）</param>
    /// <param name="byteOrder">字节序</param>
    /// <returns>协议配置</returns>
    public static ProtocolConfiguration CreateConfigurableConfiguration(
        byte[] frameHeader,
        int lengthFieldSize = 1,
        LengthFieldMeaning lengthMeaning = LengthFieldMeaning.TotalFrameLength,
        int checksumSize = 2,
        ByteOrder byteOrder = ByteOrder.LittleEndian)
    {
        var config = new ProtocolConfiguration
        {
            Name = "ConfigurableCustomProtocol",
            Version = "1.0.0",
            Description = "可配置的自定义协议",
            MinFrameLength = frameHeader.Length + 3 + lengthFieldSize + checksumSize, // 帧头 + 源地址 + 目标地址 + 命令码 + 长度 + 校验
            MaxFrameLength = 1024
        };

        // 添加字段定义
        config.AddField(ProtocolFieldDescriptor.CreateFixedValue(
            "FrameHeader", 
            frameHeader, 
            "帧头标识"))
            
        .AddField(ProtocolFieldDescriptor.CreateByte(
            "SourceAddress", 
            "源地址"))
            
        .AddField(ProtocolFieldDescriptor.CreateByte(
            "TargetAddress", 
            "目标地址"))
            
        .AddField(ProtocolFieldDescriptor.CreateByte(
            "CommandCode", 
            "命令码"));

        // 添加长度字段
        if (lengthFieldSize == 1)
        {
            config.AddField(ProtocolFieldDescriptor.CreateLength(
                "DataLength", 
                1, 
                lengthMeaning, 
                byteOrder, 
                "数据长度字段"));
        }
        else if (lengthFieldSize == 2)
        {
            config.AddField(ProtocolFieldDescriptor.CreateLength(
                "DataLength", 
                2, 
                lengthMeaning, 
                byteOrder, 
                "数据长度字段"));
        }
        else if (lengthFieldSize == 4)
        {
            config.AddField(ProtocolFieldDescriptor.CreateLength(
                "DataLength", 
                4, 
                lengthMeaning, 
                byteOrder, 
                "数据长度字段"));
        }
        else
        {
            throw new ArgumentException($"不支持的长度字段大小: {lengthFieldSize}");
        }

        // 添加数据字段和校验字段
        config.AddField(ProtocolFieldDescriptor.CreateVariableData(
            "DataField", 
            "数据字段"));

        if (checksumSize == 1)
        {
            config.AddField(ProtocolFieldDescriptor.CreateChecksum(
                "Checksum", 
                1, 
                byteOrder, 
                "校验码"));
        }
        else if (checksumSize == 2)
        {
            config.AddField(ProtocolFieldDescriptor.CreateChecksum(
                "CRC16", 
                2, 
                byteOrder, 
                "CRC16校验码"));
        }
        else if (checksumSize == 4)
        {
            config.AddField(ProtocolFieldDescriptor.CreateChecksum(
                "CRC32", 
                4, 
                byteOrder, 
                "CRC32校验码"));
        }
        else
        {
            throw new ArgumentException($"不支持的校验字段大小: {checksumSize}");
        }

        // 设置特殊字段
        config.SetFrameHeader("FrameHeader")
              .SetLengthField("DataLength")
              .SetDataField("DataField")
              .SetChecksumField(checksumSize == 1 ? "Checksum" : checksumSize == 2 ? "CRC16" : "CRC32");

        // 设置校验器
        if (checksumSize == 2)
        {
            config.SetValidator<CustomCrc16Validator>(
                ChecksumRangeConfiguration.ExcludeFrameHeaderAndChecksum());
        }

        return config;
    }

    /// <summary>
    /// 创建多字节帧头的协议配置
    /// </summary>
    /// <param name="frameHeaderHex">帧头的十六进制字符串，如 "7E7E" 或 "AA55BB"</param>
    /// <returns>协议配置</returns>
    public static ProtocolConfiguration CreateMultiByteHeaderConfiguration(string frameHeaderHex)
    {
        // 解析十六进制字符串为字节数组
        var cleanHex = frameHeaderHex.Replace(" ", "").Replace("-", "");
        if (cleanHex.Length % 2 != 0)
        {
            throw new ArgumentException("帧头十六进制字符串长度必须是偶数");
        }

        var frameHeader = new byte[cleanHex.Length / 2];
        for (int i = 0; i < frameHeader.Length; i++)
        {
            frameHeader[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
        }

        return CreateConfigurableConfiguration(frameHeader);
    }

    /// <summary>
    /// 创建简化的协议配置（仅包含必要字段）
    /// </summary>
    /// <param name="frameHeader">帧头</param>
    /// <param name="hasLengthField">是否包含长度字段</param>
    /// <param name="hasChecksum">是否包含校验字段</param>
    /// <returns>协议配置</returns>
    public static ProtocolConfiguration CreateSimplifiedConfiguration(
        byte[] frameHeader,
        bool hasLengthField = true,
        bool hasChecksum = true)
    {
        var config = new ProtocolConfiguration
        {
            Name = "SimplifiedCustomProtocol",
            Version = "1.0.0",
            Description = "简化的自定义协议",
            MinFrameLength = frameHeader.Length + 1, // 至少包含帧头和一个数据字节
            MaxFrameLength = 256
        };

        // 添加帧头
        config.AddField(ProtocolFieldDescriptor.CreateFixedValue(
            "FrameHeader", 
            frameHeader, 
            "帧头标识"));

        // 可选的长度字段
        if (hasLengthField)
        {
            config.AddField(ProtocolFieldDescriptor.CreateLength(
                "DataLength", 
                1, 
                LengthFieldMeaning.TotalFrameLength, 
                ByteOrder.LittleEndian, 
                "数据长度字段"));
            config.SetLengthField("DataLength");
        }

        // 数据字段
        config.AddField(ProtocolFieldDescriptor.CreateVariableData(
            "DataField", 
            "数据字段"));

        // 可选的校验字段
        if (hasChecksum)
        {
            config.AddField(ProtocolFieldDescriptor.CreateChecksum(
                "CRC16", 
                2, 
                ByteOrder.LittleEndian, 
                "CRC16校验码"));
            config.SetChecksumField("CRC16");
            config.SetValidator<CustomCrc16Validator>(
                ChecksumRangeConfiguration.ExcludeFrameHeaderAndChecksum());
        }

        // 设置特殊字段
        config.SetFrameHeader("FrameHeader")
              .SetDataField("DataField");

        return config;
    }
}
