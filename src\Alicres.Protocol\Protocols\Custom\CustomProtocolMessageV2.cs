using Alicres.Protocol.Configuration;
using Alicres.Protocol.Models;
using Alicres.Protocol.Parsers;
using Alicres.Protocol.Framing;

namespace Alicres.Protocol.Protocols.Custom;

/// <summary>
/// 自定义协议消息 V2 版本，基于可配置协议消息实现
/// 提供与原始 CustomProtocolMessage 兼容的接口
/// </summary>
public class CustomProtocolMessageV2 : ConfigurableProtocolMessage
{
    /// <summary>
    /// 消息类型
    /// </summary>
    public override string MessageType => "CustomMessage";

    /// <summary>
    /// 帧头字节（固定为 0x7E）
    /// </summary>
    public byte FrameHeader
    {
        get => GetFieldValue<byte[]>("FrameHeader")?[0] ?? 0x7E;
        set => SetFieldValue("FrameHeader", new byte[] { value });
    }

    /// <summary>
    /// 源地址
    /// </summary>
    public byte SourceAddress
    {
        get => GetFieldValue<byte>("SourceAddress");
        set => SetFieldValue("SourceAddress", value);
    }

    /// <summary>
    /// 目标地址
    /// </summary>
    public byte TargetAddress
    {
        get => GetFieldValue<byte>("TargetAddress");
        set => SetFieldValue("TargetAddress", value);
    }

    /// <summary>
    /// 命令码
    /// </summary>
    public byte CommandCode
    {
        get => GetFieldValue<byte>("CommandCode");
        set => SetFieldValue("CommandCode", value);
    }

    /// <summary>
    /// 数据长度（整个帧的长度）
    /// </summary>
    public byte DataLength
    {
        get => GetFieldValue<byte>("DataLength");
        set => SetFieldValue("DataLength", value);
    }

    /// <summary>
    /// 数据字段
    /// </summary>
    public byte[] DataField
    {
        get => GetFieldValue<byte[]>("DataField") ?? Array.Empty<byte>();
        set => SetFieldValue("DataField", value ?? Array.Empty<byte>());
    }

    /// <summary>
    /// CRC16 校验码
    /// </summary>
    public ushort Crc16
    {
        get => GetFieldValue<ushort>("CRC16");
        set => SetFieldValue("CRC16", value);
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    public CustomProtocolMessageV2() : base(CustomProtocolConfiguration.CreateStandardConfiguration())
    {
        // 设置默认值
        FrameHeader = 0x7E;
    }

    /// <summary>
    /// 从原始数据构造消息
    /// </summary>
    /// <param name="rawData">原始数据</param>
    public CustomProtocolMessageV2(byte[] rawData) : base(CustomProtocolConfiguration.CreateStandardConfiguration(), rawData)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sourceAddress">源地址</param>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="dataField">数据字段</param>
    public CustomProtocolMessageV2(byte sourceAddress, byte targetAddress, byte commandCode, byte[] dataField) : this()
    {
        SourceAddress = sourceAddress;
        TargetAddress = targetAddress;
        CommandCode = commandCode;
        DataField = dataField ?? Array.Empty<byte>();
        DataLength = (byte)(5 + DataField.Length + 2); // 帧头+源地址+目标地址+命令码+长度字段+数据+CRC16
        
        // 生成原始数据
        RawData = ToBytes();
    }

    /// <summary>
    /// 获取用于CRC16校验的数据（排除帧头和CRC16字段）
    /// </summary>
    /// <returns>用于校验的数据</returns>
    public byte[] GetDataForCrcCalculation()
    {
        var rawData = ToBytes();
        var dataForCrc = new byte[rawData.Length - 3]; // 排除帧头(1字节)和CRC16(2字节)
        Array.Copy(rawData, 1, dataForCrc, 0, dataForCrc.Length);
        return dataForCrc;
    }

    /// <summary>
    /// 设置CRC16校验码
    /// </summary>
    /// <param name="crc16">CRC16校验码</param>
    public void SetCrc16(ushort crc16)
    {
        Crc16 = crc16;
        
        // 更新原始数据
        var rawData = ToBytes();
        RawData = rawData;
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] {ProtocolName}.{MessageType} " +
               $"(源地址: 0x{SourceAddress:X2}, 目标地址: 0x{TargetAddress:X2}, " +
               $"命令码: 0x{CommandCode:X2}, 数据长度: {DataLength}, " +
               $"数据: {(DataField.Length > 0 ? BitConverter.ToString(DataField) : "无")}, " +
               $"CRC16: 0x{Crc16:X4})";
    }
}

/// <summary>
/// 自定义协议解析器 V2 版本，基于可配置协议解析器实现
/// 提供与原始 CustomProtocolParser 兼容的接口
/// </summary>
public class CustomProtocolParserV2 : ConfigurableProtocolParser
{
    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public CustomProtocolParserV2(Microsoft.Extensions.Logging.ILogger<CustomProtocolParserV2>? logger = null) 
        : base(CustomProtocolConfiguration.CreateStandardConfiguration(), logger)
    {
    }

    /// <summary>
    /// 创建请求消息
    /// </summary>
    /// <param name="sourceAddress">源地址</param>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="dataField">数据字段</param>
    /// <returns>自定义协议消息</returns>
    public CustomProtocolMessageV2 CreateMessage(byte sourceAddress, byte targetAddress, byte commandCode, byte[]? dataField = null)
    {
        return new CustomProtocolMessageV2(sourceAddress, targetAddress, commandCode, dataField ?? Array.Empty<byte>());
    }

    /// <summary>
    /// 解析协议示例数据
    /// </summary>
    /// <param name="hexString">十六进制字符串，如 "7E F1 05 55 0A 00 00 10 F5 82"</param>
    /// <returns>解析后的协议消息</returns>
    public async Task<CustomProtocolMessageV2?> ParseFromHexStringAsyncV2(string hexString)
    {
        var baseMessage = await base.ParseFromHexStringAsync(hexString);
        if (baseMessage == null)
            return null;

        // 转换为 CustomProtocolMessageV2
        var customMessage = new CustomProtocolMessageV2();
        foreach (var field in baseMessage.GetAllFieldValues())
        {
            customMessage.SetFieldValue(field.Key, field.Value);
        }

        return customMessage;
    }

    /// <summary>
    /// 解析消息并返回强类型结果
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>解析后的自定义协议消息</returns>
    public async Task<CustomProtocolMessageV2?> ParseAsyncV2(byte[] data)
    {
        var baseMessage = await base.ParseAsync(data);
        if (baseMessage is not ConfigurableProtocolMessage configurableMessage)
            return null;

        // 转换为 CustomProtocolMessageV2
        var customMessage = new CustomProtocolMessageV2();
        foreach (var field in configurableMessage.GetAllFieldValues())
        {
            customMessage.SetFieldValue(field.Key, field.Value);
        }

        return customMessage;
    }
}

/// <summary>
/// 自定义帧处理器 V2 版本，基于可配置帧处理器实现
/// 提供与原始 CustomHeaderFraming 兼容的接口
/// </summary>
public class CustomHeaderFramingV2 : ConfigurableFraming
{
    /// <summary>
    /// 帧头字节
    /// </summary>
    public byte FrameHeader { get; set; } = 0x7E;

    /// <summary>
    /// 长度字段在帧中的位置（从0开始）
    /// </summary>
    public int LengthFieldOffset { get; set; } = 4;

    /// <summary>
    /// 长度字段表示的是整个帧的长度还是数据部分的长度
    /// </summary>
    public bool LengthIncludesHeader { get; set; } = true;

    /// <summary>
    /// 最小帧长度（防止无效数据）
    /// </summary>
    public int MinFrameLength { get; set; } = 8;

    /// <summary>
    /// 最大帧长度（防止恶意数据）
    /// </summary>
    public int MaxFrameLength { get; set; } = 256;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="frameHeader">帧头字节，默认为 0x7E</param>
    /// <param name="logger">日志记录器</param>
    public CustomHeaderFramingV2(byte frameHeader = 0x7E, Microsoft.Extensions.Logging.ILogger<CustomHeaderFramingV2>? logger = null) 
        : base(CreateConfigurationForFrameHeader(frameHeader), logger)
    {
        FrameHeader = frameHeader;
    }

    /// <summary>
    /// 为指定帧头创建配置
    /// </summary>
    /// <param name="frameHeader">帧头字节</param>
    /// <returns>协议配置</returns>
    private static ProtocolConfiguration CreateConfigurationForFrameHeader(byte frameHeader)
    {
        return CustomProtocolConfiguration.CreateConfigurableConfiguration(new byte[] { frameHeader });
    }
}
