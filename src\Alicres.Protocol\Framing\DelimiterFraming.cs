using System.Collections.Concurrent;
using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models.EventArgs;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Framing;

/// <summary>
/// 分隔符帧处理器，使用指定的分隔符来分割消息帧
/// </summary>
public class DelimiterFraming : AbstractMessageFraming
{
    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public override string FramingName => "DelimiterFraming";

    /// <summary>
    /// 帧模式
    /// </summary>
    public override FramingMode Mode => FramingMode.Delimiter;

    /// <summary>
    /// 分隔符
    /// </summary>
    public byte Delimiter { get; set; }

    /// <summary>
    /// 是否包含分隔符在帧数据中
    /// </summary>
    public bool IncludeDelimiter { get; set; }

    /// <summary>
    /// 最大帧长度（防止无限增长）
    /// </summary>
    public int MaxFrameLength { get; set; } = 4096;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="delimiter">分隔符</param>
    /// <param name="includeDelimiter">是否在帧数据中包含分隔符</param>
    /// <param name="logger">日志记录器</param>
    public DelimiterFraming(byte delimiter, bool includeDelimiter = false, ILogger? logger = null) 
        : base(logger)
    {
        Delimiter = delimiter;
        IncludeDelimiter = includeDelimiter;
    }

    /// <summary>
    /// 检查数据是否包含完整的帧
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果包含完整帧返回 true，否则返回 false</returns>
    public override bool HasCompleteFrame(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length == 0)
            return false;

        // 查找分隔符
        for (int i = 0; i < data.Length; i++)
        {
            if (data[i] == Delimiter)
                return true;
        }

        // 检查是否超过最大帧长度
        if (data.Length >= MaxFrameLength)
        {
            Logger?.LogWarning("数据长度 {Length} 超过最大帧长度 {MaxLength}，可能缺少分隔符", 
                data.Length, MaxFrameLength);
            return true; // 强制处理以避免无限增长
        }

        return false;
    }

    /// <summary>
    /// 获取帧的预期长度
    /// </summary>
    /// <param name="data">帧数据的开始部分</param>
    /// <returns>帧的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedFrameLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length == 0)
            return -1;

        // 查找第一个分隔符的位置
        for (int i = 0; i < data.Length; i++)
        {
            if (data[i] == Delimiter)
            {
                // 如果包含分隔符，长度为分隔符位置+1，否则为分隔符位置
                return IncludeDelimiter ? i + 1 : i;
            }
        }

        // 如果超过最大长度，返回最大长度
        if (data.Length >= MaxFrameLength)
        {
            return MaxFrameLength;
        }

        return -1; // 没有找到完整帧
    }

    /// <summary>
    /// 为消息添加帧信息的具体实现
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <returns>添加帧信息后的数据</returns>
    protected override byte[] AddFrameToMessage(byte[] message)
    {
        ArgumentNullException.ThrowIfNull(message);

        // 创建包含分隔符的新数组
        var framedMessage = new byte[message.Length + 1];
        Array.Copy(message, framedMessage, message.Length);
        framedMessage[message.Length] = Delimiter;

        Logger?.LogTrace("为消息添加分隔符 0x{Delimiter:X2}，帧长度: {Length}", 
            Delimiter, framedMessage.Length);

        return framedMessage;
    }

    /// <summary>
    /// 从帧数据中移除帧信息的具体实现
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>移除帧信息后的原始消息</returns>
    protected override byte[] RemoveFrameFromMessage(byte[] framedData)
    {
        ArgumentNullException.ThrowIfNull(framedData);

        if (framedData.Length == 0)
            return framedData;

        // 如果不包含分隔符在结果中，需要移除最后的分隔符
        if (!IncludeDelimiter && framedData.Length > 0 && framedData[framedData.Length - 1] == Delimiter)
        {
            var message = new byte[framedData.Length - 1];
            Array.Copy(framedData, message, message.Length);

            Logger?.LogTrace("从帧数据中移除分隔符，消息长度: {Length}", message.Length);

            return message;
        }

        return framedData;
    }

    /// <summary>
    /// 重写帧提取逻辑以正确处理分隔符
    /// </summary>
    /// <returns>提取出的完整帧列表</returns>
    protected override List<byte[]> ExtractFramesFromBuffer()
    {
        var frames = new List<byte[]>();

        lock (LockObject)
        {
            var bufferData = DataBuffer.ToArray();
            if (bufferData.Length == 0)
                return frames;

            var processedBytes = 0;

            while (processedBytes < bufferData.Length)
            {
                var remainingData = new byte[bufferData.Length - processedBytes];
                Array.Copy(bufferData, processedBytes, remainingData, 0, remainingData.Length);

                // 查找分隔符
                var delimiterIndex = -1;
                for (int i = 0; i < remainingData.Length; i++)
                {
                    if (remainingData[i] == Delimiter)
                    {
                        delimiterIndex = i;
                        break;
                    }
                }

                if (delimiterIndex == -1)
                {
                    // 没有找到分隔符，检查是否超过最大长度
                    if (remainingData.Length >= MaxFrameLength)
                    {
                        // 强制处理以避免无限增长
                        var frameData = new byte[MaxFrameLength];
                        Array.Copy(remainingData, frameData, MaxFrameLength);
                        frames.Add(frameData);
                        processedBytes += MaxFrameLength;
                        OnFrameExtracted(new FrameExtractedEventArgs(frameData, ++FrameCounter, FramingName));
                    }
                    else
                    {
                        break; // 等待更多数据
                    }
                }
                else
                {
                    // 找到分隔符，提取帧
                    var frameLength = IncludeDelimiter ? delimiterIndex + 1 : delimiterIndex;
                    var frameData = new byte[frameLength];
                    Array.Copy(remainingData, frameData, frameLength);

                    frames.Add(frameData);
                    processedBytes += delimiterIndex + 1; // 总是跳过分隔符
                    OnFrameExtracted(new FrameExtractedEventArgs(frameData, ++FrameCounter, FramingName));
                }
            }

            // 从缓冲区中移除已处理的数据
            if (processedBytes > 0)
            {
                var newBuffer = new ConcurrentQueue<byte>();
                for (int i = processedBytes; i < bufferData.Length; i++)
                {
                    newBuffer.Enqueue(bufferData[i]);
                }

                // 替换缓冲区
                while (DataBuffer.TryDequeue(out _)) { }
                while (newBuffer.TryDequeue(out var b))
                {
                    DataBuffer.Enqueue(b);
                }
            }
        }

        return frames;
    }

    /// <summary>
    /// 获取配置信息的字符串表示
    /// </summary>
    /// <returns>配置信息</returns>
    public override string ToString()
    {
        return $"{FramingName} (分隔符: 0x{Delimiter:X2}, 包含分隔符: {IncludeDelimiter}, 最大长度: {MaxFrameLength})";
    }
}
