namespace Alicres.Protocol.Models.Performance;

/// <summary>
/// 性能指标数据
/// </summary>
public class PerformanceMetrics
{
    /// <summary>
    /// 数据吞吐量（字节/秒）
    /// </summary>
    public double ThroughputBytesPerSecond { get; set; }

    /// <summary>
    /// 发送速率（字节/秒）
    /// </summary>
    public double SendRateBytesPerSecond { get; set; }

    /// <summary>
    /// 接收速率（字节/秒）
    /// </summary>
    public double ReceiveRateBytesPerSecond { get; set; }

    /// <summary>
    /// 消息处理速率（消息/秒）
    /// </summary>
    public double MessageProcessingRate { get; set; }

    /// <summary>
    /// 平均延迟（毫秒）
    /// </summary>
    public double AverageLatencyMs { get; set; }

    /// <summary>
    /// 最大延迟（毫秒）
    /// </summary>
    public double MaxLatencyMs { get; set; }

    /// <summary>
    /// 最小延迟（毫秒）
    /// </summary>
    public double MinLatencyMs { get; set; }

    /// <summary>
    /// 错误率（百分比）
    /// </summary>
    public double ErrorRatePercentage { get; set; }

    /// <summary>
    /// 总发送字节数
    /// </summary>
    public long TotalBytesSent { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 总消息数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; set; }

    /// <summary>
    /// CPU 使用率（百分比）
    /// </summary>
    public double CpuUsagePercentage { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 队列长度
    /// </summary>
    public int QueueLength { get; set; }

    /// <summary>
    /// 监控开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 最后更新时间
    /// </summary>
    public DateTime LastUpdateTime { get; set; }

    /// <summary>
    /// 运行时长
    /// </summary>
    public TimeSpan Uptime => LastUpdateTime - StartTime;

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>性能指标的字符串描述</returns>
    public override string ToString()
    {
        return $"性能指标 [{LastUpdateTime:HH:mm:ss.fff}]: " +
               $"吞吐量 {ThroughputBytesPerSecond:F2} B/s, " +
               $"延迟 {AverageLatencyMs:F2}ms, " +
               $"错误率 {ErrorRatePercentage:F2}%, " +
               $"消息 {TotalMessages}, " +
               $"运行时长 {Uptime:hh\\:mm\\:ss}";
    }

    /// <summary>
    /// 获取详细报告
    /// </summary>
    /// <returns>详细的性能报告</returns>
    public string GetDetailedReport()
    {
        var report = new System.Text.StringBuilder();
        report.AppendLine("=== 性能指标详细报告 ===");
        report.AppendLine($"报告时间: {LastUpdateTime:yyyy-MM-dd HH:mm:ss.fff}");
        report.AppendLine($"监控开始: {StartTime:yyyy-MM-dd HH:mm:ss.fff}");
        report.AppendLine($"运行时长: {Uptime:dd\\.hh\\:mm\\:ss}");
        report.AppendLine();
        
        report.AppendLine("数据传输统计:");
        report.AppendLine($"  - 总吞吐量: {ThroughputBytesPerSecond:F2} 字节/秒");
        report.AppendLine($"  - 发送速率: {SendRateBytesPerSecond:F2} 字节/秒");
        report.AppendLine($"  - 接收速率: {ReceiveRateBytesPerSecond:F2} 字节/秒");
        report.AppendLine($"  - 总发送: {TotalBytesSent:N0} 字节");
        report.AppendLine($"  - 总接收: {TotalBytesReceived:N0} 字节");
        report.AppendLine();
        
        report.AppendLine("消息处理统计:");
        report.AppendLine($"  - 处理速率: {MessageProcessingRate:F2} 消息/秒");
        report.AppendLine($"  - 总消息数: {TotalMessages:N0}");
        report.AppendLine($"  - 总错误数: {TotalErrors:N0}");
        report.AppendLine($"  - 错误率: {ErrorRatePercentage:F2}%");
        report.AppendLine();
        
        report.AppendLine("延迟统计:");
        report.AppendLine($"  - 平均延迟: {AverageLatencyMs:F2} 毫秒");
        report.AppendLine($"  - 最大延迟: {MaxLatencyMs:F2} 毫秒");
        report.AppendLine($"  - 最小延迟: {MinLatencyMs:F2} 毫秒");
        report.AppendLine();
        
        report.AppendLine("系统资源:");
        report.AppendLine($"  - CPU 使用率: {CpuUsagePercentage:F2}%");
        report.AppendLine($"  - 内存使用: {MemoryUsageBytes / 1024.0 / 1024.0:F2} MB");
        report.AppendLine($"  - 活跃连接: {ActiveConnections}");
        report.AppendLine($"  - 队列长度: {QueueLength}");
        
        return report.ToString();
    }

    /// <summary>
    /// 获取性能等级
    /// </summary>
    /// <returns>性能等级描述</returns>
    public string GetPerformanceGrade()
    {
        var score = 100.0;
        
        // 根据错误率扣分
        score -= ErrorRatePercentage * 10;
        
        // 根据延迟扣分
        if (AverageLatencyMs > 100) score -= 10;
        else if (AverageLatencyMs > 50) score -= 5;
        
        // 根据CPU使用率扣分
        if (CpuUsagePercentage > 80) score -= 15;
        else if (CpuUsagePercentage > 60) score -= 10;
        else if (CpuUsagePercentage > 40) score -= 5;
        
        // 根据队列长度扣分
        if (QueueLength > 1000) score -= 10;
        else if (QueueLength > 500) score -= 5;
        
        score = Math.Max(0, score);
        
        return score switch
        {
            >= 90 => "优秀",
            >= 80 => "良好",
            >= 70 => "一般",
            >= 60 => "较差",
            _ => "很差"
        };
    }

    /// <summary>
    /// 获取性能建议
    /// </summary>
    /// <returns>性能优化建议列表</returns>
    public List<string> GetPerformanceSuggestions()
    {
        var suggestions = new List<string>();

        if (ErrorRatePercentage > 5)
        {
            suggestions.Add($"错误率过高 ({ErrorRatePercentage:F2}%)，建议检查协议解析逻辑和数据质量");
        }

        if (AverageLatencyMs > 100)
        {
            suggestions.Add($"平均延迟过高 ({AverageLatencyMs:F2}ms)，建议优化处理逻辑或增加处理能力");
        }

        if (CpuUsagePercentage > 80)
        {
            suggestions.Add($"CPU 使用率过高 ({CpuUsagePercentage:F2}%)，建议优化算法或增加处理资源");
        }

        if (QueueLength > 1000)
        {
            suggestions.Add($"队列长度过长 ({QueueLength})，建议增加处理速度或调整队列大小");
        }

        if (ThroughputBytesPerSecond < 1000 && TotalMessages > 100)
        {
            suggestions.Add("数据吞吐量较低，建议检查网络连接或优化数据传输");
        }

        if (suggestions.Count == 0)
        {
            suggestions.Add("系统性能表现良好，继续保持");
        }

        return suggestions;
    }
}
