# 高级缓冲管理示例

本示例详细演示 Alicres.SerialPort 1.1.0 版本的高级缓冲管理功能，包括智能缓冲策略、流控制和性能监控等特性。

## 📋 示例列表

- [智能缓冲区配置](#智能缓冲区配置)
- [缓冲区溢出处理](#缓冲区溢出处理)
- [实时缓冲区监控](#实时缓冲区监控)
- [流控制管理](#流控制管理)
- [性能优化与诊断](#性能优化与诊断)
- [批量数据处理优化](#批量数据处理优化)

---

## 🗄️ 智能缓冲区配置

### 基础缓冲区配置

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Microsoft.Extensions.Logging;

public class SmartBufferingExample
{
    public static async Task RunBasicBufferingExample()
    {
        // 创建日志记录器
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        var logger = loggerFactory.CreateLogger<SerialPortService>();

        // 配置高级缓冲管理
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 115200,
            
            // 基础缓冲区设置
            ReceiveBufferSize = 8192,        // 接收缓冲区 8KB
            SendBufferSize = 4096,           // 发送缓冲区 4KB
            
            // 高级缓冲管理
            EnableAdvancedBuffering = true,
            DataQueueMaxLength = 1000,       // 数据队列最大长度
            BufferOverflowStrategy = BufferOverflowStrategy.DropOldest,
            BufferCleanupInterval = 5000,    // 5秒清理间隔
            BufferWarningThreshold = 80      // 80% 使用率警告
        };

        using var serialPort = new SerialPortService(config, logger);

        try
        {
            Console.WriteLine("=== 智能缓冲区配置示例 ===");
            await serialPort.OpenAsync();

            // 获取初始缓冲区统计
            var initialStats = serialPort.GetBufferStatistics();
            if (initialStats != null)
            {
                Console.WriteLine("初始缓冲区状态:");
                Console.WriteLine(initialStats.ToString());
                Console.WriteLine();
            }

            // 发送测试数据
            for (int i = 1; i <= 50; i++)
            {
                var testData = $"Test message {i:D3} - {DateTime.Now:HH:mm:ss.fff}";
                await serialPort.SendTextAsync(testData + "\r\n");
                
                if (i % 10 == 0)
                {
                    var stats = serialPort.GetBufferStatistics();
                    if (stats != null)
                    {
                        Console.WriteLine($"发送 {i} 条消息后的缓冲区状态:");
                        Console.WriteLine($"  队列长度: {stats.QueueLength}/{stats.MaxQueueLength}");
                        Console.WriteLine($"  使用率: {stats.QueueUsagePercentage}%");
                        Console.WriteLine($"  总接收: {stats.TotalBytesReceived} 字节");
                        Console.WriteLine();
                    }
                }

                await Task.Delay(100);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"错误: {ex.Message}");
        }
        finally
        {
            await serialPort.CloseAsync();
        }
    }
}
```

### 动态缓冲区调整

```csharp
public class DynamicBufferingExample
{
    private readonly SerialPortService _serialPort;
    private readonly Timer _monitorTimer;
    private int _currentDataRate = 0;

    public DynamicBufferingExample(SerialPortConfiguration config)
    {
        _serialPort = new SerialPortService(config);
        
        // 每秒监控一次缓冲区状态
        _monitorTimer = new Timer(MonitorAndAdjustBuffer, null, 
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    private void MonitorAndAdjustBuffer(object state)
    {
        var stats = _serialPort.GetBufferStatistics();
        if (stats == null) return;

        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 缓冲区监控:");
        Console.WriteLine($"  使用率: {stats.QueueUsagePercentage}%");
        Console.WriteLine($"  数据丢失率: {stats.DataLossRate:F2}%");

        // 根据使用率动态调整策略
        if (stats.QueueUsagePercentage > 90)
        {
            Console.WriteLine("  ⚠️ 缓冲区使用率过高，建议优化处理速度");
            
            // 批量处理数据以减少队列压力
            var queueLength = _serialPort.GetQueueLength();
            if (queueLength > 100)
            {
                var batchData = _serialPort.DequeueDataBatch(50);
                Console.WriteLine($"  📦 批量处理了 {batchData?.Count ?? 0} 条数据");
            }
        }
        else if (stats.QueueUsagePercentage < 20)
        {
            Console.WriteLine("  ✅ 缓冲区使用率正常");
        }

        // 检查数据丢失情况
        if (stats.DataLossRate > 1.0)
        {
            Console.WriteLine($"  ❌ 数据丢失率过高: {stats.DataLossRate:F2}%");
            Console.WriteLine("  建议检查数据处理逻辑或增加缓冲区大小");
        }
    }

    public async Task RunExample()
    {
        await _serialPort.OpenAsync();
        
        Console.WriteLine("动态缓冲区调整示例运行中...");
        Console.WriteLine("将运行 30 秒，观察缓冲区动态调整过程");
        
        // 模拟不同强度的数据流
        _ = Task.Run(async () =>
        {
            for (int phase = 1; phase <= 3; phase++)
            {
                Console.WriteLine($"\n=== 阶段 {phase}: 数据强度 {phase * 10} msg/s ===");
                
                for (int i = 0; i < 100; i++)
                {
                    await _serialPort.SendTextAsync($"Phase{phase}-Data{i:D3}\r\n");
                    await Task.Delay(1000 / (phase * 10)); // 调整发送频率
                }
                
                await Task.Delay(2000); // 阶段间隔
            }
        });

        await Task.Delay(30000); // 运行30秒
        
        _monitorTimer?.Dispose();
        await _serialPort.CloseAsync();
    }
}
```

---

## ⚠️ 缓冲区溢出处理

### 多种溢出策略示例

```csharp
public class BufferOverflowExample
{
    public static async Task RunOverflowStrategiesExample()
    {
        var strategies = new[]
        {
            BufferOverflowStrategy.DropOldest,
            BufferOverflowStrategy.DropNewest,
            BufferOverflowStrategy.Block,
            BufferOverflowStrategy.Expand
        };

        foreach (var strategy in strategies)
        {
            Console.WriteLine($"\n=== 测试溢出策略: {strategy} ===");
            await TestOverflowStrategy(strategy);
            await Task.Delay(2000);
        }
    }

    private static async Task TestOverflowStrategy(BufferOverflowStrategy strategy)
    {
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600,
            EnableAdvancedBuffering = true,
            DataQueueMaxLength = 10,         // 故意设置小队列用于测试
            BufferOverflowStrategy = strategy,
            BufferWarningThreshold = 70
        };

        using var serialPort = new SerialPortService(config);

        // 订阅溢出事件
        serialPort.BufferOverflow += (sender, e) =>
        {
            Console.WriteLine($"🔥 缓冲区溢出事件:");
            Console.WriteLine($"   当前队列: {e.CurrentLength}/{e.MaxLength}");
            Console.WriteLine($"   溢出数据: {e.OverflowData.Length} 字节");
            Console.WriteLine($"   溢出内容: {e.OverflowData.ToText().Trim()}");
            Console.WriteLine($"   处理策略: {strategy}");
            Console.WriteLine($"   时间戳: {e.Timestamp:HH:mm:ss.fff}");
        };

        // 订阅警告事件
        serialPort.BufferWarning += (sender, e) =>
        {
            Console.WriteLine($"⚠️ 缓冲区警告: 使用率 {e.UsagePercentage}%");
        };

        try
        {
            await serialPort.OpenAsync();

            // 快速发送数据以触发溢出
            for (int i = 1; i <= 20; i++)
            {
                var data = $"Overflow test data {i:D2}";
                await serialPort.SendTextAsync(data + "\r\n");
                
                var stats = serialPort.GetBufferStatistics();
                if (stats != null)
                {
                    Console.WriteLine($"发送 {i:D2}: 队列 {stats.QueueLength}/{stats.MaxQueueLength} " +
                                    $"({stats.QueueUsagePercentage}%)");
                }

                await Task.Delay(50); // 快速发送
            }

            // 显示最终统计
            var finalStats = serialPort.GetBufferStatistics();
            if (finalStats != null)
            {
                Console.WriteLine("\n最终统计:");
                Console.WriteLine(finalStats.GetDetailedReport());
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"策略 {strategy} 测试异常: {ex.Message}");
        }
        finally
        {
            await serialPort.CloseAsync();
        }
    }
}
```

---

## 📊 实时缓冲区监控

### 综合监控仪表板

```csharp
public class BufferMonitoringDashboard
{
    private readonly SerialPortService _serialPort;
    private readonly Timer _dashboardTimer;
    private readonly List<BufferStatistics> _statisticsHistory = new();
    private readonly object _lockObject = new();

    public BufferMonitoringDashboard(SerialPortConfiguration config)
    {
        _serialPort = new SerialPortService(config);

        // 每500ms更新一次仪表板
        _dashboardTimer = new Timer(UpdateDashboard, null,
            TimeSpan.FromMilliseconds(500), TimeSpan.FromMilliseconds(500));
    }

    private void UpdateDashboard(object state)
    {
        var stats = _serialPort.GetBufferStatistics();
        if (stats == null) return;

        lock (_lockObject)
        {
            _statisticsHistory.Add(stats);

            // 保持最近100个统计记录
            if (_statisticsHistory.Count > 100)
            {
                _statisticsHistory.RemoveAt(0);
            }
        }

        // 清屏并显示仪表板
        Console.Clear();
        DisplayDashboard(stats);
    }

    private void DisplayDashboard(BufferStatistics stats)
    {
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    缓冲区监控仪表板                          ║");
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        Console.WriteLine($"║ 时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}                    ║");
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");

        // 队列状态
        var queueBar = CreateProgressBar(stats.QueueUsagePercentage, 40);
        Console.WriteLine($"║ 队列使用率: {stats.QueueUsagePercentage,3}% [{queueBar}] ║");
        Console.WriteLine($"║ 队列长度: {stats.QueueLength,4}/{stats.MaxQueueLength,-4}                              ║");

        // 数据统计
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        Console.WriteLine($"║ 总接收: {stats.TotalBytesReceived,10:N0} 字节                        ║");
        Console.WriteLine($"║ 总丢弃: {stats.TotalBytesDropped,10:N0} 字节                        ║");
        Console.WriteLine($"║ 丢失率: {stats.DataLossRate,6:F2}%                                  ║");

        // 性能指标
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        Console.WriteLine($"║ 溢出策略: {stats.OverflowStrategy,-15}                      ║");
        Console.WriteLine($"║ 最后清理: {stats.LastCleanupTime:HH:mm:ss}                           ║");

        // 趋势分析
        DisplayTrendAnalysis();

        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        Console.WriteLine("║ 性能建议:                                                    ║");

        var suggestions = stats.GetPerformanceSuggestions();
        foreach (var suggestion in suggestions.Take(3))
        {
            var truncated = suggestion.Length > 50 ? suggestion.Substring(0, 47) + "..." : suggestion;
            Console.WriteLine($"║ • {truncated,-55} ║");
        }

        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
        Console.WriteLine("按 Ctrl+C 退出监控");
    }

    private string CreateProgressBar(int percentage, int width)
    {
        var filled = (int)(percentage / 100.0 * width);
        var bar = new string('█', filled) + new string('░', width - filled);
        return bar;
    }

    private void DisplayTrendAnalysis()
    {
        lock (_lockObject)
        {
            if (_statisticsHistory.Count < 2) return;

            var recent = _statisticsHistory.TakeLast(10).ToList();
            var avgUsage = recent.Average(s => s.QueueUsagePercentage);
            var avgLossRate = recent.Average(s => s.DataLossRate);

            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
            Console.WriteLine($"║ 近期趋势 (最近10次):                                         ║");
            Console.WriteLine($"║ 平均使用率: {avgUsage,5:F1}%                                    ║");
            Console.WriteLine($"║ 平均丢失率: {avgLossRate,5:F2}%                                    ║");

            // 简单的趋势指示
            if (recent.Count >= 5)
            {
                var firstHalf = recent.Take(5).Average(s => s.QueueUsagePercentage);
                var secondHalf = recent.Skip(5).Average(s => s.QueueUsagePercentage);
                var trend = secondHalf > firstHalf ? "↗️ 上升" : secondHalf < firstHalf ? "↘️ 下降" : "→ 稳定";
                Console.WriteLine($"║ 使用率趋势: {trend,-10}                                      ║");
            }
        }
    }

    public async Task StartMonitoring()
    {
        Console.WriteLine("启动缓冲区监控仪表板...");

        await _serialPort.OpenAsync();

        // 模拟数据流
        _ = Task.Run(async () =>
        {
            var random = new Random();
            while (_serialPort.IsConnected)
            {
                var dataSize = random.Next(10, 100);
                var data = new string('X', dataSize);
                await _serialPort.SendTextAsync(data + "\r\n");

                // 随机间隔，模拟真实数据流
                await Task.Delay(random.Next(50, 500));
            }
        });

        // 等待用户中断
        Console.CancelKeyPress += async (sender, e) =>
        {
            e.Cancel = true;
            _dashboardTimer?.Dispose();
            await _serialPort.CloseAsync();
            Console.WriteLine("\n监控已停止");
            Environment.Exit(0);
        };

        // 保持运行
        await Task.Delay(Timeout.Infinite);
    }
}
```

### 缓冲区健康检查

```csharp
public class BufferHealthChecker
{
    private readonly SerialPortService _serialPort;

    public BufferHealthChecker(SerialPortService serialPort)
    {
        _serialPort = serialPort ?? throw new ArgumentNullException(nameof(serialPort));
    }

    public async Task<BufferHealthReport> PerformHealthCheck()
    {
        var report = new BufferHealthReport();
        var stats = _serialPort.GetBufferStatistics();

        if (stats == null)
        {
            report.OverallHealth = HealthStatus.Unknown;
            report.Issues.Add("无法获取缓冲区统计信息");
            return report;
        }

        // 检查队列使用率
        if (stats.QueueUsagePercentage >= 90)
        {
            report.Issues.Add($"队列使用率过高: {stats.QueueUsagePercentage}%");
            report.OverallHealth = HealthStatus.Critical;
        }
        else if (stats.QueueUsagePercentage >= 70)
        {
            report.Warnings.Add($"队列使用率较高: {stats.QueueUsagePercentage}%");
            if (report.OverallHealth == HealthStatus.Healthy)
                report.OverallHealth = HealthStatus.Warning;
        }

        // 检查数据丢失率
        if (stats.DataLossRate >= 5.0)
        {
            report.Issues.Add($"数据丢失率过高: {stats.DataLossRate:F2}%");
            report.OverallHealth = HealthStatus.Critical;
        }
        else if (stats.DataLossRate >= 1.0)
        {
            report.Warnings.Add($"存在数据丢失: {stats.DataLossRate:F2}%");
            if (report.OverallHealth == HealthStatus.Healthy)
                report.OverallHealth = HealthStatus.Warning;
        }

        // 检查清理频率
        var timeSinceCleanup = DateTime.Now - stats.LastCleanupTime;
        if (timeSinceCleanup.TotalMinutes > 10)
        {
            report.Warnings.Add($"缓冲区清理间隔过长: {timeSinceCleanup.TotalMinutes:F1} 分钟");
            if (report.OverallHealth == HealthStatus.Healthy)
                report.OverallHealth = HealthStatus.Warning;
        }

        // 生成建议
        report.Recommendations.AddRange(stats.GetPerformanceSuggestions());

        // 如果没有问题和警告，则健康状态良好
        if (report.Issues.Count == 0 && report.Warnings.Count == 0)
        {
            report.OverallHealth = HealthStatus.Healthy;
        }

        report.CheckTime = DateTime.Now;
        report.Statistics = stats;

        return report;
    }

    public void PrintHealthReport(BufferHealthReport report)
    {
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    缓冲区健康检查报告                        ║");
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        Console.WriteLine($"║ 检查时间: {report.CheckTime:yyyy-MM-dd HH:mm:ss.fff}              ║");

        var healthIcon = report.OverallHealth switch
        {
            HealthStatus.Healthy => "✅",
            HealthStatus.Warning => "⚠️",
            HealthStatus.Critical => "❌",
            _ => "❓"
        };

        Console.WriteLine($"║ 整体状态: {healthIcon} {report.OverallHealth,-10}                      ║");
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");

        if (report.Issues.Count > 0)
        {
            Console.WriteLine("║ 🚨 严重问题:                                                 ║");
            foreach (var issue in report.Issues)
            {
                var truncated = issue.Length > 54 ? issue.Substring(0, 51) + "..." : issue;
                Console.WriteLine($"║   • {truncated,-53} ║");
            }
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        }

        if (report.Warnings.Count > 0)
        {
            Console.WriteLine("║ ⚠️ 警告信息:                                                  ║");
            foreach (var warning in report.Warnings)
            {
                var truncated = warning.Length > 54 ? warning.Substring(0, 51) + "..." : warning;
                Console.WriteLine($"║   • {truncated,-53} ║");
            }
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        }

        if (report.Recommendations.Count > 0)
        {
            Console.WriteLine("║ 💡 优化建议:                                                 ║");
            foreach (var recommendation in report.Recommendations.Take(3))
            {
                var truncated = recommendation.Length > 54 ? recommendation.Substring(0, 51) + "..." : recommendation;
                Console.WriteLine($"║   • {truncated,-53} ║");
            }
        }

        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
    }
}

public class BufferHealthReport
{
    public DateTime CheckTime { get; set; }
    public HealthStatus OverallHealth { get; set; } = HealthStatus.Healthy;
    public List<string> Issues { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
    public BufferStatistics? Statistics { get; set; }
}

public enum HealthStatus
{
    Healthy,
    Warning,
    Critical,
    Unknown
}
```

---

## 🚦 流控制管理

### 智能流控制示例

```csharp
public class FlowControlExample
{
    public static async Task RunFlowControlExample()
    {
        // 配置流控制
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 115200,

            // 启用流控制
            EnableFlowControl = true,
            FlowControlType = FlowControlType.RtsCts,  // 硬件流控制
            SendRateLimit = 1024,                      // 1KB/s 发送限制

            // 高级缓冲配置
            EnableAdvancedBuffering = true,
            DataQueueMaxLength = 500,
            BufferOverflowStrategy = BufferOverflowStrategy.Block
        };

        using var serialPort = new SerialPortService(config);

        try
        {
            Console.WriteLine("=== 流控制管理示例 ===");
            await serialPort.OpenAsync();

            // 监控流控制状态
            var monitorTask = MonitorFlowControl(serialPort);

            // 发送大量数据测试流控制
            await SendLargeDataWithFlowControl(serialPort);

            await Task.Delay(5000); // 等待监控完成
        }
        catch (Exception ex)
        {
            Console.WriteLine($"流控制示例错误: {ex.Message}");
        }
        finally
        {
            await serialPort.CloseAsync();
        }
    }

    private static async Task MonitorFlowControl(SerialPortService serialPort)
    {
        while (serialPort.IsConnected)
        {
            var flowStats = serialPort.GetFlowControlStatistics();
            if (flowStats != null && flowStats.IsEnabled)
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 流控制状态:");
                Console.WriteLine($"  类型: {flowStats.FlowControlType}");
                Console.WriteLine($"  状态: {flowStats.CurrentStatus}");
                Console.WriteLine($"  发送速率: {flowStats.CurrentSendRate:F2}/{flowStats.SendRateLimit} 字节/秒");
                Console.WriteLine($"  使用率: {flowStats.RateUsagePercentage:F1}%");

                if (flowStats.IsXoffReceived)
                    Console.WriteLine("  ⚠️ 收到 XOFF 信号，发送暂停");

                if (flowStats.IsRtsPaused)
                    Console.WriteLine("  ⚠️ RTS 流控制暂停");

                Console.WriteLine();
            }

            await Task.Delay(2000);
        }
    }

    private static async Task SendLargeDataWithFlowControl(SerialPortService serialPort)
    {
        Console.WriteLine("开始发送大量数据，测试流控制...");

        var totalBytes = 0;
        var startTime = DateTime.Now;

        for (int i = 1; i <= 100; i++)
        {
            var data = $"Large data packet {i:D3}: " + new string('X', 100) + "\r\n";

            try
            {
                await serialPort.SendTextAsync(data);
                totalBytes += data.Length;

                if (i % 10 == 0)
                {
                    var elapsed = DateTime.Now - startTime;
                    var actualRate = totalBytes / elapsed.TotalSeconds;
                    Console.WriteLine($"已发送 {i} 包，实际速率: {actualRate:F2} 字节/秒");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"发送第 {i} 包时出错: {ex.Message}");
            }

            // 小延迟以观察流控制效果
            await Task.Delay(10);
        }

        var totalElapsed = DateTime.Now - startTime;
        var finalRate = totalBytes / totalElapsed.TotalSeconds;
        Console.WriteLine($"发送完成，总字节: {totalBytes}, 平均速率: {finalRate:F2} 字节/秒");
    }
}
```

### 自适应流控制

```csharp
public class AdaptiveFlowControlExample
{
    private readonly SerialPortService _serialPort;
    private readonly Timer _adaptiveTimer;
    private double _targetThroughput = 1024; // 目标吞吐量 (字节/秒)

    public AdaptiveFlowControlExample(SerialPortConfiguration config)
    {
        _serialPort = new SerialPortService(config);

        // 每5秒调整一次流控制参数
        _adaptiveTimer = new Timer(AdaptFlowControl, null,
            TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
    }

    private void AdaptFlowControl(object state)
    {
        var flowStats = _serialPort.GetFlowControlStatistics();
        var bufferStats = _serialPort.GetBufferStatistics();

        if (flowStats == null || bufferStats == null) return;

        Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] 自适应流控制调整:");
        Console.WriteLine($"  当前发送速率: {flowStats.CurrentSendRate:F2} 字节/秒");
        Console.WriteLine($"  缓冲区使用率: {bufferStats.QueueUsagePercentage}%");
        Console.WriteLine($"  数据丢失率: {bufferStats.DataLossRate:F2}%");

        // 根据缓冲区状态调整发送速率
        if (bufferStats.QueueUsagePercentage > 80)
        {
            // 缓冲区使用率过高，降低发送速率
            _targetThroughput *= 0.8;
            Console.WriteLine($"  📉 降低目标吞吐量至: {_targetThroughput:F2} 字节/秒");
        }
        else if (bufferStats.QueueUsagePercentage < 30 && bufferStats.DataLossRate < 0.1)
        {
            // 缓冲区使用率低且无数据丢失，可以提高发送速率
            _targetThroughput *= 1.1;
            Console.WriteLine($"  📈 提高目标吞吐量至: {_targetThroughput:F2} 字节/秒");
        }

        // 限制吞吐量范围
        _targetThroughput = Math.Max(256, Math.Min(_targetThroughput, 4096));

        // 检查流控制状态并给出建议
        if (flowStats.IsXoffReceived || flowStats.IsRtsPaused)
        {
            Console.WriteLine("  ⚠️ 流控制暂停中，建议检查对端处理能力");
        }

        if (flowStats.RateUsagePercentage > 95)
        {
            Console.WriteLine("  ⚠️ 发送速率接近限制，考虑优化数据发送策略");
        }

        Console.WriteLine();
    }

    public async Task RunAdaptiveExample()
    {
        await _serialPort.OpenAsync();

        Console.WriteLine("自适应流控制示例运行中...");
        Console.WriteLine($"初始目标吞吐量: {_targetThroughput} 字节/秒");

        // 持续发送数据，观察自适应调整
        var sendTask = Task.Run(async () =>
        {
            var packetId = 1;
            while (_serialPort.IsConnected)
            {
                try
                {
                    var data = $"Adaptive packet {packetId++}: " +
                              new string('A', (int)(_targetThroughput / 10)) + "\r\n";

                    await _serialPort.SendTextAsync(data);

                    // 根据目标吞吐量调整发送间隔
                    var delay = (int)(1000.0 / (_targetThroughput / data.Length));
                    await Task.Delay(Math.Max(10, delay));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"发送数据时出错: {ex.Message}");
                    await Task.Delay(1000);
                }
            }
        });

        // 运行30秒
        await Task.Delay(30000);

        _adaptiveTimer?.Dispose();
        await _serialPort.CloseAsync();
    }
}
```

---

## 🔧 性能优化与诊断

### 性能分析器

```csharp
public class BufferPerformanceAnalyzer
{
    private readonly SerialPortService _serialPort;
    private readonly List<PerformanceSnapshot> _snapshots = new();
    private readonly Timer _analysisTimer;

    public BufferPerformanceAnalyzer(SerialPortService serialPort)
    {
        _serialPort = serialPort ?? throw new ArgumentNullException(nameof(serialPort));

        // 每秒采集一次性能数据
        _analysisTimer = new Timer(TakeSnapshot, null,
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    private void TakeSnapshot(object state)
    {
        var bufferStats = _serialPort.GetBufferStatistics();
        var flowStats = _serialPort.GetFlowControlStatistics();

        if (bufferStats == null) return;

        var snapshot = new PerformanceSnapshot
        {
            Timestamp = DateTime.Now,
            QueueLength = bufferStats.QueueLength,
            QueueUsagePercentage = bufferStats.QueueUsagePercentage,
            TotalBytesReceived = bufferStats.TotalBytesReceived,
            TotalBytesDropped = bufferStats.TotalBytesDropped,
            DataLossRate = bufferStats.DataLossRate,
            CurrentSendRate = flowStats?.CurrentSendRate ?? 0
        };

        lock (_snapshots)
        {
            _snapshots.Add(snapshot);

            // 保持最近300个快照 (5分钟的数据)
            if (_snapshots.Count > 300)
            {
                _snapshots.RemoveAt(0);
            }
        }
    }

    public PerformanceReport GenerateReport()
    {
        lock (_snapshots)
        {
            if (_snapshots.Count < 2)
                return new PerformanceReport { IsValid = false };

            var report = new PerformanceReport { IsValid = true };
            var recent = _snapshots.TakeLast(60).ToList(); // 最近1分钟

            // 基础统计
            report.AverageQueueUsage = recent.Average(s => s.QueueUsagePercentage);
            report.MaxQueueUsage = recent.Max(s => s.QueueUsagePercentage);
            report.AverageDataLossRate = recent.Average(s => s.DataLossRate);
            report.AverageSendRate = recent.Average(s => s.CurrentSendRate);

            // 趋势分析
            if (recent.Count >= 30)
            {
                var firstHalf = recent.Take(30).Average(s => s.QueueUsagePercentage);
                var secondHalf = recent.Skip(30).Average(s => s.QueueUsagePercentage);
                report.UsageTrend = secondHalf > firstHalf + 5 ? "上升" :
                                   secondHalf < firstHalf - 5 ? "下降" : "稳定";
            }

            // 性能问题检测
            report.PerformanceIssues = DetectPerformanceIssues(recent);

            // 优化建议
            report.OptimizationSuggestions = GenerateOptimizationSuggestions(recent);

            return report;
        }
    }

    private List<string> DetectPerformanceIssues(List<PerformanceSnapshot> snapshots)
    {
        var issues = new List<string>();

        // 检测高使用率持续时间
        var highUsageCount = snapshots.Count(s => s.QueueUsagePercentage > 80);
        if (highUsageCount > snapshots.Count * 0.5)
        {
            issues.Add($"缓冲区高使用率持续时间过长 ({highUsageCount}/{snapshots.Count} 次采样)");
        }

        // 检测数据丢失
        var lossCount = snapshots.Count(s => s.DataLossRate > 0);
        if (lossCount > 0)
        {
            issues.Add($"检测到数据丢失 ({lossCount}/{snapshots.Count} 次采样有丢失)");
        }

        // 检测发送速率波动
        if (snapshots.Count > 10)
        {
            var sendRates = snapshots.Select(s => s.CurrentSendRate).Where(r => r > 0).ToList();
            if (sendRates.Count > 5)
            {
                var avgRate = sendRates.Average();
                var maxDeviation = sendRates.Max(r => Math.Abs(r - avgRate));
                if (maxDeviation > avgRate * 0.5)
                {
                    issues.Add($"发送速率波动较大 (最大偏差: {maxDeviation:F2} 字节/秒)");
                }
            }
        }

        return issues;
    }

    private List<string> GenerateOptimizationSuggestions(List<PerformanceSnapshot> snapshots)
    {
        var suggestions = new List<string>();

        var avgUsage = snapshots.Average(s => s.QueueUsagePercentage);
        var avgLoss = snapshots.Average(s => s.DataLossRate);

        if (avgUsage > 70)
        {
            suggestions.Add("考虑增加数据队列最大长度");
            suggestions.Add("优化数据处理逻辑以提高处理速度");
        }

        if (avgLoss > 0.5)
        {
            suggestions.Add("调整缓冲区溢出策略为 Block 或 Expand");
            suggestions.Add("增加缓冲区清理频率");
        }

        if (avgUsage < 20 && avgLoss < 0.1)
        {
            suggestions.Add("当前缓冲区配置良好，可以考虑减少资源占用");
        }

        var sendRateVariance = snapshots.Where(s => s.CurrentSendRate > 0)
                                      .Select(s => s.CurrentSendRate)
                                      .DefaultIfEmpty(0)
                                      .Aggregate(0.0, (acc, rate) => acc + Math.Pow(rate - snapshots.Average(s => s.CurrentSendRate), 2)) / snapshots.Count;

        if (sendRateVariance > 1000)
        {
            suggestions.Add("发送速率不稳定，建议启用流控制");
        }

        return suggestions;
    }

    public void PrintReport()
    {
        var report = GenerateReport();
        if (!report.IsValid)
        {
            Console.WriteLine("性能报告：数据不足，无法生成报告");
            return;
        }

        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    缓冲区性能分析报告                        ║");
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        Console.WriteLine($"║ 报告时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}              ║");
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        Console.WriteLine($"║ 平均队列使用率: {report.AverageQueueUsage,5:F1}%                      ║");
        Console.WriteLine($"║ 最大队列使用率: {report.MaxQueueUsage,5:F1}%                      ║");
        Console.WriteLine($"║ 平均数据丢失率: {report.AverageDataLossRate,5:F2}%                      ║");
        Console.WriteLine($"║ 平均发送速率: {report.AverageSendRate,7:F2} 字节/秒                ║");
        Console.WriteLine($"║ 使用率趋势: {report.UsageTrend,-10}                              ║");
        Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");

        if (report.PerformanceIssues.Count > 0)
        {
            Console.WriteLine("║ 🚨 性能问题:                                                 ║");
            foreach (var issue in report.PerformanceIssues.Take(3))
            {
                var truncated = issue.Length > 54 ? issue.Substring(0, 51) + "..." : issue;
                Console.WriteLine($"║   • {truncated,-53} ║");
            }
            Console.WriteLine("╠══════════════════════════════════════════════════════════════╣");
        }

        if (report.OptimizationSuggestions.Count > 0)
        {
            Console.WriteLine("║ 💡 优化建议:                                                 ║");
            foreach (var suggestion in report.OptimizationSuggestions.Take(3))
            {
                var truncated = suggestion.Length > 54 ? suggestion.Substring(0, 51) + "..." : suggestion;
                Console.WriteLine($"║   • {truncated,-53} ║");
            }
        }

        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝");
    }

    public void Dispose()
    {
        _analysisTimer?.Dispose();
    }
}

public class PerformanceSnapshot
{
    public DateTime Timestamp { get; set; }
    public int QueueLength { get; set; }
    public int QueueUsagePercentage { get; set; }
    public long TotalBytesReceived { get; set; }
    public long TotalBytesDropped { get; set; }
    public double DataLossRate { get; set; }
    public double CurrentSendRate { get; set; }
}

public class PerformanceReport
{
    public bool IsValid { get; set; }
    public double AverageQueueUsage { get; set; }
    public double MaxQueueUsage { get; set; }
    public double AverageDataLossRate { get; set; }
    public double AverageSendRate { get; set; }
    public string UsageTrend { get; set; } = "未知";
    public List<string> PerformanceIssues { get; set; } = new();
    public List<string> OptimizationSuggestions { get; set; } = new();
}
```

---

## 📦 批量数据处理优化

### 智能批量处理器

```csharp
public class SmartBatchProcessor
{
    private readonly SerialPortService _serialPort;
    private readonly ConcurrentQueue<SerialPortData> _processingQueue = new();
    private readonly Timer _batchTimer;
    private readonly SemaphoreSlim _processingLock = new(1, 1);

    private int _batchSize = 50;
    private int _batchTimeoutMs = 1000;
    private readonly int _maxBatchSize = 200;
    private readonly int _minBatchSize = 10;

    public SmartBatchProcessor(SerialPortService serialPort)
    {
        _serialPort = serialPort ?? throw new ArgumentNullException(nameof(serialPort));

        // 动态调整批处理参数
        _batchTimer = new Timer(ProcessBatchTimeout, null,
            _batchTimeoutMs, _batchTimeoutMs);

        // 订阅数据接收事件
        _serialPort.DataReceived += OnDataReceived;
    }

    private async void OnDataReceived(object sender, SerialPortDataReceivedEventArgs e)
    {
        _processingQueue.Enqueue(e.Data);

        // 动态调整批处理大小
        await AdjustBatchParameters();

        // 检查是否达到批处理条件
        if (_processingQueue.Count >= _batchSize)
        {
            await ProcessBatch();
        }
    }

    private async Task AdjustBatchParameters()
    {
        var bufferStats = _serialPort.GetBufferStatistics();
        if (bufferStats == null) return;

        var queueLength = _processingQueue.Count;
        var bufferUsage = bufferStats.QueueUsagePercentage;

        // 根据缓冲区使用率调整批处理大小
        if (bufferUsage > 80 || queueLength > 100)
        {
            // 高负载时增加批处理大小，减少处理频率
            _batchSize = Math.Min(_maxBatchSize, _batchSize + 10);
            _batchTimeoutMs = Math.Max(500, _batchTimeoutMs - 100);
        }
        else if (bufferUsage < 30 && queueLength < 20)
        {
            // 低负载时减少批处理大小，提高响应性
            _batchSize = Math.Max(_minBatchSize, _batchSize - 5);
            _batchTimeoutMs = Math.Min(2000, _batchTimeoutMs + 100);
        }

        // 更新定时器间隔
        _batchTimer?.Change(_batchTimeoutMs, _batchTimeoutMs);
    }

    private async void ProcessBatchTimeout(object state)
    {
        if (_processingQueue.Count > 0)
        {
            await ProcessBatch();
        }
    }

    private async Task ProcessBatch()
    {
        await _processingLock.WaitAsync();
        try
        {
            var batchData = new List<SerialPortData>();

            // 收集批处理数据
            while (batchData.Count < _batchSize && _processingQueue.TryDequeue(out var data))
            {
                batchData.Add(data);
            }

            if (batchData.Count == 0) return;

            Console.WriteLine($"[{DateTime.Now:HH:mm:ss.fff}] 批处理 {batchData.Count} 条数据 " +
                            $"(批大小: {_batchSize}, 超时: {_batchTimeoutMs}ms)");

            // 并行处理批数据
            var processingTasks = batchData.Select(async (data, index) =>
            {
                await ProcessSingleData(data, index);
            });

            var startTime = DateTime.Now;
            await Task.WhenAll(processingTasks);
            var processingTime = DateTime.Now - startTime;

            Console.WriteLine($"批处理完成，耗时: {processingTime.TotalMilliseconds:F2}ms, " +
                            $"平均: {processingTime.TotalMilliseconds / batchData.Count:F2}ms/条");

            // 根据处理时间调整参数
            if (processingTime.TotalMilliseconds > _batchTimeoutMs * 0.8)
            {
                Console.WriteLine("处理时间接近超时，考虑减少批大小");
            }
        }
        finally
        {
            _processingLock.Release();
        }
    }

    private async Task ProcessSingleData(SerialPortData data, int index)
    {
        // 模拟数据处理
        await Task.Delay(Random.Shared.Next(10, 50));

        // 这里可以添加实际的数据处理逻辑
        var processedText = data.ToText().Trim();
        if (!string.IsNullOrEmpty(processedText))
        {
            Console.WriteLine($"  [{index:D2}] 处理: {processedText.Substring(0, Math.Min(30, processedText.Length))}...");
        }
    }

    public void PrintStatistics()
    {
        Console.WriteLine($"批处理器状态:");
        Console.WriteLine($"  当前批大小: {_batchSize}");
        Console.WriteLine($"  超时时间: {_batchTimeoutMs}ms");
        Console.WriteLine($"  待处理队列: {_processingQueue.Count}");
    }

    public void Dispose()
    {
        _batchTimer?.Dispose();
        _processingLock?.Dispose();
    }
}
```

### 完整的高级缓冲管理示例

```csharp
public class ComprehensiveBufferingExample
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("=== Alicres.SerialPort 高级缓冲管理综合示例 ===\n");

        // 配置高级缓冲管理
        var config = new SerialPortConfiguration
        {
            PortName = GetAvailablePort(),
            BaudRate = 115200,

            // 高级缓冲配置
            EnableAdvancedBuffering = true,
            DataQueueMaxLength = 1000,
            BufferOverflowStrategy = BufferOverflowStrategy.DropOldest,
            BufferCleanupInterval = 3000,
            BufferWarningThreshold = 75,

            // 流控制配置
            EnableFlowControl = true,
            FlowControlType = FlowControlType.XonXoff,
            SendRateLimit = 2048
        };

        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));
        var logger = loggerFactory.CreateLogger<SerialPortService>();

        using var serialPort = new SerialPortService(config, logger);

        try
        {
            await serialPort.OpenAsync();
            Console.WriteLine($"串口 {config.PortName} 已打开\n");

            // 创建各种管理器
            using var dashboard = new BufferMonitoringDashboard(config);
            using var healthChecker = new BufferHealthChecker(serialPort);
            using var performanceAnalyzer = new BufferPerformanceAnalyzer(serialPort);
            using var batchProcessor = new SmartBatchProcessor(serialPort);

            // 启动监控任务
            var monitoringTask = StartMonitoring(serialPort, healthChecker, performanceAnalyzer, batchProcessor);

            // 模拟数据流
            var dataGenerationTask = GenerateTestData(serialPort);

            Console.WriteLine("示例运行中，按任意键查看详细报告...");
            Console.ReadKey();

            // 生成详细报告
            await GenerateDetailedReport(serialPort, healthChecker, performanceAnalyzer);

            Console.WriteLine("\n按任意键退出...");
            Console.ReadKey();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"示例运行错误: {ex.Message}");
        }
        finally
        {
            await serialPort.CloseAsync();
            Console.WriteLine("串口已关闭");
        }
    }

    private static async Task StartMonitoring(SerialPortService serialPort,
        BufferHealthChecker healthChecker, BufferPerformanceAnalyzer performanceAnalyzer,
        SmartBatchProcessor batchProcessor)
    {
        _ = Task.Run(async () =>
        {
            while (serialPort.IsConnected)
            {
                // 每10秒进行一次健康检查
                var healthReport = await healthChecker.PerformHealthCheck();
                if (healthReport.OverallHealth != HealthStatus.Healthy)
                {
                    Console.WriteLine("\n=== 健康检查警告 ===");
                    healthChecker.PrintHealthReport(healthReport);
                }

                // 显示批处理器状态
                batchProcessor.PrintStatistics();

                await Task.Delay(10000);
            }
        });
    }

    private static async Task GenerateTestData(SerialPortService serialPort)
    {
        _ = Task.Run(async () =>
        {
            var random = new Random();
            var messageId = 1;

            while (serialPort.IsConnected)
            {
                try
                {
                    // 模拟不同类型的数据
                    var dataType = random.Next(1, 4);
                    string data = dataType switch
                    {
                        1 => $"STATUS:{messageId++}:OK:{DateTime.Now:HH:mm:ss.fff}",
                        2 => $"DATA:{messageId++}:" + new string('X', random.Next(50, 200)),
                        _ => $"EVENT:{messageId++}:SENSOR_READING:{random.Next(100, 999)}"
                    };

                    await serialPort.SendTextAsync(data + "\r\n");

                    // 随机间隔，模拟真实数据流
                    await Task.Delay(random.Next(50, 300));
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"数据生成错误: {ex.Message}");
                    await Task.Delay(1000);
                }
            }
        });
    }

    private static async Task GenerateDetailedReport(SerialPortService serialPort,
        BufferHealthChecker healthChecker, BufferPerformanceAnalyzer performanceAnalyzer)
    {
        Console.Clear();
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                    综合缓冲管理报告                          ║");
        Console.WriteLine("╚══════════════════════════════════════════════════════════════╝\n");

        // 健康检查报告
        var healthReport = await healthChecker.PerformHealthCheck();
        healthChecker.PrintHealthReport(healthReport);
        Console.WriteLine();

        // 性能分析报告
        performanceAnalyzer.PrintReport();
        Console.WriteLine();

        // 当前缓冲区状态
        var bufferStats = serialPort.GetBufferStatistics();
        var flowStats = serialPort.GetFlowControlStatistics();

        if (bufferStats != null)
        {
            Console.WriteLine("=== 当前缓冲区详细状态 ===");
            Console.WriteLine(bufferStats.GetDetailedReport());
        }

        if (flowStats != null && flowStats.IsEnabled)
        {
            Console.WriteLine("\n=== 流控制状态 ===");
            Console.WriteLine(flowStats.ToString());
        }
    }

    private static string GetAvailablePort()
    {
        var availablePorts = SerialPortService.GetAvailablePorts();
        return availablePorts.Length > 0 ? availablePorts[0] : "COM1";
    }
}
```

---

## 🚀 运行示例

要运行这些高级缓冲管理示例，请按照以下步骤：

1. **基础配置示例**：
   ```bash
   dotnet run -- --example basic-buffering
   ```

2. **实时监控仪表板**：
   ```bash
   dotnet run -- --example monitoring-dashboard
   ```

3. **综合示例**：
   ```bash
   dotnet run -- --example comprehensive
   ```

这些示例展示了 Alicres.SerialPort 1.1.0 版本的完整高级缓冲管理功能，包括智能缓冲策略、实时监控、性能分析和自适应优化等特性。

---

**上一篇**: ← [基本串口通讯示例](basic-communication.md)
**下一篇**: [协议解析示例](protocol-parsing.md) →
