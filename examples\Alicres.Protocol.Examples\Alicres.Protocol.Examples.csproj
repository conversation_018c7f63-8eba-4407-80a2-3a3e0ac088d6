<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <!-- 日志记录 -->
    <PackageReference Include="Microsoft.Extensions.Logging" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" />
    
    <!-- 依赖注入和主机 -->
    <PackageReference Include="Microsoft.Extensions.Hosting" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\src\Alicres.Protocol\Alicres.Protocol.csproj" />
    <ProjectReference Include="..\..\src\Alicres.SerialPort\Alicres.SerialPort.csproj" />
  </ItemGroup>

</Project>
