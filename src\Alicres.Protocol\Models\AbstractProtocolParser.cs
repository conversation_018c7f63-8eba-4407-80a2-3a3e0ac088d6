using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models.EventArgs;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Models;

/// <summary>
/// 抽象协议解析器基类，提供协议解析器的通用实现
/// </summary>
public abstract class AbstractProtocolParser : IProtocolParser
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    protected readonly ILogger? Logger;

    /// <summary>
    /// 协议名称
    /// </summary>
    public abstract string ProtocolName { get; }

    /// <summary>
    /// 协议版本
    /// </summary>
    public virtual string ProtocolVersion => "1.0.0";

    /// <summary>
    /// 是否启用数据校验
    /// </summary>
    public bool ValidationEnabled { get; set; } = true;

    /// <summary>
    /// 数据校验器
    /// </summary>
    public IProtocolValidator? Validator { get; set; }

    /// <summary>
    /// 消息帧处理器
    /// </summary>
    public IMessageFraming? MessageFraming { get; set; }

    /// <summary>
    /// 消息解析完成事件
    /// </summary>
    public event EventHandler<ProtocolMessageEventArgs>? MessageParsed;

    /// <summary>
    /// 消息序列化完成事件
    /// </summary>
    public event EventHandler<ProtocolSerializationEventArgs>? MessageSerialized;

    /// <summary>
    /// 解析错误事件
    /// </summary>
    public event EventHandler<ProtocolErrorEventArgs>? ParseError;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    protected AbstractProtocolParser(ILogger? logger = null)
    {
        Logger = logger;
    }

    /// <summary>
    /// 解析原始数据为协议消息
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的协议消息，如果解析失败返回 null</returns>
    public async Task<IProtocolMessage?> ParseAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);

        try
        {
            Logger?.LogDebug("开始解析 {ProtocolName} 协议数据，长度: {Length} 字节", ProtocolName, data.Length);

            // 如果启用了帧处理，先进行帧处理
            byte[] processedData = data;
            if (MessageFraming != null && MessageFraming.IsEnabled)
            {
                var frames = MessageFraming.ProcessIncomingData(data);
                if (frames.Count == 0)
                {
                    Logger?.LogDebug("帧处理器未提取到完整帧");
                    return null;
                }

                // 使用第一个完整帧进行协议解析
                processedData = frames[0];
                Logger?.LogDebug("使用帧处理器提取的数据进行协议解析，长度: {Length} 字节", processedData.Length);
            }
            else
            {
                // 检查数据是否为完整消息
                if (!IsCompleteMessage(processedData))
                {
                    Logger?.LogWarning("数据不是完整的 {ProtocolName} 协议消息", ProtocolName);
                    return null;
                }
            }

            // 数据校验
            if (ValidationEnabled && Validator != null)
            {
                var isValid = await Validator.ValidateAsync(processedData, cancellationToken);
                if (!isValid)
                {
                    var errorMessage = $"{ProtocolName} 协议数据校验失败";
                    Logger?.LogError(errorMessage);
                    OnParseError(new ProtocolErrorEventArgs(errorMessage, ProtocolName, processedData));
                    return null;
                }
            }

            // 执行具体的解析逻辑
            var message = await ParseInternalAsync(processedData, cancellationToken);
            
            if (message != null)
            {
                Logger?.LogDebug("成功解析 {ProtocolName} 协议消息: {MessageType}", ProtocolName, message.MessageType);
                OnMessageParsed(new ProtocolMessageEventArgs(message, ProtocolName));
            }

            return message;
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "解析 {ProtocolName} 协议数据时发生错误", ProtocolName);
            OnParseError(new ProtocolErrorEventArgs(ex, ProtocolName, data));
            return null;
        }
    }

    /// <summary>
    /// 将协议消息序列化为字节数组
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>序列化后的字节数组</returns>
    public async Task<byte[]> SerializeAsync(IProtocolMessage message, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(message);

        try
        {
            Logger?.LogDebug("开始序列化 {ProtocolName} 协议消息: {MessageType}", ProtocolName, message.MessageType);

            // 验证消息
            if (!ValidateMessage(message))
            {
                throw new InvalidOperationException($"消息验证失败: {message.MessageType}");
            }

            // 执行具体的序列化逻辑
            var data = await SerializeInternalAsync(message, cancellationToken);

            // 添加校验信息
            if (ValidationEnabled && Validator != null)
            {
                data = Validator.AddChecksum(data);
            }

            // 如果启用了帧处理，添加帧信息
            if (MessageFraming != null && MessageFraming.IsEnabled)
            {
                data = MessageFraming.FrameMessage(data);
                Logger?.LogDebug("为协议消息添加帧信息，最终长度: {Length} 字节", data.Length);
            }

            Logger?.LogDebug("成功序列化 {ProtocolName} 协议消息，长度: {Length} 字节", ProtocolName, data.Length);
            OnMessageSerialized(new ProtocolSerializationEventArgs(message, data, ProtocolName));

            return data;
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "序列化 {ProtocolName} 协议消息时发生错误", ProtocolName);
            OnParseError(new ProtocolErrorEventArgs(ex, ProtocolName));
            throw;
        }
    }

    /// <summary>
    /// 验证消息完整性
    /// </summary>
    /// <param name="message">待验证的消息</param>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    public virtual bool ValidateMessage(IProtocolMessage message)
    {
        ArgumentNullException.ThrowIfNull(message);

        // 检查协议名称是否匹配
        if (message.ProtocolName != ProtocolName)
        {
            Logger?.LogWarning("消息协议名称不匹配: 期望 {Expected}，实际 {Actual}", ProtocolName, message.ProtocolName);
            return false;
        }

        // 调用消息自身的验证方法
        return message.Validate();
    }

    /// <summary>
    /// 检查数据是否为完整的协议消息
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果是完整消息返回 true，否则返回 false</returns>
    public abstract bool IsCompleteMessage(byte[] data);

    /// <summary>
    /// 获取消息的预期长度
    /// </summary>
    /// <param name="data">消息数据的开始部分</param>
    /// <returns>消息的预期总长度，如果无法确定返回 -1</returns>
    public abstract int GetExpectedMessageLength(byte[] data);

    /// <summary>
    /// 内部解析逻辑，由子类实现
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的协议消息</returns>
    protected abstract Task<IProtocolMessage?> ParseInternalAsync(byte[] data, CancellationToken cancellationToken);

    /// <summary>
    /// 内部序列化逻辑，由子类实现
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>序列化后的字节数组</returns>
    protected abstract Task<byte[]> SerializeInternalAsync(IProtocolMessage message, CancellationToken cancellationToken);

    /// <summary>
    /// 触发消息解析完成事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnMessageParsed(ProtocolMessageEventArgs e)
    {
        MessageParsed?.Invoke(this, e);
    }

    /// <summary>
    /// 触发消息序列化完成事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnMessageSerialized(ProtocolSerializationEventArgs e)
    {
        MessageSerialized?.Invoke(this, e);
    }

    /// <summary>
    /// 触发解析错误事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnParseError(ProtocolErrorEventArgs e)
    {
        ParseError?.Invoke(this, e);
    }
}
