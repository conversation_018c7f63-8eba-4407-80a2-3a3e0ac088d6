using Alicres.Protocol.Configuration;
using Alicres.Protocol.Validators;
using FluentAssertions;
using Xunit;

namespace Alicres.Protocol.Tests.Configuration;

/// <summary>
/// 协议配置测试
/// </summary>
public class ProtocolConfigurationTests
{
    [Fact]
    public void Constructor_ShouldInitializeWithDefaults()
    {
        // Act
        var config = new ProtocolConfiguration();

        // Assert
        config.Name.Should().BeEmpty();
        config.Version.Should().Be("1.0.0");
        config.Description.Should().BeEmpty();
        config.Fields.Should().BeEmpty();
        config.MinFrameLength.Should().Be(1);
        config.MaxFrameLength.Should().Be(1024);
    }

    [Fact]
    public void AddField_ShouldAddFieldToCollection()
    {
        // Arrange
        var config = new ProtocolConfiguration();
        var field = ProtocolFieldDescriptor.CreateByte("TestField", "Test description");

        // Act
        var result = config.AddField(field);

        // Assert
        result.Should().BeSameAs(config); // 支持链式调用
        config.Fields.Should().HaveCount(1);
        config.Fields[0].Should().BeSameAs(field);
    }

    [Fact]
    public void SetFrameHeader_ShouldSetFrameHeaderFieldName()
    {
        // Arrange
        var config = new ProtocolConfiguration();

        // Act
        var result = config.SetFrameHeader("Header");

        // Assert
        result.Should().BeSameAs(config);
        config.FrameHeaderFieldName.Should().Be("Header");
    }

    [Fact]
    public void GetField_WithExistingField_ShouldReturnField()
    {
        // Arrange
        var config = new ProtocolConfiguration();
        var field = ProtocolFieldDescriptor.CreateByte("TestField");
        config.AddField(field);

        // Act
        var result = config.GetField("TestField");

        // Assert
        result.Should().BeSameAs(field);
    }

    [Fact]
    public void GetField_WithNonExistingField_ShouldReturnNull()
    {
        // Arrange
        var config = new ProtocolConfiguration();

        // Act
        var result = config.GetField("NonExisting");

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void GetField_ShouldBeCaseInsensitive()
    {
        // Arrange
        var config = new ProtocolConfiguration();
        var field = ProtocolFieldDescriptor.CreateByte("TestField");
        config.AddField(field);

        // Act
        var result = config.GetField("testfield");

        // Assert
        result.Should().BeSameAs(field);
    }

    [Fact]
    public void Validate_WithValidConfiguration_ShouldReturnTrue()
    {
        // Arrange
        var config = new ProtocolConfiguration
        {
            Name = "TestProtocol",
            MinFrameLength = 5,
            MaxFrameLength = 100
        };
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field1"));

        // Act
        var (isValid, errors) = config.Validate();

        // Assert
        isValid.Should().BeTrue();
        errors.Should().BeEmpty();
    }

    [Fact]
    public void Validate_WithEmptyName_ShouldReturnFalse()
    {
        // Arrange
        var config = new ProtocolConfiguration();
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field1"));

        // Act
        var (isValid, errors) = config.Validate();

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("协议名称不能为空");
    }

    [Fact]
    public void Validate_WithNoFields_ShouldReturnFalse()
    {
        // Arrange
        var config = new ProtocolConfiguration
        {
            Name = "TestProtocol"
        };

        // Act
        var (isValid, errors) = config.Validate();

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("协议必须至少包含一个字段");
    }

    [Fact]
    public void Validate_WithDuplicateFieldNames_ShouldReturnFalse()
    {
        // Arrange
        var config = new ProtocolConfiguration
        {
            Name = "TestProtocol"
        };
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field1"));
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field1")); // 重复名称

        // Act
        var (isValid, errors) = config.Validate();

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("字段名称重复: Field1");
    }

    [Fact]
    public void Validate_WithInvalidFrameLengthRange_ShouldReturnFalse()
    {
        // Arrange
        var config = new ProtocolConfiguration
        {
            Name = "TestProtocol",
            MinFrameLength = 0,
            MaxFrameLength = 5
        };
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field1"));

        // Act
        var (isValid, errors) = config.Validate();

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("最小帧长度必须大于0");
    }

    [Fact]
    public void Validate_WithMaxLengthLessThanMinLength_ShouldReturnFalse()
    {
        // Arrange
        var config = new ProtocolConfiguration
        {
            Name = "TestProtocol",
            MinFrameLength = 10,
            MaxFrameLength = 5
        };
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field1"));

        // Act
        var (isValid, errors) = config.Validate();

        // Assert
        isValid.Should().BeFalse();
        errors.Should().Contain("最大帧长度必须大于最小帧长度");
    }

    [Fact]
    public void GetFixedFieldsLength_ShouldCalculateCorrectly()
    {
        // Arrange
        var config = new ProtocolConfiguration();
        config.AddField(ProtocolFieldDescriptor.CreateFixedValue("Header", new byte[] { 0x7E }));
        config.AddField(ProtocolFieldDescriptor.CreateByte("Address"));
        config.AddField(ProtocolFieldDescriptor.CreateUInt16("Length"));
        config.AddField(ProtocolFieldDescriptor.CreateVariableData("Data")); // 变长字段，不计入

        // Act
        var length = config.GetFixedFieldsLength();

        // Assert
        length.Should().Be(4); // 1 + 1 + 2 = 4
    }

    [Fact]
    public void SetValidator_ShouldSetValidatorType()
    {
        // Arrange
        var config = new ProtocolConfiguration();
        var checksumRange = ChecksumRangeConfiguration.ExcludeFrameHeaderAndChecksum();

        // Act
        var result = config.SetValidator<CustomCrc16Validator>(checksumRange);

        // Assert
        result.Should().BeSameAs(config);
        config.ValidatorType.Should().Be(typeof(CustomCrc16Validator));
        config.ChecksumRange.Should().BeSameAs(checksumRange);
    }

    [Fact]
    public void ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var config = new ProtocolConfiguration
        {
            Name = "TestProtocol",
            Version = "2.0.0"
        };
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field1"));
        config.AddField(ProtocolFieldDescriptor.CreateByte("Field2"));

        // Act
        var result = config.ToString();

        // Assert
        result.Should().Be("TestProtocol v2.0.0 (2 字段)");
    }
}

/// <summary>
/// 校验范围配置测试
/// </summary>
public class ChecksumRangeConfigurationTests
{
    [Fact]
    public void ExcludeFrameHeaderAndChecksum_ShouldCreateCorrectConfiguration()
    {
        // Act
        var config = ChecksumRangeConfiguration.ExcludeFrameHeaderAndChecksum();

        // Assert
        config.ExcludeFrameHeader.Should().BeTrue();
        config.ExcludeChecksumField.Should().BeTrue();
        config.ExcludedFields.Should().BeEmpty();
        config.StartOffset.Should().Be(0);
        config.EndOffset.Should().Be(0);
    }

    [Fact]
    public void ExcludeFields_ShouldCreateCorrectConfiguration()
    {
        // Act
        var config = ChecksumRangeConfiguration.ExcludeFields("Field1", "Field2");

        // Assert
        config.ExcludedFields.Should().Contain("Field1");
        config.ExcludedFields.Should().Contain("Field2");
        config.ExcludeChecksumField.Should().BeTrue();
        config.ExcludeFrameHeader.Should().BeFalse();
    }
}
