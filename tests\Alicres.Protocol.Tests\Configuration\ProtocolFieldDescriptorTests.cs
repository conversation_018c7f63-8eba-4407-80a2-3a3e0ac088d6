using Alicres.Protocol.Configuration;
using FluentAssertions;
using Xunit;

namespace Alicres.Protocol.Tests.Configuration;

/// <summary>
/// 协议字段描述符测试
/// </summary>
public class ProtocolFieldDescriptorTests
{
    [Fact]
    public void CreateFixedValue_ShouldCreateCorrectDescriptor()
    {
        // Arrange
        var fixedValue = new byte[] { 0x7E, 0x7E };
        var name = "Header";
        var description = "Frame header";

        // Act
        var descriptor = ProtocolFieldDescriptor.CreateFixedValue(name, fixedValue, description);

        // Assert
        descriptor.Name.Should().Be(name);
        descriptor.Type.Should().Be(ProtocolFieldType.FixedValue);
        descriptor.Length.Should().Be(2);
        descriptor.FixedValue.Should().Equal(fixedValue);
        descriptor.Description.Should().Be(description);
        descriptor.IsRequired.Should().BeTrue();
    }

    [Fact]
    public void CreateByte_ShouldCreateCorrectDescriptor()
    {
        // Arrange
        var name = "Address";
        var description = "Device address";

        // Act
        var descriptor = ProtocolFieldDescriptor.CreateByte(name, description);

        // Assert
        descriptor.Name.Should().Be(name);
        descriptor.Type.Should().Be(ProtocolFieldType.Byte);
        descriptor.Length.Should().Be(1);
        descriptor.Description.Should().Be(description);
        descriptor.IsRequired.Should().BeTrue();
    }

    [Fact]
    public void CreateUInt16_ShouldCreateCorrectDescriptor()
    {
        // Arrange
        var name = "Length";
        var byteOrder = ByteOrder.BigEndian;
        var description = "Data length";

        // Act
        var descriptor = ProtocolFieldDescriptor.CreateUInt16(name, byteOrder, description);

        // Assert
        descriptor.Name.Should().Be(name);
        descriptor.Type.Should().Be(ProtocolFieldType.UInt16);
        descriptor.Length.Should().Be(2);
        descriptor.ByteOrder.Should().Be(byteOrder);
        descriptor.Description.Should().Be(description);
    }

    [Fact]
    public void CreateUInt16_WithDefaultByteOrder_ShouldUseLittleEndian()
    {
        // Act
        var descriptor = ProtocolFieldDescriptor.CreateUInt16("Test");

        // Assert
        descriptor.ByteOrder.Should().Be(ByteOrder.LittleEndian);
    }

    [Fact]
    public void CreateLength_ShouldCreateCorrectDescriptor()
    {
        // Arrange
        var name = "DataLength";
        var length = 2;
        var meaning = LengthFieldMeaning.DataLength;
        var byteOrder = ByteOrder.BigEndian;
        var description = "Length of data field";

        // Act
        var descriptor = ProtocolFieldDescriptor.CreateLength(name, length, meaning, byteOrder, description);

        // Assert
        descriptor.Name.Should().Be(name);
        descriptor.Type.Should().Be(ProtocolFieldType.Length);
        descriptor.Length.Should().Be(length);
        descriptor.LengthMeaning.Should().Be(meaning);
        descriptor.ByteOrder.Should().Be(byteOrder);
        descriptor.Description.Should().Be(description);
    }

    [Fact]
    public void CreateLength_WithDefaults_ShouldUseCorrectDefaults()
    {
        // Act
        var descriptor = ProtocolFieldDescriptor.CreateLength("Test", 1);

        // Assert
        descriptor.LengthMeaning.Should().Be(LengthFieldMeaning.TotalFrameLength);
        descriptor.ByteOrder.Should().Be(ByteOrder.LittleEndian);
    }

    [Fact]
    public void CreateVariableData_ShouldCreateCorrectDescriptor()
    {
        // Arrange
        var name = "Payload";
        var description = "Variable payload data";

        // Act
        var descriptor = ProtocolFieldDescriptor.CreateVariableData(name, description);

        // Assert
        descriptor.Name.Should().Be(name);
        descriptor.Type.Should().Be(ProtocolFieldType.VariableData);
        descriptor.Length.Should().Be(-1);
        descriptor.Description.Should().Be(description);
    }

    [Fact]
    public void CreateChecksum_ShouldCreateCorrectDescriptor()
    {
        // Arrange
        var name = "CRC16";
        var length = 2;
        var byteOrder = ByteOrder.BigEndian;
        var description = "CRC16 checksum";

        // Act
        var descriptor = ProtocolFieldDescriptor.CreateChecksum(name, length, byteOrder, description);

        // Assert
        descriptor.Name.Should().Be(name);
        descriptor.Type.Should().Be(ProtocolFieldType.Checksum);
        descriptor.Length.Should().Be(length);
        descriptor.ByteOrder.Should().Be(byteOrder);
        descriptor.Description.Should().Be(description);
    }

    [Fact]
    public void CreateCustom_ShouldCreateCorrectDescriptor()
    {
        // Arrange
        var name = "CustomField";
        var length = 4;
        var description = "Custom field";
        Func<object, byte[]> serializer = obj => new byte[4];
        Func<byte[], object> deserializer = bytes => new object();

        // Act
        var descriptor = ProtocolFieldDescriptor.CreateCustom(name, length, serializer, deserializer, description);

        // Assert
        descriptor.Name.Should().Be(name);
        descriptor.Type.Should().Be(ProtocolFieldType.Custom);
        descriptor.Length.Should().Be(length);
        descriptor.CustomSerializer.Should().BeSameAs(serializer);
        descriptor.CustomDeserializer.Should().BeSameAs(deserializer);
        descriptor.Description.Should().Be(description);
    }

    [Theory]
    [InlineData(ProtocolFieldType.FixedValue, 2, "TestField(FixedValue, 2字节, 固定值: 7E-7E)")]
    [InlineData(ProtocolFieldType.Byte, 1, "TestField(Byte, 1字节)")]
    [InlineData(ProtocolFieldType.VariableData, -1, "TestField(VariableData, 变长)")]
    public void ToString_ShouldReturnFormattedString(ProtocolFieldType type, int length, string expected)
    {
        // Arrange
        var descriptor = new ProtocolFieldDescriptor
        {
            Name = "TestField",
            Type = type,
            Length = length
        };

        if (type == ProtocolFieldType.FixedValue)
        {
            descriptor.FixedValue = new byte[] { 0x7E, 0x7E };
        }

        // Act
        var result = descriptor.ToString();

        // Assert
        result.Should().Be(expected);
    }

    [Fact]
    public void ToString_WithDescription_ShouldIncludeDescription()
    {
        // Arrange
        var descriptor = new ProtocolFieldDescriptor
        {
            Name = "TestField",
            Type = ProtocolFieldType.Byte,
            Length = 1,
            Description = "Test description"
        };

        // Act
        var result = descriptor.ToString();

        // Assert
        result.Should().Be("TestField(Byte, 1字节, Test description)");
    }

    [Fact]
    public void Constructor_ShouldInitializeWithDefaults()
    {
        // Act
        var descriptor = new ProtocolFieldDescriptor();

        // Assert
        descriptor.Name.Should().BeEmpty();
        descriptor.Type.Should().Be(ProtocolFieldType.FixedValue);
        descriptor.Length.Should().Be(0);
        descriptor.Offset.Should().Be(-1);
        descriptor.ByteOrder.Should().Be(ByteOrder.LittleEndian);
        descriptor.FixedValue.Should().BeNull();
        descriptor.LengthMeaning.Should().Be(LengthFieldMeaning.TotalFrameLength);
        descriptor.IsRequired.Should().BeTrue();
        descriptor.Description.Should().BeEmpty();
        descriptor.CustomValidator.Should().BeNull();
        descriptor.CustomSerializer.Should().BeNull();
        descriptor.CustomDeserializer.Should().BeNull();
    }

    [Fact]
    public void Properties_ShouldBeSettable()
    {
        // Arrange
        var descriptor = new ProtocolFieldDescriptor();
        var fixedValue = new byte[] { 0xAA };
        Func<byte[], bool> validator = _ => true;
        Func<object, byte[]> serializer = _ => new byte[1];
        Func<byte[], object> deserializer = _ => new object();

        // Act
        descriptor.Name = "TestName";
        descriptor.Type = ProtocolFieldType.Custom;
        descriptor.Length = 5;
        descriptor.Offset = 10;
        descriptor.ByteOrder = ByteOrder.BigEndian;
        descriptor.FixedValue = fixedValue;
        descriptor.LengthMeaning = LengthFieldMeaning.DataLength;
        descriptor.IsRequired = false;
        descriptor.Description = "Test description";
        descriptor.CustomValidator = validator;
        descriptor.CustomSerializer = serializer;
        descriptor.CustomDeserializer = deserializer;

        // Assert
        descriptor.Name.Should().Be("TestName");
        descriptor.Type.Should().Be(ProtocolFieldType.Custom);
        descriptor.Length.Should().Be(5);
        descriptor.Offset.Should().Be(10);
        descriptor.ByteOrder.Should().Be(ByteOrder.BigEndian);
        descriptor.FixedValue.Should().BeSameAs(fixedValue);
        descriptor.LengthMeaning.Should().Be(LengthFieldMeaning.DataLength);
        descriptor.IsRequired.Should().BeFalse();
        descriptor.Description.Should().Be("Test description");
        descriptor.CustomValidator.Should().BeSameAs(validator);
        descriptor.CustomSerializer.Should().BeSameAs(serializer);
        descriptor.CustomDeserializer.Should().BeSameAs(deserializer);
    }
}
