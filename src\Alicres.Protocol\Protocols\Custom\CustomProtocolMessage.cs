using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models;

namespace Alicres.Protocol.Protocols.Custom;

/// <summary>
/// 自定义协议消息基类
/// 协议格式：帧头(1字节) + 源地址(1字节) + 目标地址(1字节) + 命令码(1字节) + 数据长度(1字节) + 数据字段(N字节) + CRC16(2字节)
/// </summary>
public class CustomProtocolMessage : ProtocolMessage
{
    /// <summary>
    /// 协议名称
    /// </summary>
    public override string ProtocolName => "CustomProtocol";

    /// <summary>
    /// 消息类型
    /// </summary>
    public override string MessageType => "CustomMessage";

    /// <summary>
    /// 帧头字节（固定为 0x7E）
    /// </summary>
    public byte FrameHeader { get; set; } = 0x7E;

    /// <summary>
    /// 源地址
    /// </summary>
    public byte SourceAddress { get; set; }

    /// <summary>
    /// 目标地址
    /// </summary>
    public byte TargetAddress { get; set; }

    /// <summary>
    /// 命令码
    /// </summary>
    public byte CommandCode { get; set; }

    /// <summary>
    /// 数据长度（整个帧的长度）
    /// </summary>
    public byte DataLength { get; set; }

    /// <summary>
    /// 数据字段
    /// </summary>
    public byte[] DataField { get; set; } = Array.Empty<byte>();

    /// <summary>
    /// CRC16 校验码
    /// </summary>
    public ushort Crc16 { get; set; }

    /// <summary>
    /// 构造函数
    /// </summary>
    public CustomProtocolMessage() : base()
    {
    }

    /// <summary>
    /// 从原始数据构造消息
    /// </summary>
    /// <param name="rawData">原始数据</param>
    public CustomProtocolMessage(byte[] rawData) : base(rawData)
    {
        ParseFromRawData(rawData);
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="sourceAddress">源地址</param>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="dataField">数据字段</param>
    public CustomProtocolMessage(byte sourceAddress, byte targetAddress, byte commandCode, byte[] dataField) : base()
    {
        SourceAddress = sourceAddress;
        TargetAddress = targetAddress;
        CommandCode = commandCode;
        DataField = dataField ?? Array.Empty<byte>();
        DataLength = (byte)(5 + DataField.Length + 2); // 帧头+源地址+目标地址+命令码+长度字段+数据+CRC16
        
        // 生成原始数据
        RawData = ToBytes();
    }

    /// <summary>
    /// 从原始数据解析消息字段
    /// </summary>
    /// <param name="rawData">原始数据</param>
    private void ParseFromRawData(byte[] rawData)
    {
        if (rawData == null || rawData.Length < 8)
        {
            throw new ArgumentException("原始数据长度不足，无法解析自定义协议消息");
        }

        FrameHeader = rawData[0];
        SourceAddress = rawData[1];
        TargetAddress = rawData[2];
        CommandCode = rawData[3];
        DataLength = rawData[4];

        // 验证数据长度
        if (rawData.Length != DataLength)
        {
            throw new ArgumentException($"数据长度不匹配：期望 {DataLength} 字节，实际 {rawData.Length} 字节");
        }

        // 提取数据字段
        var dataFieldLength = DataLength - 7; // 总长度 - 帧头 - 源地址 - 目标地址 - 命令码 - 长度字段 - CRC16(2字节)
        if (dataFieldLength > 0)
        {
            DataField = new byte[dataFieldLength];
            Array.Copy(rawData, 5, DataField, 0, dataFieldLength);
        }
        else
        {
            DataField = Array.Empty<byte>();
        }

        // 提取 CRC16
        var crcIndex = rawData.Length - 2;
        Crc16 = (ushort)(rawData[crcIndex] | (rawData[crcIndex + 1] << 8)); // 小端字节序
    }

    /// <summary>
    /// 验证消息完整性
    /// </summary>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    protected override bool ValidateInternal()
    {
        // 检查帧头
        if (FrameHeader != 0x7E)
            return false;

        // 检查数据长度
        var expectedLength = 5 + DataField.Length + 2;
        if (DataLength != expectedLength)
            return false;

        // 检查原始数据长度
        if (RawData.Length != DataLength)
            return false;

        // CRC16 校验将由外部校验器处理
        return true;
    }

    /// <summary>
    /// 将消息序列化为字节数组
    /// </summary>
    /// <returns>序列化后的字节数组</returns>
    public override byte[] ToBytes()
    {
        var totalLength = 5 + DataField.Length + 2; // 帧头+源地址+目标地址+命令码+长度+数据+CRC16
        var result = new byte[totalLength];

        result[0] = FrameHeader;
        result[1] = SourceAddress;
        result[2] = TargetAddress;
        result[3] = CommandCode;
        result[4] = (byte)totalLength; // 数据长度字段

        // 复制数据字段
        if (DataField.Length > 0)
        {
            Array.Copy(DataField, 0, result, 5, DataField.Length);
        }

        // CRC16 将在发送时由校验器计算和添加
        // 这里先预留位置
        var crcIndex = result.Length - 2;
        result[crcIndex] = (byte)(Crc16 & 0xFF);
        result[crcIndex + 1] = (byte)((Crc16 >> 8) & 0xFF);

        return result;
    }

    /// <summary>
    /// 获取用于CRC16校验的数据（排除帧头和CRC16字段）
    /// </summary>
    /// <returns>用于校验的数据</returns>
    public byte[] GetDataForCrcCalculation()
    {
        var rawData = ToBytes();
        var dataForCrc = new byte[rawData.Length - 3]; // 排除帧头(1字节)和CRC16(2字节)
        Array.Copy(rawData, 1, dataForCrc, 0, dataForCrc.Length);
        return dataForCrc;
    }

    /// <summary>
    /// 设置CRC16校验码
    /// </summary>
    /// <param name="crc16">CRC16校验码</param>
    public void SetCrc16(ushort crc16)
    {
        Crc16 = crc16;
        
        // 更新原始数据
        var rawData = ToBytes();
        RawData = rawData;
    }

    /// <summary>
    /// 获取消息的字符串表示
    /// </summary>
    /// <returns>消息的字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] {ProtocolName}.{MessageType} " +
               $"(源地址: 0x{SourceAddress:X2}, 目标地址: 0x{TargetAddress:X2}, " +
               $"命令码: 0x{CommandCode:X2}, 数据长度: {DataLength}, " +
               $"数据: {(DataField.Length > 0 ? BitConverter.ToString(DataField) : "无")}, " +
               $"CRC16: 0x{Crc16:X4})";
    }
}
