using Alicres.Protocol.Configuration;
using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models.EventArgs;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace Alicres.Protocol.Framing;

/// <summary>
/// 可配置的帧处理器，支持多种帧头和帧结构配置
/// </summary>
public class ConfigurableFraming : AbstractMessageFraming
{
    /// <summary>
    /// 协议配置
    /// </summary>
    public ProtocolConfiguration Configuration { get; }

    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public override string FramingName => $"ConfigurableFraming_{Configuration.Name}";

    /// <summary>
    /// 帧模式
    /// </summary>
    public override FramingMode Mode => FramingMode.Custom;

    /// <summary>
    /// 帧头字段描述符
    /// </summary>
    private readonly ProtocolFieldDescriptor? _frameHeaderField;

    /// <summary>
    /// 长度字段描述符
    /// </summary>
    private readonly ProtocolFieldDescriptor? _lengthField;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">协议配置</param>
    /// <param name="logger">日志记录器</param>
    public ConfigurableFraming(ProtocolConfiguration configuration, ILogger<ConfigurableFraming>? logger = null) : base(logger)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        var (isValid, errors) = configuration.Validate();
        if (!isValid)
        {
            throw new ArgumentException($"协议配置无效: {string.Join(", ", errors)}");
        }

        _frameHeaderField = configuration.GetFrameHeaderField();
        _lengthField = configuration.GetLengthField();
    }

    /// <summary>
    /// 检查数据是否包含完整的帧
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果包含完整帧返回 true，否则返回 false</returns>
    public override bool HasCompleteFrame(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < Configuration.MinFrameLength)
            return false;

        // 查找帧头
        var headerIndex = FindFrameHeader(data);
        if (headerIndex == -1)
            return false;

        // 如果没有长度字段，使用最小帧长度检查
        if (_lengthField == null)
        {
            return data.Length >= headerIndex + Configuration.MinFrameLength;
        }

        // 检查是否有足够的数据读取长度字段
        var lengthFieldOffset = CalculateLengthFieldOffset(headerIndex);
        if (data.Length < lengthFieldOffset + _lengthField.Length)
            return false;

        // 读取长度字段
        var frameLength = ReadLengthField(data, lengthFieldOffset);
        
        if (frameLength < Configuration.MinFrameLength || frameLength > Configuration.MaxFrameLength)
        {
            Logger?.LogWarning("无效的帧长度: {Length}", frameLength);
            return false;
        }

        // 检查是否有完整的帧数据
        var expectedLength = CalculateExpectedFrameLength(frameLength);
        return data.Length >= headerIndex + expectedLength;
    }

    /// <summary>
    /// 获取帧的预期长度
    /// </summary>
    /// <param name="data">帧数据的开始部分</param>
    /// <returns>帧的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedFrameLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        var headerIndex = FindFrameHeader(data);
        if (headerIndex == -1)
            return -1;

        if (_lengthField == null)
            return Configuration.MinFrameLength;

        var lengthFieldOffset = CalculateLengthFieldOffset(headerIndex);
        if (data.Length < lengthFieldOffset + _lengthField.Length)
            return -1;

        var frameLength = ReadLengthField(data, lengthFieldOffset);
        
        if (frameLength < Configuration.MinFrameLength || frameLength > Configuration.MaxFrameLength)
            return -1;

        return CalculateExpectedFrameLength(frameLength);
    }

    /// <summary>
    /// 查找帧头在数据中的位置
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>帧头位置，如果未找到返回 -1</returns>
    private int FindFrameHeader(byte[] data)
    {
        if (_frameHeaderField?.FixedValue == null)
            return 0; // 没有帧头字段，从开始位置处理

        var headerBytes = _frameHeaderField.FixedValue;
        
        for (int i = 0; i <= data.Length - headerBytes.Length; i++)
        {
            bool found = true;
            for (int j = 0; j < headerBytes.Length; j++)
            {
                if (data[i + j] != headerBytes[j])
                {
                    found = false;
                    break;
                }
            }
            
            if (found)
                return i;
        }
        
        return -1;
    }

    /// <summary>
    /// 计算长度字段的偏移量
    /// </summary>
    /// <param name="frameStartIndex">帧开始位置</param>
    /// <returns>长度字段偏移量</returns>
    private int CalculateLengthFieldOffset(int frameStartIndex)
    {
        if (_lengthField == null)
            return frameStartIndex;

        var offset = frameStartIndex;
        
        // 计算长度字段之前所有字段的长度
        foreach (var field in Configuration.Fields)
        {
            if (field.Name == _lengthField.Name)
                break;
                
            if (field.Length > 0)
                offset += field.Length;
        }
        
        return offset;
    }

    /// <summary>
    /// 读取长度字段的值
    /// </summary>
    /// <param name="data">数据</param>
    /// <param name="offset">长度字段偏移量</param>
    /// <returns>长度值</returns>
    private int ReadLengthField(byte[] data, int offset)
    {
        if (_lengthField == null)
            return 0;

        return _lengthField.Length switch
        {
            1 => data[offset],
            2 => _lengthField.ByteOrder == ByteOrder.LittleEndian 
                ? (ushort)(data[offset] | (data[offset + 1] << 8))
                : (ushort)((data[offset] << 8) | data[offset + 1]),
            4 => _lengthField.ByteOrder == ByteOrder.LittleEndian
                ? (int)(data[offset] | (data[offset + 1] << 8) | (data[offset + 2] << 16) | (data[offset + 3] << 24))
                : (int)((data[offset] << 24) | (data[offset + 1] << 16) | (data[offset + 2] << 8) | data[offset + 3]),
            _ => throw new NotSupportedException($"不支持的长度字段大小: {_lengthField.Length}")
        };
    }

    /// <summary>
    /// 计算预期的帧长度
    /// </summary>
    /// <param name="declaredLength">声明的长度</param>
    /// <returns>预期的帧长度</returns>
    private int CalculateExpectedFrameLength(int declaredLength)
    {
        if (_lengthField == null)
            return declaredLength;

        return _lengthField.LengthMeaning switch
        {
            LengthFieldMeaning.TotalFrameLength => declaredLength,
            LengthFieldMeaning.DataLength => declaredLength + Configuration.GetFixedFieldsLength(),
            LengthFieldMeaning.RemainingBytes => declaredLength + CalculateLengthFieldOffset(0) + _lengthField.Length,
            _ => throw new NotSupportedException($"不支持的长度字段含义: {_lengthField.LengthMeaning}")
        };
    }

    /// <summary>
    /// 为消息添加帧信息的具体实现
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <returns>添加帧信息后的数据</returns>
    protected override byte[] AddFrameToMessage(byte[] message)
    {
        ArgumentNullException.ThrowIfNull(message);

        // 如果没有帧头字段，直接返回原始消息
        if (_frameHeaderField?.FixedValue == null)
            return message;

        var framedMessage = new byte[_frameHeaderField.FixedValue.Length + message.Length];
        Array.Copy(_frameHeaderField.FixedValue, 0, framedMessage, 0, _frameHeaderField.FixedValue.Length);
        Array.Copy(message, 0, framedMessage, _frameHeaderField.FixedValue.Length, message.Length);

        Logger?.LogTrace("为消息添加帧头，帧长度: {Length}", framedMessage.Length);

        return framedMessage;
    }

    /// <summary>
    /// 从帧数据中移除帧信息的具体实现
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>移除帧信息后的原始消息</returns>
    protected override byte[] RemoveFrameFromMessage(byte[] framedData)
    {
        ArgumentNullException.ThrowIfNull(framedData);

        var headerIndex = FindFrameHeader(framedData);
        if (headerIndex == -1)
        {
            Logger?.LogWarning("未找到帧头，返回原始数据");
            return framedData;
        }

        // 如果没有帧头字段，返回原始数据
        if (_frameHeaderField?.FixedValue == null)
            return framedData;

        // 移除帧头，返回剩余数据
        var headerLength = _frameHeaderField.FixedValue.Length;
        var messageLength = framedData.Length - headerIndex - headerLength;
        var message = new byte[messageLength];
        Array.Copy(framedData, headerIndex + headerLength, message, 0, messageLength);

        Logger?.LogTrace("从帧数据中移除帧头，消息长度: {Length}", message.Length);

        return message;
    }

    /// <summary>
    /// 处理接收到的数据，提取完整的消息帧
    /// </summary>
    /// <param name="data">接收到的原始数据</param>
    /// <returns>提取出的完整消息帧列表</returns>
    public List<byte[]> ProcessIncomingDataCustom(byte[] data)
    {
        var frames = new List<byte[]>();

        lock (DataBuffer)
        {
            // 将新数据添加到缓冲区
            foreach (var b in data)
            {
                DataBuffer.Enqueue(b);
            }

            // 将缓冲区数据转换为数组进行处理
            var bufferData = DataBuffer.ToArray();
            var processedBytes = 0;

            while (processedBytes < bufferData.Length)
            {
                var remainingData = new byte[bufferData.Length - processedBytes];
                Array.Copy(bufferData, processedBytes, remainingData, 0, remainingData.Length);

                // 查找帧头
                var headerIndex = FindFrameHeader(remainingData);
                if (headerIndex == -1)
                {
                    // 没有找到帧头，丢弃所有数据
                    processedBytes = bufferData.Length;
                    break;
                }

                // 跳过帧头之前的无效数据
                if (headerIndex > 0)
                {
                    processedBytes += headerIndex;
                    continue;
                }

                // 检查是否有完整的帧
                if (!HasCompleteFrame(remainingData))
                {
                    break; // 等待更多数据
                }

                // 获取帧长度
                var frameLength = GetExpectedFrameLength(remainingData);
                if (frameLength <= 0)
                {
                    // 无效帧，跳过帧头
                    processedBytes += _frameHeaderField?.FixedValue?.Length ?? 1;
                    OnFramingError(new FramingErrorEventArgs($"无效的帧长度: {frameLength}", FramingName, remainingData));
                    continue;
                }

                // 提取完整帧
                var frameData = new byte[frameLength];
                Array.Copy(remainingData, frameData, frameLength);

                frames.Add(frameData);
                processedBytes += frameLength;
                OnFrameExtracted(new FrameExtractedEventArgs(frameData, ++FrameCounter, FramingName));

                Logger?.LogTrace("提取到完整帧，长度: {Length} 字节", frameData.Length);
            }

            // 从缓冲区中移除已处理的数据
            if (processedBytes > 0)
            {
                var newBuffer = new ConcurrentQueue<byte>();
                for (int i = processedBytes; i < bufferData.Length; i++)
                {
                    newBuffer.Enqueue(bufferData[i]);
                }

                // 替换缓冲区
                while (DataBuffer.TryDequeue(out _)) { }
                while (newBuffer.TryDequeue(out var b))
                {
                    DataBuffer.Enqueue(b);
                }
            }
        }

        return frames;
    }
}
