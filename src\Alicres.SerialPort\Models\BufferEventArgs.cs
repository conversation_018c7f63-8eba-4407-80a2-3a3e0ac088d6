namespace Alicres.SerialPort.Models;

/// <summary>
/// 缓冲区警告事件参数
/// </summary>
public class BufferWarningEventArgs : EventArgs
{
    /// <summary>
    /// 当前使用率（百分比）
    /// </summary>
    public int UsagePercentage { get; }

    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int CurrentLength { get; }

    /// <summary>
    /// 最大队列长度
    /// </summary>
    public int MaxLength { get; }

    /// <summary>
    /// 警告时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="usagePercentage">使用率</param>
    /// <param name="currentLength">当前长度</param>
    /// <param name="maxLength">最大长度</param>
    public BufferWarningEventArgs(int usagePercentage, int currentLength, int maxLength)
    {
        UsagePercentage = usagePercentage;
        CurrentLength = currentLength;
        MaxLength = maxLength;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 缓冲区警告: 使用率 {UsagePercentage}% ({CurrentLength}/{MaxLength})";
    }
}

/// <summary>
/// 缓冲区溢出事件参数
/// </summary>
public class BufferOverflowEventArgs : EventArgs
{
    /// <summary>
    /// 当前队列长度
    /// </summary>
    public int CurrentLength { get; }

    /// <summary>
    /// 最大队列长度
    /// </summary>
    public int MaxLength { get; }

    /// <summary>
    /// 导致溢出的数据
    /// </summary>
    public SerialPortData OverflowData { get; }

    /// <summary>
    /// 溢出时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="currentLength">当前长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <param name="overflowData">溢出数据</param>
    public BufferOverflowEventArgs(int currentLength, int maxLength, SerialPortData overflowData)
    {
        CurrentLength = currentLength;
        MaxLength = maxLength;
        OverflowData = overflowData ?? throw new ArgumentNullException(nameof(overflowData));
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 缓冲区溢出: 队列已满 ({CurrentLength}/{MaxLength}), 数据长度: {OverflowData.Length} 字节";
    }
}

/// <summary>
/// 流控制状态变化事件参数
/// </summary>
public class FlowControlStatusChangedEventArgs : EventArgs
{
    /// <summary>
    /// 旧状态
    /// </summary>
    public FlowControlStatus OldStatus { get; }

    /// <summary>
    /// 新状态
    /// </summary>
    public FlowControlStatus NewStatus { get; }

    /// <summary>
    /// 状态变化时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="oldStatus">旧状态</param>
    /// <param name="newStatus">新状态</param>
    public FlowControlStatusChangedEventArgs(FlowControlStatus oldStatus, FlowControlStatus newStatus)
    {
        OldStatus = oldStatus;
        NewStatus = newStatus;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 流控制状态变化: {OldStatus} -> {NewStatus}";
    }
}

/// <summary>
/// 拥塞检测事件参数
/// </summary>
public class CongestionDetectedEventArgs : EventArgs
{
    /// <summary>
    /// 当前发送速率
    /// </summary>
    public double CurrentSendRate { get; }

    /// <summary>
    /// 速率限制
    /// </summary>
    public int RateLimit { get; }

    /// <summary>
    /// 拥塞级别（百分比）
    /// </summary>
    public int CongestionLevel { get; }

    /// <summary>
    /// 检测时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="currentSendRate">当前发送速率</param>
    /// <param name="rateLimit">速率限制</param>
    /// <param name="congestionLevel">拥塞级别</param>
    public CongestionDetectedEventArgs(double currentSendRate, int rateLimit, int congestionLevel)
    {
        CurrentSendRate = currentSendRate;
        RateLimit = rateLimit;
        CongestionLevel = congestionLevel;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 拥塞检测: 当前速率 {CurrentSendRate:F2}, 限制 {RateLimit}, 拥塞级别 {CongestionLevel}%";
    }
}
