# Alicres.Protocol 快速入门指南

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.Protocol.svg)](https://www.nuget.org/packages/Alicres.Protocol/)

本指南将帮助您快速上手 Alicres.Protocol 协议解析库，从安装到实现基本的协议解析功能。

## 📋 目录

- [环境要求](#环境要求)
- [安装](#安装)
- [核心概念](#核心概念)
- [快速开始](#快速开始)
- [消息帧处理](#消息帧处理)
- [数据校验](#数据校验)
- [传输层适配器](#传输层适配器)
- [下一步](#下一步)

---

## 🔧 环境要求

### 系统要求
- **.NET 8.0** 或更高版本
- **Windows 10/11** 或 **Linux** 或 **macOS**

### 开发环境
- **Visual Studio 2022** (推荐) 或 **VS Code**
- **NuGet 包管理器**

### 可选依赖
- **Alicres.SerialPort 1.1.0** - 用于串口传输
- **System.Text.Json** - JSON 序列化支持

---

## 📦 安装

### 基础安装

```bash
# 安装协议解析库
dotnet add package Alicres.Protocol --version 1.0.0

# 如果需要串口传输支持
dotnet add package Alicres.SerialPort --version 1.1.0
```

### 验证安装

```csharp
using Alicres.Protocol.Framing;
using Alicres.Protocol.Validators;

Console.WriteLine("Alicres.Protocol 安装成功！");

// 创建基本组件
var framing = new DelimiterFraming(0x0D);
var validator = new Crc16Validator();

Console.WriteLine($"分隔符帧处理器: {framing}");
Console.WriteLine($"CRC16 校验器: {validator.ValidatorName}");
```

---

## 💡 核心概念

### 协议解析框架架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   传输层适配器   │ -> │   消息帧处理器   │ -> │   数据校验器     │
│ Transport Layer │    │ Message Framing │    │ Data Validator  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
        │                       │                       │
        v                       v                       v
   串口/TCP/UDP           分隔符/固定长度           CRC/校验和
```

### 核心组件

#### 1. 消息帧处理器 (IMessageFraming)
负责从数据流中提取完整的消息帧：

```csharp
// 分隔符帧处理
var delimiterFraming = new DelimiterFraming(0x0D); // 使用 CR 作为分隔符

// 固定长度帧处理
var fixedFraming = new FixedLengthFraming(16); // 固定 16 字节长度
```

#### 2. 数据校验器 (IDataValidator)
验证数据完整性和正确性：

```csharp
// CRC16 校验
var crc16Validator = new Crc16Validator();

// 校验和验证
var checksumValidator = new ChecksumValidator();
```

#### 3. 传输层适配器 (ITransportAdapter)
提供不同传输方式的统一接口：

```csharp
// 串口传输适配器
var serialAdapter = new SerialPortTransportAdapter(serialPortService);

// TCP 传输适配器
var tcpAdapter = new TcpTransportAdapter("192.168.1.100", 502);
```

---

## 🚀 快速开始

### 示例 1: 基本协议解析

```csharp
using Alicres.Protocol.Framing;
using Alicres.Protocol.Validators;

// 创建分隔符帧处理器（使用换行符作为分隔符）
var framing = new DelimiterFraming(0x0A); // LF (Line Feed)

// 创建 CRC16 校验器
var validator = new Crc16Validator();

// 模拟接收到的数据
var receivedData = new byte[] 
{ 
    0x01, 0x03, 0x04, 0x00, 0x0A, 0x00, 0x0D, // 数据 + CRC
    0x0A, // 分隔符
    0x02, 0x03, 0x02, 0x00, 0x64, 0x39, 0x85, // 另一帧数据
    0x0A  // 分隔符
};

// 处理接收到的数据
var frames = framing.ProcessIncomingData(receivedData);

Console.WriteLine($"提取到 {frames.Count} 个完整帧:");

foreach (var frame in frames)
{
    Console.WriteLine($"帧数据: {BitConverter.ToString(frame)}");
    
    // 验证数据完整性
    if (validator.Validate(frame))
    {
        Console.WriteLine("✅ 数据校验通过");
        
        // 移除校验码获取原始数据
        var originalData = validator.RemoveChecksum(frame);
        Console.WriteLine($"原始数据: {BitConverter.ToString(originalData)}");
    }
    else
    {
        Console.WriteLine("❌ 数据校验失败");
    }
    
    Console.WriteLine();
}
```

### 示例 2: 发送数据并添加校验

```csharp
using Alicres.Protocol.Framing;
using Alicres.Protocol.Validators;

var framing = new DelimiterFraming(0x0D, 0x0A); // CR+LF 作为分隔符
var validator = new Crc16Validator();

// 要发送的原始数据
var originalData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };

Console.WriteLine($"原始数据: {BitConverter.ToString(originalData)}");

// 添加校验码
var dataWithChecksum = validator.AddChecksum(originalData);
Console.WriteLine($"添加校验后: {BitConverter.ToString(dataWithChecksum)}");

// 添加帧分隔符
var framedData = framing.FrameMessage(dataWithChecksum);
Console.WriteLine($"完整帧: {BitConverter.ToString(framedData)}");

// 模拟发送数据
Console.WriteLine("数据已准备发送");
```

### 示例 3: 异步数据处理

```csharp
using Alicres.Protocol.Framing;
using Alicres.Protocol.Validators;

var framing = new DelimiterFraming(0x0D);
var validator = new Crc16Validator();

// 异步处理数据流
async Task ProcessDataStreamAsync(byte[] dataStream)
{
    try
    {
        // 提取消息帧
        var frames = framing.ProcessIncomingData(dataStream);
        
        // 并行处理每个帧
        var tasks = frames.Select(async frame =>
        {
            // 异步验证数据
            bool isValid = await validator.ValidateAsync(frame);
            
            if (isValid)
            {
                var originalData = validator.RemoveChecksum(frame);
                await ProcessValidFrameAsync(originalData);
            }
            else
            {
                Console.WriteLine($"无效帧: {BitConverter.ToString(frame)}");
            }
        });
        
        await Task.WhenAll(tasks);
    }
    catch (Exception ex)
    {
        Console.WriteLine($"数据处理错误: {ex.Message}");
    }
}

async Task ProcessValidFrameAsync(byte[] data)
{
    // 模拟异步处理有效数据
    await Task.Delay(10);
    Console.WriteLine($"处理有效数据: {BitConverter.ToString(data)}");
}

// 使用示例
var testData = new byte[] { 0x01, 0x03, 0x04, 0x00, 0x0A, 0x00, 0x0D, 0x0D };
await ProcessDataStreamAsync(testData);
```

---

## 📦 消息帧处理

### 分隔符帧处理

```csharp
// 单字节分隔符
var singleDelimiter = new DelimiterFraming(0x0D);

// 多字节分隔符
var multiDelimiter = new DelimiterFraming(0x0D, 0x0A);

// 配置选项
var configuredFraming = new DelimiterFraming(0x0D)
{
    IncludeDelimiterInFrame = false,  // 不包含分隔符
    MaxFrameLength = 1024,            // 最大帧长度
    Enabled = true                    // 启用帧处理
};

// 处理数据
var inputData = new byte[] { 0x01, 0x02, 0x03, 0x0D, 0x04, 0x05, 0x0D };
var frames = configuredFraming.ProcessIncomingData(inputData);

foreach (var frame in frames)
{
    Console.WriteLine($"帧: {BitConverter.ToString(frame)}");
}
```

### 固定长度帧处理

```csharp
// 创建固定长度帧处理器
var fixedFraming = new FixedLengthFraming(8); // 8 字节固定长度

// 配置填充选项
fixedFraming.SetPaddingConfiguration(0x00, true, true);
// 参数: 填充字节, 允许填充, 自动去除填充

// 处理数据
var inputData = new byte[] 
{ 
    0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, // 第一帧
    0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10  // 第二帧
};

var frames = fixedFraming.ProcessIncomingData(inputData);
Console.WriteLine($"提取到 {frames.Count} 个固定长度帧");

// 发送数据时自动填充
var shortData = new byte[] { 0x01, 0x02, 0x03 };
var paddedFrame = fixedFraming.FrameMessage(shortData);
Console.WriteLine($"填充后的帧: {BitConverter.ToString(paddedFrame)}");
```

---

## ✅ 数据校验

### CRC16 校验

```csharp
var crc16 = new Crc16Validator();

// 计算 CRC16
var data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
ushort crcValue = crc16.CalculateCrc16(data);
Console.WriteLine($"CRC16 值: 0x{crcValue:X4}");

// 添加校验码
var dataWithCrc = crc16.AddChecksum(data);
Console.WriteLine($"带校验码的数据: {BitConverter.ToString(dataWithCrc)}");

// 验证数据
bool isValid = crc16.Validate(dataWithCrc);
Console.WriteLine($"数据有效性: {isValid}");

// 移除校验码
var originalData = crc16.RemoveChecksum(dataWithCrc);
Console.WriteLine($"原始数据: {BitConverter.ToString(originalData)}");
```

### 自定义校验器

```csharp
// 实现自定义校验器
public class CustomChecksumValidator : IDataValidator
{
    public string ValidatorName => "Custom Checksum";

    public byte[] CalculateChecksum(byte[] data)
    {
        byte checksum = 0;
        foreach (byte b in data)
        {
            checksum ^= b; // 简单异或校验
        }
        return new byte[] { checksum };
    }

    public byte[] AddChecksum(byte[] data)
    {
        var checksum = CalculateChecksum(data);
        var result = new byte[data.Length + checksum.Length];
        Array.Copy(data, result, data.Length);
        Array.Copy(checksum, 0, result, data.Length, checksum.Length);
        return result;
    }

    public bool Validate(byte[] data)
    {
        if (data.Length < 1) return false;
        
        var dataOnly = new byte[data.Length - 1];
        Array.Copy(data, dataOnly, dataOnly.Length);
        
        var expectedChecksum = CalculateChecksum(dataOnly);
        return data[data.Length - 1] == expectedChecksum[0];
    }

    public byte[] RemoveChecksum(byte[] data)
    {
        if (data.Length < 1) return data;
        
        var result = new byte[data.Length - 1];
        Array.Copy(data, result, result.Length);
        return result;
    }

    public Task<bool> ValidateAsync(byte[] data)
    {
        return Task.FromResult(Validate(data));
    }
}

// 使用自定义校验器
var customValidator = new CustomChecksumValidator();
var testData = new byte[] { 0x01, 0x02, 0x03 };
var dataWithChecksum = customValidator.AddChecksum(testData);
bool isValid = customValidator.Validate(dataWithChecksum);
```

---

## 🔌 传输层适配器

### 串口传输适配器

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Alicres.Protocol.Transport;

// 创建串口服务
var serialConfig = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600
};

var serialPort = new SerialPortService(serialConfig);

// 创建串口传输适配器
var serialAdapter = new SerialPortTransportAdapter(serialPort);

// 配置协议处理
var framing = new DelimiterFraming(0x0D);
var validator = new Crc16Validator();

// 订阅数据接收事件
serialAdapter.DataReceived += (sender, data) =>
{
    var frames = framing.ProcessIncomingData(data);
    foreach (var frame in frames)
    {
        if (validator.Validate(frame))
        {
            var originalData = validator.RemoveChecksum(frame);
            Console.WriteLine($"接收到有效数据: {BitConverter.ToString(originalData)}");
        }
    }
};

// 打开连接并发送数据
await serialAdapter.OpenAsync();

var sendData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x0A };
var framedData = framing.FrameMessage(validator.AddChecksum(sendData));
await serialAdapter.SendAsync(framedData);
```

### TCP 传输适配器

```csharp
using Alicres.Protocol.Transport;

// 创建 TCP 传输适配器
var tcpAdapter = new TcpTransportAdapter("192.168.1.100", 502);

// 配置协议处理
var framing = new FixedLengthFraming(8);

// 订阅连接事件
tcpAdapter.Connected += (sender, e) =>
{
    Console.WriteLine("TCP 连接已建立");
};

tcpAdapter.Disconnected += (sender, e) =>
{
    Console.WriteLine("TCP 连接已断开");
};

// 订阅数据接收
tcpAdapter.DataReceived += (sender, data) =>
{
    var frames = framing.ProcessIncomingData(data);
    foreach (var frame in frames)
    {
        Console.WriteLine($"接收到 TCP 数据: {BitConverter.ToString(frame)}");
    }
};

// 连接并发送数据
await tcpAdapter.OpenAsync();
await tcpAdapter.SendAsync(new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 });
```

---

## 🎯 下一步

恭喜！您已经掌握了 Alicres.Protocol 的基础使用方法。接下来可以：

### 📚 深入学习
- [协议类型详解](protocol-types.md) - 学习 Modbus RTU/ASCII 和自定义协议
- [集成指南](integration.md) - 与 Alicres.SerialPort 深度集成
- [示例代码集合](examples/) - 查看更多实际应用示例

### 🔗 实际应用
- 实现 Modbus 设备通讯
- 开发自定义工业协议
- 创建多传输层支持的应用

### 🛠️ 高级功能
- 协议栈组合使用
- 性能优化技巧
- 错误处理和恢复机制

---

## 📞 获取帮助

如果您在使用过程中遇到问题：

- 📖 查看 [协议类型详解](protocol-types.md)
- 🐛 提交 [Issue](https://gitee.com/liam-gitee/alicres/issues)
- 📚 查看 [API 文档](../../api/alicres-protocol.md)

---

**下一篇**: [协议类型详解](protocol-types.md) →
