using System.IO.Ports;
using System.Text;
using Microsoft.Extensions.Logging;
using Alicres.SerialPort.Constants;
using Alicres.SerialPort.Exceptions;
using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;

namespace Alicres.SerialPort.Services;

/// <summary>
/// 串口通讯服务实现
/// </summary>
public class SerialPortService : ISerialPortService
{
    private readonly ILogger<SerialPortService> _logger;
    private System.IO.Ports.SerialPort? _serialPort;
    private readonly object _lockObject = new();
    private bool _disposed = false;
    private CancellationTokenSource? _reconnectCancellationTokenSource;
    private Task? _reconnectTask;
    private AdvancedBufferManager? _bufferManager;
    private FlowControlManager? _flowControlManager;

    /// <summary>
    /// 当前串口配置
    /// </summary>
    public SerialPortConfiguration Configuration { get; private set; }

    /// <summary>
    /// 当前串口状态
    /// </summary>
    public SerialPortStatus Status { get; private set; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => _serialPort?.IsOpen == true;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<SerialPortStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 错误发生事件
    /// </summary>
    public event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 缓冲区警告事件
    /// </summary>
    public event EventHandler<BufferWarningEventArgs>? BufferWarning;

    /// <summary>
    /// 缓冲区溢出事件
    /// </summary>
    public event EventHandler<BufferOverflowEventArgs>? BufferOverflow;

    /// <summary>
    /// 流控制状态变化事件
    /// </summary>
    public event EventHandler<FlowControlStatusChangedEventArgs>? FlowControlStatusChanged;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public SerialPortService(ILogger<SerialPortService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        Configuration = new SerialPortConfiguration();
        Status = SerialPortStatus.Create(string.Empty);
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <param name="logger">日志记录器</param>
    public SerialPortService(SerialPortConfiguration configuration, ILogger<SerialPortService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        Status = SerialPortStatus.Create(configuration.PortName);

        ValidateConfiguration(configuration);
        InitializeBufferManager();
        InitializeFlowControlManager();
    }

    /// <summary>
    /// 构造函数（使用默认的空日志记录器）
    /// </summary>
    /// <param name="configuration">串口配置</param>
    public SerialPortService(SerialPortConfiguration configuration)
        : this(configuration, Microsoft.Extensions.Logging.Abstractions.NullLogger<SerialPortService>.Instance)
    {
    }

    /// <summary>
    /// 获取系统中可用的串口列表
    /// </summary>
    /// <returns>可用串口名称数组</returns>
    public string[] GetAvailablePorts()
    {
        try
        {
            return System.IO.Ports.SerialPort.GetPortNames();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取可用串口列表失败");
            return Array.Empty<string>();
        }
    }

    /// <summary>
    /// 配置串口参数
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <exception cref="SerialPortConfigurationException">配置无效时抛出</exception>
    public void Configure(SerialPortConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        
        lock (_lockObject)
        {
            if (IsConnected)
            {
                throw new SerialPortConfigurationException("无法在连接状态下修改配置", configuration.PortName);
            }

            ValidateConfiguration(configuration);
            
            var oldPortName = Configuration.PortName;
            Configuration = configuration;
            Status.PortName = configuration.PortName;

            _logger.LogInformation("串口配置已更新: {PortName} -> {NewPortName}", oldPortName, configuration.PortName);
        }
    }

    /// <summary>
    /// 打开串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功打开返回 true，否则返回 false</returns>
    /// <exception cref="SerialPortConnectionException">连接失败时抛出</exception>
    public async Task<bool> OpenAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        return await Task.Run(() =>
        {
            lock (_lockObject)
            {
                if (IsConnected)
                {
                    _logger.LogWarning("串口 {PortName} 已经打开", Configuration.PortName);
                    return true;
                }

                try
                {
                    UpdateConnectionState(SerialPortConnectionState.Connecting);

                    _serialPort = CreateSerialPort();
                    _serialPort.DataReceived += OnDataReceived;
                    _serialPort.ErrorReceived += OnErrorReceived;

                    _serialPort.Open();

                    UpdateConnectionState(SerialPortConnectionState.Connected);
                    Status.ResetReconnectAttempts();

                    _logger.LogInformation("串口 {PortName} 打开成功", Configuration.PortName);
                    return true;
                }
                catch (Exception ex)
                {
                    var errorMessage = $"打开串口 {Configuration.PortName} 失败: {ex.Message}";
                    _logger.LogError(ex, errorMessage);

                    Status.RecordError(errorMessage);
                    OnErrorOccurred(new SerialPortConnectionException(errorMessage, Configuration.PortName, ex));

                    CleanupSerialPort();
                    return false;
                }
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 关闭串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功关闭返回 true，否则返回 false</returns>
    public async Task<bool> CloseAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        // 停止重连任务
        StopReconnectTask();

        return await Task.Run(() =>
        {
            lock (_lockObject)
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("串口 {PortName} 已经关闭", Configuration.PortName);
                    return true;
                }

                try
                {
                    UpdateConnectionState(SerialPortConnectionState.Disconnecting);

                    CleanupSerialPort();

                    UpdateConnectionState(SerialPortConnectionState.Disconnected);

                    _logger.LogInformation("串口 {PortName} 关闭成功", Configuration.PortName);
                    return true;
                }
                catch (Exception ex)
                {
                    var errorMessage = $"关闭串口 {Configuration.PortName} 失败: {ex.Message}";
                    _logger.LogError(ex, errorMessage);

                    Status.RecordError(errorMessage);
                    OnErrorOccurred(new SerialPortConnectionException(errorMessage, Configuration.PortName, ex));

                    return false;
                }
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际发送的字节数</returns>
    /// <exception cref="SerialPortDataException">发送失败时抛出</exception>
    public async Task<int> SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);
        ThrowIfDisposed();

        if (!IsConnected)
        {
            throw new SerialPortDataException(SerialPortConstants.ErrorMessages.PortNotOpen, Configuration.PortName);
        }

        try
        {
            // 检查流控制状态
            if (_flowControlManager != null && _flowControlManager.IsEnabled)
            {
                if (!_flowControlManager.CanSend(data.Length))
                {
                    throw new SerialPortDataException("流控制阻止数据发送", Configuration.PortName);
                }
            }

            await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    _serialPort?.Write(data, 0, data.Length);
                }
            }, cancellationToken);

            // 记录发送统计
            Status.UpdateDataStatistics(sentBytes: data.Length);
            _flowControlManager?.RecordSend(data.Length);

            _logger.LogDebug("发送数据到串口 {PortName}: {Length} 字节", Configuration.PortName, data.Length);
            return data.Length;
        }
        catch (Exception ex)
        {
            var errorMessage = $"发送数据到串口 {Configuration.PortName} 失败: {ex.Message}";
            _logger.LogError(ex, errorMessage);

            OnErrorOccurred(new SerialPortDataException(errorMessage, Configuration.PortName, ex));
            throw;
        }
    }

    /// <summary>
    /// 发送文本数据
    /// </summary>
    /// <param name="text">要发送的文本</param>
    /// <param name="encoding">编码方式，默认为 UTF-8</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际发送的字节数</returns>
    /// <exception cref="SerialPortDataException">发送失败时抛出</exception>
    public async Task<int> SendTextAsync(string text, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(text);

        encoding ??= Encoding.UTF8;
        var data = encoding.GetBytes(text);

        return await SendAsync(data, cancellationToken);
    }

    /// <summary>
    /// 读取数据
    /// </summary>
    /// <param name="buffer">接收缓冲区</param>
    /// <param name="offset">缓冲区偏移量</param>
    /// <param name="count">要读取的字节数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际读取的字节数</returns>
    /// <exception cref="SerialPortDataException">读取失败时抛出</exception>
    public async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(buffer);
        ThrowIfDisposed();

        if (!IsConnected)
        {
            throw new SerialPortDataException(SerialPortConstants.ErrorMessages.PortNotOpen, Configuration.PortName);
        }

        try
        {
            var bytesRead = await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    return _serialPort?.Read(buffer, offset, count) ?? 0;
                }
            }, cancellationToken);

            if (bytesRead > 0)
            {
                Status.UpdateDataStatistics(receivedBytes: bytesRead);
                _flowControlManager?.RecordReceive(bytesRead);
                _logger.LogDebug("从串口 {PortName} 读取数据: {Length} 字节", Configuration.PortName, bytesRead);
            }

            return bytesRead;
        }
        catch (Exception ex)
        {
            var errorMessage = $"从串口 {Configuration.PortName} 读取数据失败: {ex.Message}";
            _logger.LogError(ex, errorMessage);

            OnErrorOccurred(new SerialPortDataException(errorMessage, Configuration.PortName, ex));
            throw;
        }
    }

    /// <summary>
    /// 读取所有可用数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的数据</returns>
    /// <exception cref="SerialPortDataException">读取失败时抛出</exception>
    public async Task<byte[]> ReadAllAvailableAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        if (!IsConnected)
        {
            throw new SerialPortDataException(SerialPortConstants.ErrorMessages.PortNotOpen, Configuration.PortName);
        }

        try
        {
            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var bytesToRead = _serialPort?.BytesToRead ?? 0;
                    if (bytesToRead == 0)
                        return Array.Empty<byte>();

                    var buffer = new byte[bytesToRead];
                    var bytesRead = _serialPort?.Read(buffer, 0, bytesToRead) ?? 0;

                    if (bytesRead < bytesToRead)
                    {
                        Array.Resize(ref buffer, bytesRead);
                    }

                    Status.UpdateDataStatistics(receivedBytes: bytesRead);
                    _flowControlManager?.RecordReceive(bytesRead);
                    return buffer;
                }
            }, cancellationToken);
        }
        catch (Exception ex)
        {
            var errorMessage = $"从串口 {Configuration.PortName} 读取所有可用数据失败: {ex.Message}";
            _logger.LogError(ex, errorMessage);

            OnErrorOccurred(new SerialPortDataException(errorMessage, Configuration.PortName, ex));
            throw;
        }
    }

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    public void ClearReceiveBuffer()
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            try
            {
                _serialPort?.DiscardInBuffer();
                _logger.LogDebug("清空串口 {PortName} 接收缓冲区", Configuration.PortName);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清空串口 {PortName} 接收缓冲区失败", Configuration.PortName);
            }
        }
    }

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    public void ClearSendBuffer()
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            try
            {
                _serialPort?.DiscardOutBuffer();
                _logger.LogDebug("清空串口 {PortName} 发送缓冲区", Configuration.PortName);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清空串口 {PortName} 发送缓冲区失败", Configuration.PortName);
            }
        }
    }

    /// <summary>
    /// 获取接收缓冲区中的字节数
    /// </summary>
    /// <returns>缓冲区中的字节数</returns>
    public int GetBytesToRead()
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            return _serialPort?.BytesToRead ?? 0;
        }
    }

    /// <summary>
    /// 获取发送缓冲区中的字节数
    /// </summary>
    /// <returns>缓冲区中的字节数</returns>
    public int GetBytesToWrite()
    {
        ThrowIfDisposed();

        lock (_lockObject)
        {
            return _serialPort?.BytesToWrite ?? 0;
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    /// <param name="disposing">是否正在释放</param>
    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            StopReconnectTask();
            CleanupSerialPort();

            // 释放缓冲管理器
            if (_bufferManager != null)
            {
                _bufferManager.BufferWarning -= OnBufferWarning;
                _bufferManager.BufferOverflow -= OnBufferOverflow;
                _bufferManager.Dispose();
                _bufferManager = null;
            }

            // 释放流控制管理器
            if (_flowControlManager != null)
            {
                _flowControlManager.StatusChanged -= OnFlowControlStatusChanged;
                _flowControlManager.Dispose();
                _flowControlManager = null;
            }

            _disposed = true;

            _logger.LogInformation("串口服务 {PortName} 已释放", Configuration.PortName);
        }
    }

    #region 私有方法

    /// <summary>
    /// 验证配置
    /// </summary>
    /// <param name="configuration">配置</param>
    /// <exception cref="SerialPortConfigurationException">配置无效时抛出</exception>
    private static void ValidateConfiguration(SerialPortConfiguration configuration)
    {
        if (!configuration.IsValid())
        {
            throw new SerialPortConfigurationException("串口配置无效", configuration.PortName);
        }
    }

    /// <summary>
    /// 创建串口实例
    /// </summary>
    /// <returns>串口实例</returns>
    private System.IO.Ports.SerialPort CreateSerialPort()
    {
        var serialPort = new System.IO.Ports.SerialPort
        {
            PortName = Configuration.PortName,
            BaudRate = Configuration.BaudRate,
            DataBits = Configuration.DataBits,
            StopBits = Configuration.StopBits,
            Parity = Configuration.Parity,
            Handshake = Configuration.Handshake,
            ReadTimeout = Configuration.ReadTimeout,
            WriteTimeout = Configuration.WriteTimeout,
            ReceivedBytesThreshold = 1,
            DtrEnable = Configuration.DtrEnable,
            RtsEnable = Configuration.RtsEnable
        };

        return serialPort;
    }

    /// <summary>
    /// 清理串口资源
    /// </summary>
    private void CleanupSerialPort()
    {
        if (_serialPort != null)
        {
            try
            {
                _serialPort.DataReceived -= OnDataReceived;
                _serialPort.ErrorReceived -= OnErrorReceived;

                if (_serialPort.IsOpen)
                {
                    _serialPort.Close();
                }

                _serialPort.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "清理串口 {PortName} 资源时发生异常", Configuration.PortName);
            }
            finally
            {
                _serialPort = null;
            }
        }
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    /// <param name="newState">新状态</param>
    private void UpdateConnectionState(SerialPortConnectionState newState)
    {
        var previousState = Status.ConnectionState;
        Status.UpdateConnectionState(newState);

        if (previousState != newState)
        {
            OnStatusChanged(new SerialPortStatusChangedEventArgs(Configuration.PortName, previousState, newState));
        }
    }

    /// <summary>
    /// 数据接收事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnDataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        try
        {
            var data = ReadAllAvailableAsync().GetAwaiter().GetResult();
            if (data.Length > 0)
            {
                var serialPortData = new SerialPortData(data, Configuration.PortName, SerialPortDataDirection.Received);

                // 如果启用了高级缓冲管理，将数据添加到队列
                if (_bufferManager != null)
                {
                    var success = _bufferManager.EnqueueData(serialPortData);
                    if (!success)
                    {
                        _logger.LogWarning("数据添加到缓冲队列失败，数据长度: {Length} 字节", data.Length);
                    }
                }

                // 处理流控制数据
                if (_flowControlManager != null && _flowControlManager.IsEnabled)
                {
                    _flowControlManager.ProcessFlowControlData(data);
                }

                // 触发数据接收事件
                OnDataReceived(new SerialPortDataReceivedEventArgs(serialPortData));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理串口 {PortName} 数据接收事件时发生异常", Configuration.PortName);
            OnErrorOccurred(new SerialPortDataException("数据接收处理失败", Configuration.PortName, ex));
        }
    }

    /// <summary>
    /// 错误接收事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnErrorReceived(object sender, SerialErrorReceivedEventArgs e)
    {
        var errorMessage = $"串口 {Configuration.PortName} 发生错误: {e.EventType}";
        _logger.LogError(errorMessage);

        Status.RecordError(errorMessage);
        OnErrorOccurred(new SerialPortException(errorMessage, Configuration.PortName));

        // 如果启用自动重连，则尝试重连
        if (Configuration.EnableAutoReconnect)
        {
            StartReconnectTask();
        }
    }

    /// <summary>
    /// 触发数据接收事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnDataReceived(SerialPortDataReceivedEventArgs e)
    {
        DataReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 触发状态变化事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnStatusChanged(SerialPortStatusChangedEventArgs e)
    {
        StatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnErrorOccurred(SerialPortErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    /// <param name="exception">异常</param>
    protected virtual void OnErrorOccurred(Exception exception)
    {
        OnErrorOccurred(new SerialPortErrorEventArgs(Configuration.PortName, exception));
    }

    /// <summary>
    /// 开始重连任务
    /// </summary>
    private void StartReconnectTask()
    {
        if (_reconnectTask?.IsCompleted != false)
        {
            StopReconnectTask();

            _reconnectCancellationTokenSource = new CancellationTokenSource();
            _reconnectTask = ReconnectAsync(_reconnectCancellationTokenSource.Token);
        }
    }

    /// <summary>
    /// 停止重连任务
    /// </summary>
    private void StopReconnectTask()
    {
        _reconnectCancellationTokenSource?.Cancel();
        _reconnectCancellationTokenSource?.Dispose();
        _reconnectCancellationTokenSource = null;
    }

    /// <summary>
    /// 重连逻辑
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    private async Task ReconnectAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested &&
               Status.ReconnectAttempts < Configuration.MaxReconnectAttempts)
        {
            try
            {
                UpdateConnectionState(SerialPortConnectionState.Reconnecting);
                Status.IncrementReconnectAttempts();

                _logger.LogInformation("尝试重连串口 {PortName}，第 {Attempt} 次",
                    Configuration.PortName, Status.ReconnectAttempts);

                await Task.Delay(Configuration.ReconnectInterval, cancellationToken);

                if (await OpenAsync(cancellationToken))
                {
                    _logger.LogInformation("串口 {PortName} 重连成功", Configuration.PortName);
                    return;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "串口 {PortName} 重连失败，第 {Attempt} 次",
                    Configuration.PortName, Status.ReconnectAttempts);
            }
        }

        if (Status.ReconnectAttempts >= Configuration.MaxReconnectAttempts)
        {
            _logger.LogError("串口 {PortName} 重连失败，已达到最大重连次数 {MaxAttempts}",
                Configuration.PortName, Configuration.MaxReconnectAttempts);

            UpdateConnectionState(SerialPortConnectionState.Error);
        }
    }

    /// <summary>
    /// 检查是否已释放
    /// </summary>
    /// <exception cref="ObjectDisposedException">已释放时抛出</exception>
    private void ThrowIfDisposed()
    {
        if (_disposed)
        {
            throw new ObjectDisposedException(nameof(SerialPortService));
        }
    }

    /// <summary>
    /// 初始化缓冲管理器
    /// </summary>
    private void InitializeBufferManager()
    {
        if (Configuration.EnableAdvancedBuffering)
        {
            _bufferManager = new AdvancedBufferManager(Configuration, _logger);
            _bufferManager.BufferWarning += OnBufferWarning;
            _bufferManager.BufferOverflow += OnBufferOverflow;

            _logger.LogDebug("高级缓冲管理器已启用，队列最大长度: {MaxLength}",
                Configuration.DataQueueMaxLength);
        }
    }

    /// <summary>
    /// 初始化流控制管理器
    /// </summary>
    private void InitializeFlowControlManager()
    {
        if (Configuration.EnableFlowControl)
        {
            _flowControlManager = new FlowControlManager(Configuration, _logger);
            _flowControlManager.FlowControlType = Configuration.FlowControlType;
            _flowControlManager.IsEnabled = true;
            _flowControlManager.SendRateLimit = Configuration.SendRateLimit;
            _flowControlManager.StatusChanged += OnFlowControlStatusChanged;

            _logger.LogDebug("流控制管理器已启用，类型: {Type}, 速率限制: {Limit}",
                Configuration.FlowControlType, Configuration.SendRateLimit);
        }
    }

    /// <summary>
    /// 处理缓冲区警告事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnBufferWarning(object? sender, BufferWarningEventArgs e)
    {
        _logger.LogWarning("缓冲区使用率警告: {Usage}% ({Current}/{Max})",
            e.UsagePercentage, e.CurrentLength, e.MaxLength);
        BufferWarning?.Invoke(this, e);
    }

    /// <summary>
    /// 处理缓冲区溢出事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnBufferOverflow(object? sender, BufferOverflowEventArgs e)
    {
        _logger.LogError("缓冲区溢出: 队列已满 ({Current}/{Max}), 数据长度: {DataLength} 字节",
            e.CurrentLength, e.MaxLength, e.OverflowData.Length);
        BufferOverflow?.Invoke(this, e);
    }

    /// <summary>
    /// 处理流控制状态变化事件
    /// </summary>
    /// <param name="sender">事件发送者</param>
    /// <param name="e">事件参数</param>
    private void OnFlowControlStatusChanged(object? sender, FlowControlStatusChangedEventArgs e)
    {
        _logger.LogDebug("流控制状态变化: {OldStatus} -> {NewStatus}", e.OldStatus, e.NewStatus);
        FlowControlStatusChanged?.Invoke(this, e);
    }

    #endregion

    #region 缓冲管理器相关方法

    /// <summary>
    /// 获取缓冲区统计信息
    /// </summary>
    /// <returns>缓冲区统计信息，如果未启用高级缓冲则返回 null</returns>
    public BufferStatistics? GetBufferStatistics()
    {
        return _bufferManager?.GetStatistics();
    }

    /// <summary>
    /// 清空数据队列
    /// </summary>
    public void ClearDataQueue()
    {
        _bufferManager?.ClearQueue();
        _logger.LogDebug("数据队列已清空");
    }

    /// <summary>
    /// 从队列中批量获取数据
    /// </summary>
    /// <param name="maxCount">最大获取数量</param>
    /// <returns>数据列表</returns>
    public List<SerialPortData> DequeueDataBatch(int maxCount = 10)
    {
        return _bufferManager?.DequeueBatch(maxCount) ?? new List<SerialPortData>();
    }

    /// <summary>
    /// 获取队列中的数据数量
    /// </summary>
    /// <returns>队列长度</returns>
    public int GetQueueLength()
    {
        return _bufferManager?.QueueLength ?? 0;
    }

    /// <summary>
    /// 获取队列使用率
    /// </summary>
    /// <returns>使用率百分比</returns>
    public int GetQueueUsagePercentage()
    {
        return _bufferManager?.QueueUsagePercentage ?? 0;
    }

    #endregion

    #region 流控制管理器相关方法

    /// <summary>
    /// 获取流控制统计信息
    /// </summary>
    /// <returns>流控制统计信息，如果未启用流控制则返回 null</returns>
    public FlowControlStatistics? GetFlowControlStatistics()
    {
        return _flowControlManager?.GetStatistics();
    }

    /// <summary>
    /// 设置发送速率限制
    /// </summary>
    /// <param name="rateLimit">速率限制（字节/秒），0 表示无限制</param>
    public void SetSendRateLimit(int rateLimit)
    {
        if (_flowControlManager != null)
        {
            _flowControlManager.SendRateLimit = rateLimit;
            _logger.LogDebug("发送速率限制已设置为: {Limit} 字节/秒", rateLimit);
        }
    }

    /// <summary>
    /// 发送 XON 字符（恢复发送）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送的字节数</returns>
    public async Task<int> SendXonAsync(CancellationToken cancellationToken = default)
    {
        if (_flowControlManager != null)
        {
            var xonData = _flowControlManager.SendXon();
            return await SendAsync(xonData, cancellationToken);
        }
        return 0;
    }

    /// <summary>
    /// 发送 XOFF 字符（暂停发送）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送的字节数</returns>
    public async Task<int> SendXoffAsync(CancellationToken cancellationToken = default)
    {
        if (_flowControlManager != null)
        {
            var xoffData = _flowControlManager.SendXoff();
            return await SendAsync(xoffData, cancellationToken);
        }
        return 0;
    }

    /// <summary>
    /// 设置 RTS 流控制状态
    /// </summary>
    /// <param name="isPaused">是否暂停</param>
    public void SetRtsFlowControl(bool isPaused)
    {
        _flowControlManager?.SetRtsFlowControl(isPaused);
    }

    /// <summary>
    /// 获取当前发送速率
    /// </summary>
    /// <returns>发送速率（字节/秒）</returns>
    public double GetCurrentSendRate()
    {
        return _flowControlManager?.GetCurrentSendRate() ?? 0;
    }

    #endregion
}
