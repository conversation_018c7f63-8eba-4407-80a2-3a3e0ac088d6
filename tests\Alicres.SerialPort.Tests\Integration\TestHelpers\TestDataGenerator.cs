using Alicres.SerialPort.Models;
using System.IO.Ports;
using System.Text;

namespace Alicres.SerialPort.Tests.Integration.TestHelpers;

/// <summary>
/// 测试数据生成器
/// </summary>
public static class TestDataGenerator
{
    private static readonly Random _random = new();

    /// <summary>
    /// 生成标准串口配置
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>串口配置</returns>
    public static SerialPortConfiguration CreateStandardConfiguration(string portName = "COM_TEST")
    {
        return new SerialPortConfiguration
        {
            PortName = portName,
            BaudRate = 9600,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.None,
            ReadTimeout = 1000,
            WriteTimeout = 1000,
            ReceiveBufferSize = 4096,
            SendBufferSize = 4096,
            EnableAutoReconnect = false,
            ReconnectInterval = 5000, // 毫秒
            MaxReconnectAttempts = 3
        };
    }

    /// <summary>
    /// 生成高级缓冲配置
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>串口配置</returns>
    public static SerialPortConfiguration CreateAdvancedBufferingConfiguration(string portName = "COM_BUFFER_TEST")
    {
        return new SerialPortConfiguration
        {
            PortName = portName,
            BaudRate = 115200,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.None,
            ReadTimeout = 2000,
            WriteTimeout = 2000,
            ReceiveBufferSize = 8192,
            SendBufferSize = 8192,
            EnableAutoReconnect = true,
            ReconnectInterval = 2000, // 毫秒
            MaxReconnectAttempts = 5,
            EnableAdvancedBuffering = true,
            DataQueueMaxLength = 1000,
            BufferWarningThreshold = 80, // 百分比
            BufferOverflowStrategy = BufferOverflowStrategy.DropOldest,
            BufferCleanupInterval = 60000 // 毫秒
        };
    }

    /// <summary>
    /// 生成流控制配置
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <returns>串口配置</returns>
    public static SerialPortConfiguration CreateFlowControlConfiguration(string portName = "COM_FLOW_TEST")
    {
        return new SerialPortConfiguration
        {
            PortName = portName,
            BaudRate = 38400,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.Even,
            ReadTimeout = 3000,
            WriteTimeout = 3000,
            ReceiveBufferSize = 2048,
            SendBufferSize = 2048,
            EnableAutoReconnect = true,
            ReconnectInterval = 3000, // 毫秒
            MaxReconnectAttempts = 10
        };
    }

    /// <summary>
    /// 生成多种配置组合
    /// </summary>
    /// <returns>配置列表</returns>
    public static List<SerialPortConfiguration> CreateConfigurationCombinations()
    {
        var configurations = new List<SerialPortConfiguration>();

        // 不同波特率组合
        var baudRates = new[] { 9600, 19200, 38400, 57600, 115200 };
        var dataBits = new[] { 7, 8 };
        var stopBits = new[] { StopBits.One, StopBits.Two };
        var parities = new[] { Parity.None, Parity.Even, Parity.Odd };

        var configIndex = 0;
        foreach (var baudRate in baudRates.Take(3)) // 限制组合数量
        {
            foreach (var dataBit in dataBits)
            {
                foreach (var stopBit in stopBits)
                {
                    foreach (var parity in parities.Take(2)) // 限制组合数量
                    {
                        configurations.Add(new SerialPortConfiguration
                        {
                            PortName = $"COM_COMBO_{configIndex++}",
                            BaudRate = baudRate,
                            DataBits = dataBit,
                            StopBits = stopBit,
                            Parity = parity,
                            ReadTimeout = 1000,
                            WriteTimeout = 1000,
                            ReceiveBufferSize = 4096,
                            SendBufferSize = 4096
                        });
                    }
                }
            }
        }

        return configurations;
    }

    /// <summary>
    /// 生成测试数据
    /// </summary>
    /// <param name="size">数据大小（字节）</param>
    /// <returns>测试数据</returns>
    public static byte[] GenerateTestData(int size)
    {
        var data = new byte[size];
        _random.NextBytes(data);
        return data;
    }

    /// <summary>
    /// 生成ASCII测试数据
    /// </summary>
    /// <param name="size">数据大小（字节）</param>
    /// <returns>ASCII测试数据</returns>
    public static byte[] GenerateAsciiTestData(int size)
    {
        var data = new byte[size];
        for (int i = 0; i < size; i++)
        {
            data[i] = (byte)_random.Next(32, 127); // 可打印ASCII字符
        }
        return data;
    }

    /// <summary>
    /// 生成测试文本
    /// </summary>
    /// <param name="length">文本长度</param>
    /// <returns>测试文本</returns>
    public static string GenerateTestText(int length)
    {
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        var result = new StringBuilder(length);
        
        for (int i = 0; i < length; i++)
        {
            result.Append(chars[_random.Next(chars.Length)]);
        }
        
        return result.ToString();
    }

    /// <summary>
    /// 生成结构化测试消息
    /// </summary>
    /// <param name="messageCount">消息数量</param>
    /// <returns>测试消息列表</returns>
    public static List<string> GenerateStructuredMessages(int messageCount)
    {
        var messages = new List<string>();
        
        for (int i = 0; i < messageCount; i++)
        {
            var messageType = _random.Next(1, 5);
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var data = GenerateTestText(10);
            
            messages.Add($"MSG{messageType:D2}|{timestamp}|{data}|{i:D4}\r\n");
        }
        
        return messages;
    }

    /// <summary>
    /// 生成大数据块
    /// </summary>
    /// <param name="blockSize">块大小（KB）</param>
    /// <returns>大数据块</returns>
    public static byte[] GenerateLargeDataBlock(int blockSize)
    {
        var totalBytes = blockSize * 1024;
        var data = new byte[totalBytes];
        
        // 生成有模式的数据，便于验证
        for (int i = 0; i < totalBytes; i++)
        {
            data[i] = (byte)(i % 256);
        }
        
        return data;
    }

    /// <summary>
    /// 生成协议帧数据
    /// </summary>
    /// <param name="frameCount">帧数量</param>
    /// <returns>协议帧列表</returns>
    public static List<byte[]> GenerateProtocolFrames(int frameCount)
    {
        var frames = new List<byte[]>();
        
        for (int i = 0; i < frameCount; i++)
        {
            var dataLength = _random.Next(10, 100);
            var frame = new List<byte>
            {
                0x02, // STX
                (byte)dataLength // 数据长度
            };
            
            // 添加数据
            var data = GenerateTestData(dataLength);
            frame.AddRange(data);
            
            // 计算校验和
            var checksum = (byte)(frame.Skip(1).Sum(b => b) & 0xFF);
            frame.Add(checksum);
            frame.Add(0x03); // ETX
            
            frames.Add(frame.ToArray());
        }
        
        return frames;
    }

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    /// <param name="original">原始数据</param>
    /// <param name="received">接收数据</param>
    /// <returns>是否完整</returns>
    public static bool ValidateDataIntegrity(byte[] original, byte[] received)
    {
        if (original.Length != received.Length)
            return false;
            
        for (int i = 0; i < original.Length; i++)
        {
            if (original[i] != received[i])
                return false;
        }
        
        return true;
    }

    /// <summary>
    /// 计算数据校验和
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>校验和</returns>
    public static byte CalculateChecksum(byte[] data)
    {
        return (byte)(data.Sum(b => b) & 0xFF);
    }

    /// <summary>
    /// 生成性能测试数据集
    /// </summary>
    /// <returns>性能测试数据集</returns>
    public static Dictionary<string, byte[]> GeneratePerformanceTestDataSet()
    {
        return new Dictionary<string, byte[]>
        {
            ["Small_1KB"] = GenerateTestData(1024),
            ["Medium_10KB"] = GenerateTestData(10 * 1024),
            ["Large_100KB"] = GenerateTestData(100 * 1024),
            ["XLarge_1MB"] = GenerateTestData(1024 * 1024),
            ["Pattern_64KB"] = GenerateLargeDataBlock(64)
        };
    }
}
