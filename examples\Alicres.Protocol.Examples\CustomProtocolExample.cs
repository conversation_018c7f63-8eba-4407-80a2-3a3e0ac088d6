using Alicres.Protocol.Protocols.Custom;
using Alicres.Protocol.Validators;
using Alicres.Protocol.Framing;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Examples;

/// <summary>
/// 自定义协议示例
/// 演示如何使用自定义协议解析器处理格式：7E F1 05 55 0A 00 00 10 F5 82
/// </summary>
public static class CustomProtocolExample
{
    /// <summary>
    /// 运行自定义协议示例
    /// </summary>
    public static async Task RunExample()
    {
        Console.WriteLine("\n=== 自定义协议解析示例 ===");
        Console.WriteLine("协议格式：帧头(1字节) + 源地址(1字节) + 目标地址(1字节) + 命令码(1字节) + 数据长度(1字节) + 数据字段(N字节) + CRC16(2字节)");
        Console.WriteLine("示例数据：7E F1 05 55 0A 00 00 10 F5 82");
        Console.WriteLine();

        // 创建日志工厂
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Debug));

        var logger = loggerFactory.CreateLogger<CustomProtocolParser>();

        try
        {
            // 1. 创建自定义协议解析器
            var protocolParser = new CustomProtocolParser(logger);
            Console.WriteLine($"✓ 创建协议解析器: {protocolParser.ProtocolName} v{protocolParser.ProtocolVersion}");

            // 2. 解析示例数据
            var exampleHex = "7E F1 05 55 0A 00 00 10 F5 82";
            Console.WriteLine($"📥 解析示例数据: {exampleHex}");

            var parsedMessage = await protocolParser.ParseFromHexStringAsync(exampleHex);
            if (parsedMessage != null)
            {
                Console.WriteLine("✓ 解析成功！");
                Console.WriteLine($"   帧头: 0x{parsedMessage.FrameHeader:X2}");
                Console.WriteLine($"   源地址: 0x{parsedMessage.SourceAddress:X2}");
                Console.WriteLine($"   目标地址: 0x{parsedMessage.TargetAddress:X2}");
                Console.WriteLine($"   命令码: 0x{parsedMessage.CommandCode:X2}");
                Console.WriteLine($"   数据长度: {parsedMessage.DataLength}");
                Console.WriteLine($"   数据字段: {(parsedMessage.DataField.Length > 0 ? BitConverter.ToString(parsedMessage.DataField) : "无")}");
                Console.WriteLine($"   CRC16: 0x{parsedMessage.Crc16:X4}");
                Console.WriteLine($"   消息详情: {parsedMessage}");
            }
            else
            {
                Console.WriteLine("❌ 解析失败");
                return;
            }

            Console.WriteLine();

            // 3. 验证CRC16校验
            var validator = new CustomCrc16Validator(loggerFactory.CreateLogger<CustomCrc16Validator>());
            var isValid = validator.ValidateHexString(exampleHex);
            Console.WriteLine($"🔍 CRC16校验结果: {(isValid ? "✓ 有效" : "❌ 无效")}");

            Console.WriteLine();

            // 4. 创建新的协议消息
            Console.WriteLine("📤 创建新的协议消息:");
            var newMessage = protocolParser.CreateMessage(
                sourceAddress: 0x01,
                targetAddress: 0x02,
                commandCode: 0x03,
                dataField: new byte[] { 0x11, 0x22, 0x33 }
            );

            Console.WriteLine($"   创建的消息: {newMessage}");

            // 5. 序列化消息
            var serializedData = await protocolParser.SerializeAsync(newMessage);
            var serializedHex = BitConverter.ToString(serializedData).Replace("-", " ");
            Console.WriteLine($"   序列化结果: {serializedHex}");

            // 6. 验证序列化后的数据
            var serializedValid = validator.Validate(serializedData);
            Console.WriteLine($"   序列化数据校验: {(serializedValid ? "✓ 有效" : "❌ 无效")}");

            Console.WriteLine();

            // 7. 测试帧处理器
            Console.WriteLine("🔧 测试自定义帧处理器:");
            var framing = new CustomHeaderFraming(0x7E, loggerFactory.CreateLogger<CustomHeaderFraming>());

            // 模拟接收到的数据流（包含多个帧）
            var dataStream = new List<byte>();
            dataStream.AddRange(serializedData); // 第一个完整帧
            dataStream.AddRange(new byte[] { 0x7E, 0x03, 0x04, 0x05, 0x08, 0x99, 0xAA, 0x12, 0x34 }); // 第二个帧
            
            var frames = framing.ProcessIncomingData(dataStream.ToArray());
            Console.WriteLine($"   从数据流中提取到 {frames.Count} 个完整帧");

            for (int i = 0; i < frames.Count; i++)
            {
                var frameHex = BitConverter.ToString(frames[i]).Replace("-", " ");
                Console.WriteLine($"   帧 {i + 1}: {frameHex}");
                
                // 尝试解析每个帧
                var frameMessage = await protocolParser.ParseAsync(frames[i]);
                if (frameMessage is CustomProtocolMessage customFrame)
                {
                    Console.WriteLine($"     解析结果: 源=0x{customFrame.SourceAddress:X2}, 目标=0x{customFrame.TargetAddress:X2}, 命令=0x{customFrame.CommandCode:X2}");
                }
            }

            Console.WriteLine();

            // 8. 性能测试
            Console.WriteLine("⚡ 性能测试:");
            await PerformanceTest(protocolParser, validator);

            Console.WriteLine("✅ 自定义协议示例完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例执行失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
    }

    /// <summary>
    /// 性能测试
    /// </summary>
    private static async Task PerformanceTest(CustomProtocolParser parser, CustomCrc16Validator validator)
    {
        const int testCount = 1000;
        var testData = "7E F1 05 55 0A 00 00 10 F5 82";

        // 解析性能测试
        var parseStopwatch = System.Diagnostics.Stopwatch.StartNew();
        for (int i = 0; i < testCount; i++)
        {
            await parser.ParseFromHexStringAsync(testData);
        }
        parseStopwatch.Stop();

        // 校验性能测试
        var validateStopwatch = System.Diagnostics.Stopwatch.StartNew();
        for (int i = 0; i < testCount; i++)
        {
            validator.ValidateHexString(testData);
        }
        validateStopwatch.Stop();

        Console.WriteLine($"   解析性能: {testCount} 次解析耗时 {parseStopwatch.ElapsedMilliseconds} ms，平均 {(double)parseStopwatch.ElapsedMilliseconds / testCount:F2} ms/次");
        Console.WriteLine($"   校验性能: {testCount} 次校验耗时 {validateStopwatch.ElapsedMilliseconds} ms，平均 {(double)validateStopwatch.ElapsedMilliseconds / testCount:F2} ms/次");
    }

    /// <summary>
    /// 演示错误处理
    /// </summary>
    public static async Task DemonstrateErrorHandling()
    {
        Console.WriteLine("\n=== 错误处理演示 ===");

        var parser = new CustomProtocolParser();
        var validator = new CustomCrc16Validator();

        // 测试无效数据
        var invalidCases = new[]
        {
            ("无效帧头", "FF F1 05 55 0A 00 00 10 F5 82"),
            ("数据长度不足", "7E F1 05"),
            ("CRC校验错误", "7E F1 05 55 0A 00 00 10 FF FF"),
            ("长度字段错误", "7E F1 05 55 FF 00 00 10 F5 82")
        };

        foreach (var (description, hexData) in invalidCases)
        {
            Console.WriteLine($"🧪 测试 {description}: {hexData}");
            
            var message = await parser.ParseFromHexStringAsync(hexData);
            var isValid = validator.ValidateHexString(hexData);
            
            Console.WriteLine($"   解析结果: {(message != null ? "成功" : "失败")}");
            Console.WriteLine($"   校验结果: {(isValid ? "有效" : "无效")}");
        }
    }
}
