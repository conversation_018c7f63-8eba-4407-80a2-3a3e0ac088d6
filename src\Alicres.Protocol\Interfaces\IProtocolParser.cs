using Alicres.Protocol.Models.EventArgs;

namespace Alicres.Protocol.Interfaces;

/// <summary>
/// 协议解析器接口，定义协议解析的核心功能
/// </summary>
public interface IProtocolParser
{
    /// <summary>
    /// 协议名称
    /// </summary>
    string ProtocolName { get; }

    /// <summary>
    /// 协议版本
    /// </summary>
    string ProtocolVersion { get; }

    /// <summary>
    /// 是否启用数据校验
    /// </summary>
    bool ValidationEnabled { get; set; }

    /// <summary>
    /// 数据校验器
    /// </summary>
    IProtocolValidator? Validator { get; set; }

    /// <summary>
    /// 解析原始数据为协议消息
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的协议消息，如果解析失败返回 null</returns>
    Task<IProtocolMessage?> ParseAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 将协议消息序列化为字节数组
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>序列化后的字节数组</returns>
    Task<byte[]> SerializeAsync(IProtocolMessage message, CancellationToken cancellationToken = default);

    /// <summary>
    /// 验证消息完整性
    /// </summary>
    /// <param name="message">待验证的消息</param>
    /// <returns>如果消息有效返回 true，否则返回 false</returns>
    bool ValidateMessage(IProtocolMessage message);

    /// <summary>
    /// 检查数据是否为完整的协议消息
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果是完整消息返回 true，否则返回 false</returns>
    bool IsCompleteMessage(byte[] data);

    /// <summary>
    /// 获取消息的预期长度
    /// </summary>
    /// <param name="data">消息数据的开始部分</param>
    /// <returns>消息的预期总长度，如果无法确定返回 -1</returns>
    int GetExpectedMessageLength(byte[] data);

    /// <summary>
    /// 消息解析完成事件
    /// </summary>
    event EventHandler<ProtocolMessageEventArgs>? MessageParsed;

    /// <summary>
    /// 消息序列化完成事件
    /// </summary>
    event EventHandler<ProtocolSerializationEventArgs>? MessageSerialized;

    /// <summary>
    /// 解析错误事件
    /// </summary>
    event EventHandler<ProtocolErrorEventArgs>? ParseError;
}
