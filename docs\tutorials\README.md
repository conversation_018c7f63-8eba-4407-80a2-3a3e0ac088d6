# Alicres 系列库使用教程

欢迎使用 Alicres 系列 C# 功能库！本教程将帮助您快速掌握 Alicres.SerialPort 和 Alicres.Protocol 的使用方法。

## 📚 教程目录

### 🔌 Alicres.SerialPort - 串口通讯库

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.SerialPort.svg)](https://www.nuget.org/packages/Alicres.SerialPort/)

**当前版本**: 1.1.0  
**功能完成度**: 95% (P1 级功能完成)  
**测试覆盖**: 260 个测试用例

#### 📖 教程文档
- [📘 快速入门指南](alicres-serialport/getting-started.md) - 安装、配置和基本使用
- [🚀 高级功能详解](alicres-serialport/advanced-features.md) - P1 级功能：缓冲管理、流控制、性能监控
- [💡 示例代码集合](alicres-serialport/examples/) - 完整的可运行示例
- [🔧 故障排除指南](alicres-serialport/troubleshooting.md) - 常见问题和解决方案

#### 🎯 适用场景
- 工业设备通讯
- 传感器数据采集
- 自动化控制系统
- 物联网设备连接
- 嵌入式系统通讯

---

### 📡 Alicres.Protocol - 协议解析库

[![NuGet Version](https://img.shields.io/nuget/v/Alicres.Protocol.svg)](https://www.nuget.org/packages/Alicres.Protocol/)

**当前版本**: 1.0.0 (正式版)  
**功能完成度**: 80% (P0 级核心功能完成)  
**测试覆盖**: 53 个测试用例

#### 📖 教程文档
- [📘 快速入门指南](alicres-protocol/getting-started.md) - 安装、基本概念和简单使用
- [🔍 协议类型详解](alicres-protocol/protocol-types.md) - Modbus RTU/ASCII 和自定义协议
- [💡 示例代码集合](alicres-protocol/examples/) - 协议解析和传输示例
- [🔗 集成指南](alicres-protocol/integration.md) - 与 Alicres.SerialPort 集成使用

#### 🎯 适用场景
- Modbus 设备通讯
- 工业协议解析
- 自定义通讯协议
- 多传输层支持
- 数据校验和完整性检查

---

## 🚀 快速开始

### 环境要求
- **.NET 8.0** 或更高版本
- **Visual Studio 2022** 或 **VS Code**
- **NuGet 包管理器**

### 安装包
```bash
# 安装串口通讯库
dotnet add package Alicres.SerialPort --version 1.1.0

# 安装协议解析库
dotnet add package Alicres.Protocol --version 1.0.0
```

### 5 分钟快速体验

#### 串口通讯示例
```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;

// 创建串口配置
var config = new SerialPortConfiguration
{
    PortName = "COM1",
    BaudRate = 9600,
    EnableAutoReconnect = true
};

// 创建串口服务
using var serialPort = new SerialPortService(config);

// 订阅数据接收事件
serialPort.DataReceived += (sender, e) =>
{
    Console.WriteLine($"接收到数据: {e.Data.ToText()}");
};

// 打开串口并发送数据
await serialPort.OpenAsync();
await serialPort.SendAsync("Hello, Serial Port!");
```

#### 协议解析示例
```csharp
using Alicres.Protocol.Framing;
using Alicres.Protocol.Validators;

// 创建分隔符帧处理器
var framing = new DelimiterFraming(0x0D); // 使用 CR 作为分隔符

// 创建 CRC16 校验器
var validator = new Crc16Validator();

// 处理接收到的数据
var receivedData = new byte[] { 0x01, 0x03, 0x04, 0x00, 0x0A, 0x00, 0x0D };
var frames = framing.ProcessIncomingData(receivedData);

foreach (var frame in frames)
{
    if (validator.Validate(frame))
    {
        Console.WriteLine($"有效帧: {BitConverter.ToString(frame)}");
    }
}
```

---

## 📖 学习路径建议

### 🔰 初学者路径
1. **阅读快速入门指南** - 了解基本概念和安装方法
2. **运行基础示例** - 体验核心功能
3. **学习配置选项** - 掌握常用配置参数
4. **实践简单项目** - 创建自己的小项目

### 🚀 进阶开发者路径
1. **深入高级功能** - 学习缓冲管理、流控制等高级特性
2. **协议集成应用** - 掌握两个库的协同使用
3. **性能优化技巧** - 了解性能监控和优化方法
4. **故障诊断技能** - 学习问题排查和解决方法

### 🏭 工业应用路径
1. **Modbus 协议应用** - 掌握工业标准协议
2. **设备集成方案** - 学习多设备管理
3. **可靠性设计** - 了解容错和恢复机制
4. **生产环境部署** - 掌握部署和维护技巧

---

## 🔗 相关资源

### 📦 NuGet 包
- [Alicres.SerialPort](https://www.nuget.org/packages/Alicres.SerialPort/) - 串口通讯库
- [Alicres.Protocol](https://www.nuget.org/packages/Alicres.Protocol/) - 协议解析库

### 🌐 项目链接
- [Gitee 仓库](https://gitee.com/liam-gitee/alicres.git) - 源代码和文档
- [Issues 反馈](https://gitee.com/liam-gitee/alicres/issues) - 问题报告和功能请求

### 📚 API 文档
- [Alicres.SerialPort API 参考](../api/alicres-serialport.md)
- [Alicres.Protocol API 参考](../api/alicres-protocol.md)

---

## 🤝 社区支持

### 💬 获取帮助
- **GitHub Issues**: 报告 Bug 和功能请求
- **文档反馈**: 改进建议和内容补充
- **示例贡献**: 分享您的使用案例

### 📝 贡献指南
- 遵循 [Alicres C# 开发规范](../../README.md#开发规范)
- 提供完整的测试用例
- 包含详细的文档说明
- 保持代码风格一致

---

**最后更新**: 2025-06-12  
**文档版本**: 1.0.0  
**适用库版本**: Alicres.SerialPort 1.1.0, Alicres.Protocol 1.0.0
