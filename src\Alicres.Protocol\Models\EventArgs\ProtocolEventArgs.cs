using Alicres.Protocol.Interfaces;

namespace Alicres.Protocol.Models.EventArgs;

/// <summary>
/// 协议消息事件参数
/// </summary>
public class ProtocolMessageEventArgs : System.EventArgs
{
    /// <summary>
    /// 协议消息
    /// </summary>
    public IProtocolMessage Message { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public string ProtocolName { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="protocolName">协议名称</param>
    public ProtocolMessageEventArgs(IProtocolMessage message, string protocolName)
    {
        Message = message ?? throw new ArgumentNullException(nameof(message));
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 协议序列化事件参数
/// </summary>
public class ProtocolSerializationEventArgs : System.EventArgs
{
    /// <summary>
    /// 原始消息
    /// </summary>
    public IProtocolMessage Message { get; }

    /// <summary>
    /// 序列化后的数据
    /// </summary>
    public byte[] SerializedData { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public string ProtocolName { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <param name="serializedData">序列化后的数据</param>
    /// <param name="protocolName">协议名称</param>
    public ProtocolSerializationEventArgs(IProtocolMessage message, byte[] serializedData, string protocolName)
    {
        Message = message ?? throw new ArgumentNullException(nameof(message));
        SerializedData = serializedData ?? throw new ArgumentNullException(nameof(serializedData));
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 协议错误事件参数
/// </summary>
public class ProtocolErrorEventArgs : System.EventArgs
{
    /// <summary>
    /// 错误异常
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public string ProtocolName { get; }

    /// <summary>
    /// 错误相关的原始数据
    /// </summary>
    public byte[]? RawData { get; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="exception">错误异常</param>
    /// <param name="protocolName">协议名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public ProtocolErrorEventArgs(Exception exception, string protocolName, byte[]? rawData = null)
    {
        Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        RawData = rawData;
        ErrorMessage = exception.Message;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorMessage">错误描述</param>
    /// <param name="protocolName">协议名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public ProtocolErrorEventArgs(string errorMessage, string protocolName, byte[]? rawData = null)
    {
        Exception = new InvalidOperationException(errorMessage);
        ProtocolName = protocolName ?? throw new ArgumentNullException(nameof(protocolName));
        RawData = rawData;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 帧提取事件参数
/// </summary>
public class FrameExtractedEventArgs : System.EventArgs
{
    /// <summary>
    /// 提取出的帧数据
    /// </summary>
    public byte[] FrameData { get; }

    /// <summary>
    /// 帧序号
    /// </summary>
    public int FrameNumber { get; }

    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public string FramingName { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="frameData">帧数据</param>
    /// <param name="frameNumber">帧序号</param>
    /// <param name="framingName">帧处理器名称</param>
    public FrameExtractedEventArgs(byte[] frameData, int frameNumber, string framingName)
    {
        FrameData = frameData ?? throw new ArgumentNullException(nameof(frameData));
        FrameNumber = frameNumber;
        FramingName = framingName ?? throw new ArgumentNullException(nameof(framingName));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 帧处理错误事件参数
/// </summary>
public class FramingErrorEventArgs : System.EventArgs
{
    /// <summary>
    /// 错误异常
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public string FramingName { get; }

    /// <summary>
    /// 错误相关的原始数据
    /// </summary>
    public byte[]? RawData { get; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="exception">错误异常</param>
    /// <param name="framingName">帧处理器名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public FramingErrorEventArgs(Exception exception, string framingName, byte[]? rawData = null)
    {
        Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        FramingName = framingName ?? throw new ArgumentNullException(nameof(framingName));
        RawData = rawData;
        ErrorMessage = exception.Message;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorMessage">错误描述</param>
    /// <param name="framingName">帧处理器名称</param>
    /// <param name="rawData">错误相关的原始数据</param>
    public FramingErrorEventArgs(string errorMessage, string framingName, byte[]? rawData = null)
    {
        Exception = new InvalidOperationException(errorMessage);
        FramingName = framingName ?? throw new ArgumentNullException(nameof(framingName));
        RawData = rawData;
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 性能指标更新事件参数
/// </summary>
public class PerformanceMetricsUpdatedEventArgs : System.EventArgs
{
    /// <summary>
    /// 性能指标
    /// </summary>
    public Performance.PerformanceMetrics Metrics { get; }

    /// <summary>
    /// 更新时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="metrics">性能指标</param>
    public PerformanceMetricsUpdatedEventArgs(Performance.PerformanceMetrics metrics)
    {
        Metrics = metrics ?? throw new ArgumentNullException(nameof(metrics));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 性能警告事件参数
/// </summary>
public class PerformanceWarningEventArgs : System.EventArgs
{
    /// <summary>
    /// 警告类型
    /// </summary>
    public string WarningType { get; }

    /// <summary>
    /// 警告消息
    /// </summary>
    public string WarningMessage { get; }

    /// <summary>
    /// 当前指标值
    /// </summary>
    public double CurrentValue { get; }

    /// <summary>
    /// 阈值
    /// </summary>
    public double Threshold { get; }

    /// <summary>
    /// 警告时间
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="warningType">警告类型</param>
    /// <param name="warningMessage">警告消息</param>
    /// <param name="currentValue">当前值</param>
    /// <param name="threshold">阈值</param>
    public PerformanceWarningEventArgs(string warningType, string warningMessage, double currentValue, double threshold)
    {
        WarningType = warningType ?? throw new ArgumentNullException(nameof(warningType));
        WarningMessage = warningMessage ?? throw new ArgumentNullException(nameof(warningMessage));
        CurrentValue = currentValue;
        Threshold = threshold;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 获取字符串表示
    /// </summary>
    /// <returns>警告信息的字符串描述</returns>
    public override string ToString()
    {
        return $"[{Timestamp:HH:mm:ss.fff}] 性能警告 ({WarningType}): {WarningMessage} (当前值: {CurrentValue:F2}, 阈值: {Threshold:F2})";
    }
}
