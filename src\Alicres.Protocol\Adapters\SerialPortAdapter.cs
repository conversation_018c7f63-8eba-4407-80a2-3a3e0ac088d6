using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models.EventArgs;
using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Adapters;

/// <summary>
/// 串口传输适配器，连接 Alicres.SerialPort 和 Alicres.Protocol
/// </summary>
public class SerialPortAdapter : ITransportAdapter
{
    private readonly ISerialPortService _serialPortService;
    private readonly ILogger? _logger;
    private bool _disposed;

    /// <summary>
    /// 传输类型名称
    /// </summary>
    public string TransportType => "SerialPort";

    /// <summary>
    /// 连接状态
    /// </summary>
    public bool IsConnected => _serialPortService.IsConnected;

    /// <summary>
    /// 传输配置信息
    /// </summary>
    public object? Configuration => _serialPortService.Configuration;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<TransportDataEventArgs>? DataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<TransportStatusEventArgs>? StatusChanged;

    /// <summary>
    /// 传输错误事件
    /// </summary>
    public event EventHandler<TransportErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="serialPortService">串口服务实例</param>
    /// <param name="logger">日志记录器</param>
    public SerialPortAdapter(ISerialPortService serialPortService, ILogger<SerialPortAdapter>? logger = null)
    {
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));
        _logger = logger;

        // 订阅串口服务事件
        _serialPortService.DataReceived += OnSerialPortDataReceived;
        _serialPortService.StatusChanged += OnSerialPortStatusChanged;
        _serialPortService.ErrorOccurred += OnSerialPortErrorOccurred;

        _logger?.LogDebug("串口适配器已创建，端口: {PortName}", _serialPortService.Configuration.PortName);
    }

    /// <summary>
    /// 打开连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功打开返回 true，否则返回 false</returns>
    public async Task<bool> OpenAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        try
        {
            _logger?.LogDebug("正在打开串口连接: {PortName}", _serialPortService.Configuration.PortName);
            var result = await _serialPortService.OpenAsync(cancellationToken);
            
            if (result)
            {
                _logger?.LogInformation("串口连接已打开: {PortName}", _serialPortService.Configuration.PortName);
            }
            else
            {
                _logger?.LogWarning("串口连接打开失败: {PortName}", _serialPortService.Configuration.PortName);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "打开串口连接时发生错误: {PortName}", _serialPortService.Configuration.PortName);
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType, true));
            return false;
        }
    }

    /// <summary>
    /// 关闭连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功关闭返回 true，否则返回 false</returns>
    public async Task<bool> CloseAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        try
        {
            _logger?.LogDebug("正在关闭串口连接: {PortName}", _serialPortService.Configuration.PortName);
            var result = await _serialPortService.CloseAsync(cancellationToken);
            
            if (result)
            {
                _logger?.LogInformation("串口连接已关闭: {PortName}", _serialPortService.Configuration.PortName);
            }
            else
            {
                _logger?.LogWarning("串口连接关闭失败: {PortName}", _serialPortService.Configuration.PortName);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "关闭串口连接时发生错误: {PortName}", _serialPortService.Configuration.PortName);
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType));
            return false;
        }
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">待发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功发送返回 true，否则返回 false</returns>
    public async Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(data);

        try
        {
            _logger?.LogDebug("发送数据到串口 {PortName}，长度: {Length} 字节", 
                _serialPortService.Configuration.PortName, data.Length);

            var bytesSent = await _serialPortService.SendAsync(data, cancellationToken);
            var result = bytesSent == data.Length;
            
            if (result)
            {
                _logger?.LogDebug("数据发送成功，长度: {Length} 字节", data.Length);
            }
            else
            {
                _logger?.LogWarning("数据发送失败，长度: {Length} 字节", data.Length);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "发送数据到串口时发生错误: {PortName}", _serialPortService.Configuration.PortName);
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType));
            return false;
        }
    }

    /// <summary>
    /// 接收数据
    /// </summary>
    /// <param name="buffer">接收缓冲区</param>
    /// <param name="offset">缓冲区偏移量</param>
    /// <param name="count">要接收的字节数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际接收的字节数</returns>
    public async Task<int> ReceiveAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();
        ArgumentNullException.ThrowIfNull(buffer);

        try
        {
            var bytesRead = await _serialPortService.ReadAsync(buffer, offset, count, cancellationToken);
            
            if (bytesRead > 0)
            {
                _logger?.LogDebug("从串口 {PortName} 接收数据，长度: {Length} 字节",
                    _serialPortService.Configuration.PortName, bytesRead);
            }

            return bytesRead;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "从串口接收数据时发生错误: {PortName}", _serialPortService.Configuration.PortName);
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType));
            return 0;
        }
    }

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    public void ClearReceiveBuffer()
    {
        ThrowIfDisposed();

        try
        {
            // 串口服务可能没有直接的清空缓冲区方法，这里可以根据实际情况实现
            _logger?.LogDebug("清空串口 {PortName} 接收缓冲区", _serialPortService.Configuration.PortName);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "清空串口接收缓冲区时发生错误: {PortName}", _serialPortService.Configuration.PortName);
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType));
        }
    }

    /// <summary>
    /// 获取接收缓冲区中的可用字节数
    /// </summary>
    /// <returns>可用字节数</returns>
    public int GetAvailableBytes()
    {
        ThrowIfDisposed();

        try
        {
            // 这里需要根据串口服务的实际实现来获取可用字节数
            // 目前返回0，表示需要通过事件方式接收数据
            return 0;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "获取串口可用字节数时发生错误: {PortName}", _serialPortService.Configuration.PortName);
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType));
            return 0;
        }
    }

    /// <summary>
    /// 处理串口数据接收事件
    /// </summary>
    private void OnSerialPortDataReceived(object? sender, SerialPortDataReceivedEventArgs e)
    {
        try
        {
            _logger?.LogDebug("串口适配器接收到数据，长度: {Length} 字节", e.Data.Length);
            OnDataReceived(new TransportDataEventArgs(e.Data.RawData, TransportType));
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理串口数据接收事件时发生错误");
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType));
        }
    }

    /// <summary>
    /// 处理串口状态变化事件
    /// </summary>
    private void OnSerialPortStatusChanged(object? sender, SerialPortStatusChangedEventArgs e)
    {
        try
        {
            var previousConnected = e.PreviousState == SerialPortConnectionState.Connected;
            var currentConnected = e.CurrentState == SerialPortConnectionState.Connected;
            
            _logger?.LogDebug("串口状态变化: {Previous} -> {Current}", e.PreviousState, e.CurrentState);
            OnStatusChanged(new TransportStatusEventArgs(previousConnected, currentConnected, TransportType, e.CurrentState.ToString()));
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理串口状态变化事件时发生错误");
            OnErrorOccurred(new TransportErrorEventArgs(ex, TransportType));
        }
    }

    /// <summary>
    /// 处理串口错误事件
    /// </summary>
    private void OnSerialPortErrorOccurred(object? sender, SerialPortErrorEventArgs e)
    {
        try
        {
            _logger?.LogError(e.Exception, "串口发生错误: {Message}", e.Exception.Message);
            OnErrorOccurred(new TransportErrorEventArgs(e.Exception, TransportType));
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "处理串口错误事件时发生错误");
        }
    }

    /// <summary>
    /// 触发数据接收事件
    /// </summary>
    protected virtual void OnDataReceived(TransportDataEventArgs e)
    {
        DataReceived?.Invoke(this, e);
    }

    /// <summary>
    /// 触发状态变化事件
    /// </summary>
    protected virtual void OnStatusChanged(TransportStatusEventArgs e)
    {
        StatusChanged?.Invoke(this, e);
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    protected virtual void OnErrorOccurred(TransportErrorEventArgs e)
    {
        ErrorOccurred?.Invoke(this, e);
    }

    /// <summary>
    /// 检查是否已释放
    /// </summary>
    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(SerialPortAdapter));
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            // 取消订阅事件
            _serialPortService.DataReceived -= OnSerialPortDataReceived;
            _serialPortService.StatusChanged -= OnSerialPortStatusChanged;
            _serialPortService.ErrorOccurred -= OnSerialPortErrorOccurred;

            _logger?.LogDebug("串口适配器已释放: {PortName}", _serialPortService.Configuration.PortName);
            _disposed = true;
        }
    }
}
