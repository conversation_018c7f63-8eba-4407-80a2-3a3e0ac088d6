using System.Text;

namespace Alicres.Protocol.Configuration;

/// <summary>
/// 协议字段类型枚举
/// </summary>
public enum ProtocolFieldType
{
    /// <summary>
    /// 固定值字段（如帧头）
    /// </summary>
    FixedValue,
    
    /// <summary>
    /// 单字节字段
    /// </summary>
    Byte,
    
    /// <summary>
    /// 双字节字段
    /// </summary>
    UInt16,
    
    /// <summary>
    /// 四字节字段
    /// </summary>
    UInt32,
    
    /// <summary>
    /// 变长数据字段
    /// </summary>
    VariableData,
    
    /// <summary>
    /// 长度字段
    /// </summary>
    Length,
    
    /// <summary>
    /// 校验字段
    /// </summary>
    Checksum,
    
    /// <summary>
    /// 自定义字段
    /// </summary>
    Custom
}

/// <summary>
/// 字节序枚举
/// </summary>
public enum ByteOrder
{
    /// <summary>
    /// 大端字节序（网络字节序）
    /// </summary>
    BigEndian,
    
    /// <summary>
    /// 小端字节序
    /// </summary>
    LittleEndian
}

/// <summary>
/// 长度字段含义枚举
/// </summary>
public enum LengthFieldMeaning
{
    /// <summary>
    /// 表示整个帧的长度
    /// </summary>
    TotalFrameLength,
    
    /// <summary>
    /// 表示数据部分的长度
    /// </summary>
    DataLength,
    
    /// <summary>
    /// 表示剩余字节数
    /// </summary>
    RemainingBytes
}

/// <summary>
/// 协议字段描述符
/// </summary>
public class ProtocolFieldDescriptor
{
    /// <summary>
    /// 字段名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 字段类型
    /// </summary>
    public ProtocolFieldType Type { get; set; }

    /// <summary>
    /// 字段长度（字节数），-1表示变长
    /// </summary>
    public int Length { get; set; }

    /// <summary>
    /// 字段在帧中的偏移量，-1表示动态计算
    /// </summary>
    public int Offset { get; set; } = -1;

    /// <summary>
    /// 字节序（仅对多字节字段有效）
    /// </summary>
    public ByteOrder ByteOrder { get; set; } = ByteOrder.LittleEndian;

    /// <summary>
    /// 固定值（仅对FixedValue类型有效）
    /// </summary>
    public byte[]? FixedValue { get; set; }

    /// <summary>
    /// 长度字段含义（仅对Length类型有效）
    /// </summary>
    public LengthFieldMeaning LengthMeaning { get; set; } = LengthFieldMeaning.TotalFrameLength;

    /// <summary>
    /// 是否为必需字段
    /// </summary>
    public bool IsRequired { get; set; } = true;

    /// <summary>
    /// 字段描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 自定义验证函数
    /// </summary>
    public Func<byte[], bool>? CustomValidator { get; set; }

    /// <summary>
    /// 自定义序列化函数
    /// </summary>
    public Func<object, byte[]>? CustomSerializer { get; set; }

    /// <summary>
    /// 自定义反序列化函数
    /// </summary>
    public Func<byte[], object>? CustomDeserializer { get; set; }

    /// <summary>
    /// 创建固定值字段描述符
    /// </summary>
    /// <param name="name">字段名称</param>
    /// <param name="fixedValue">固定值</param>
    /// <param name="description">字段描述</param>
    /// <returns>字段描述符</returns>
    public static ProtocolFieldDescriptor CreateFixedValue(string name, byte[] fixedValue, string description = "")
    {
        return new ProtocolFieldDescriptor
        {
            Name = name,
            Type = ProtocolFieldType.FixedValue,
            Length = fixedValue.Length,
            FixedValue = fixedValue,
            Description = description
        };
    }

    /// <summary>
    /// 创建单字节字段描述符
    /// </summary>
    /// <param name="name">字段名称</param>
    /// <param name="description">字段描述</param>
    /// <returns>字段描述符</returns>
    public static ProtocolFieldDescriptor CreateByte(string name, string description = "")
    {
        return new ProtocolFieldDescriptor
        {
            Name = name,
            Type = ProtocolFieldType.Byte,
            Length = 1,
            Description = description
        };
    }

    /// <summary>
    /// 创建双字节字段描述符
    /// </summary>
    /// <param name="name">字段名称</param>
    /// <param name="byteOrder">字节序</param>
    /// <param name="description">字段描述</param>
    /// <returns>字段描述符</returns>
    public static ProtocolFieldDescriptor CreateUInt16(string name, ByteOrder byteOrder = ByteOrder.LittleEndian, string description = "")
    {
        return new ProtocolFieldDescriptor
        {
            Name = name,
            Type = ProtocolFieldType.UInt16,
            Length = 2,
            ByteOrder = byteOrder,
            Description = description
        };
    }

    /// <summary>
    /// 创建长度字段描述符
    /// </summary>
    /// <param name="name">字段名称</param>
    /// <param name="length">字段长度</param>
    /// <param name="meaning">长度含义</param>
    /// <param name="byteOrder">字节序</param>
    /// <param name="description">字段描述</param>
    /// <returns>字段描述符</returns>
    public static ProtocolFieldDescriptor CreateLength(string name, int length, LengthFieldMeaning meaning = LengthFieldMeaning.TotalFrameLength, ByteOrder byteOrder = ByteOrder.LittleEndian, string description = "")
    {
        return new ProtocolFieldDescriptor
        {
            Name = name,
            Type = ProtocolFieldType.Length,
            Length = length,
            LengthMeaning = meaning,
            ByteOrder = byteOrder,
            Description = description
        };
    }

    /// <summary>
    /// 创建变长数据字段描述符
    /// </summary>
    /// <param name="name">字段名称</param>
    /// <param name="description">字段描述</param>
    /// <returns>字段描述符</returns>
    public static ProtocolFieldDescriptor CreateVariableData(string name, string description = "")
    {
        return new ProtocolFieldDescriptor
        {
            Name = name,
            Type = ProtocolFieldType.VariableData,
            Length = -1,
            Description = description
        };
    }

    /// <summary>
    /// 创建校验字段描述符
    /// </summary>
    /// <param name="name">字段名称</param>
    /// <param name="length">校验字段长度</param>
    /// <param name="byteOrder">字节序</param>
    /// <param name="description">字段描述</param>
    /// <returns>字段描述符</returns>
    public static ProtocolFieldDescriptor CreateChecksum(string name, int length, ByteOrder byteOrder = ByteOrder.LittleEndian, string description = "")
    {
        return new ProtocolFieldDescriptor
        {
            Name = name,
            Type = ProtocolFieldType.Checksum,
            Length = length,
            ByteOrder = byteOrder,
            Description = description
        };
    }

    /// <summary>
    /// 创建自定义字段描述符
    /// </summary>
    /// <param name="name">字段名称</param>
    /// <param name="length">字段长度</param>
    /// <param name="serializer">序列化函数</param>
    /// <param name="deserializer">反序列化函数</param>
    /// <param name="description">字段描述</param>
    /// <returns>字段描述符</returns>
    public static ProtocolFieldDescriptor CreateCustom(string name, int length, Func<object, byte[]> serializer, Func<byte[], object> deserializer, string description = "")
    {
        return new ProtocolFieldDescriptor
        {
            Name = name,
            Type = ProtocolFieldType.Custom,
            Length = length,
            CustomSerializer = serializer,
            CustomDeserializer = deserializer,
            Description = description
        };
    }

    /// <summary>
    /// 获取字段描述符的字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        var sb = new StringBuilder();
        sb.Append($"{Name}({Type}");
        
        if (Length > 0)
            sb.Append($", {Length}字节");
        else if (Length == -1)
            sb.Append(", 变长");
            
        if (Type == ProtocolFieldType.FixedValue && FixedValue != null)
            sb.Append($", 固定值: {BitConverter.ToString(FixedValue)}");
            
        if (!string.IsNullOrEmpty(Description))
            sb.Append($", {Description}");
            
        sb.Append(")");
        return sb.ToString();
    }
}
