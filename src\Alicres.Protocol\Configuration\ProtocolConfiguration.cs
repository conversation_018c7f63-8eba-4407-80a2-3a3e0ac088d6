using Alicres.Protocol.Interfaces;

namespace Alicres.Protocol.Configuration;

/// <summary>
/// 协议配置类，定义协议的完整结构
/// </summary>
public class ProtocolConfiguration
{
    /// <summary>
    /// 协议名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 协议版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 协议描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 协议字段列表（按顺序）
    /// </summary>
    public List<ProtocolFieldDescriptor> Fields { get; set; } = new();

    /// <summary>
    /// 帧头字段名称
    /// </summary>
    public string? FrameHeaderFieldName { get; set; }

    /// <summary>
    /// 长度字段名称
    /// </summary>
    public string? LengthFieldName { get; set; }

    /// <summary>
    /// 校验字段名称
    /// </summary>
    public string? ChecksumFieldName { get; set; }

    /// <summary>
    /// 数据字段名称
    /// </summary>
    public string? DataFieldName { get; set; }

    /// <summary>
    /// 最小帧长度
    /// </summary>
    public int MinFrameLength { get; set; } = 1;

    /// <summary>
    /// 最大帧长度
    /// </summary>
    public int MaxFrameLength { get; set; } = 1024;

    /// <summary>
    /// 校验器类型
    /// </summary>
    public Type? ValidatorType { get; set; }

    /// <summary>
    /// 校验范围配置
    /// </summary>
    public ChecksumRangeConfiguration? ChecksumRange { get; set; }

    /// <summary>
    /// 添加字段
    /// </summary>
    /// <param name="field">字段描述符</param>
    /// <returns>当前配置实例</returns>
    public ProtocolConfiguration AddField(ProtocolFieldDescriptor field)
    {
        Fields.Add(field);
        return this;
    }

    /// <summary>
    /// 设置帧头字段
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <returns>当前配置实例</returns>
    public ProtocolConfiguration SetFrameHeader(string fieldName)
    {
        FrameHeaderFieldName = fieldName;
        return this;
    }

    /// <summary>
    /// 设置长度字段
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <returns>当前配置实例</returns>
    public ProtocolConfiguration SetLengthField(string fieldName)
    {
        LengthFieldName = fieldName;
        return this;
    }

    /// <summary>
    /// 设置校验字段
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <returns>当前配置实例</returns>
    public ProtocolConfiguration SetChecksumField(string fieldName)
    {
        ChecksumFieldName = fieldName;
        return this;
    }

    /// <summary>
    /// 设置数据字段
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <returns>当前配置实例</returns>
    public ProtocolConfiguration SetDataField(string fieldName)
    {
        DataFieldName = fieldName;
        return this;
    }

    /// <summary>
    /// 设置帧长度范围
    /// </summary>
    /// <param name="minLength">最小长度</param>
    /// <param name="maxLength">最大长度</param>
    /// <returns>当前配置实例</returns>
    public ProtocolConfiguration SetFrameLengthRange(int minLength, int maxLength)
    {
        MinFrameLength = minLength;
        MaxFrameLength = maxLength;
        return this;
    }

    /// <summary>
    /// 设置校验器
    /// </summary>
    /// <param name="checksumRange">校验范围配置</param>
    /// <returns>当前配置实例</returns>
    public ProtocolConfiguration SetValidator<T>(ChecksumRangeConfiguration? checksumRange = null) where T : IProtocolValidator
    {
        ValidatorType = typeof(T);
        ChecksumRange = checksumRange;
        return this;
    }

    /// <summary>
    /// 获取指定名称的字段描述符
    /// </summary>
    /// <param name="fieldName">字段名称</param>
    /// <returns>字段描述符，如果未找到返回null</returns>
    public ProtocolFieldDescriptor? GetField(string fieldName)
    {
        return Fields.FirstOrDefault(f => f.Name.Equals(fieldName, StringComparison.OrdinalIgnoreCase));
    }

    /// <summary>
    /// 获取帧头字段描述符
    /// </summary>
    /// <returns>帧头字段描述符</returns>
    public ProtocolFieldDescriptor? GetFrameHeaderField()
    {
        return string.IsNullOrEmpty(FrameHeaderFieldName) ? null : GetField(FrameHeaderFieldName);
    }

    /// <summary>
    /// 获取长度字段描述符
    /// </summary>
    /// <returns>长度字段描述符</returns>
    public ProtocolFieldDescriptor? GetLengthField()
    {
        return string.IsNullOrEmpty(LengthFieldName) ? null : GetField(LengthFieldName);
    }

    /// <summary>
    /// 获取校验字段描述符
    /// </summary>
    /// <returns>校验字段描述符</returns>
    public ProtocolFieldDescriptor? GetChecksumField()
    {
        return string.IsNullOrEmpty(ChecksumFieldName) ? null : GetField(ChecksumFieldName);
    }

    /// <summary>
    /// 获取数据字段描述符
    /// </summary>
    /// <returns>数据字段描述符</returns>
    public ProtocolFieldDescriptor? GetDataField()
    {
        return string.IsNullOrEmpty(DataFieldName) ? null : GetField(DataFieldName);
    }

    /// <summary>
    /// 验证配置的有效性
    /// </summary>
    /// <returns>验证结果</returns>
    public (bool IsValid, List<string> Errors) Validate()
    {
        var errors = new List<string>();

        if (string.IsNullOrEmpty(Name))
            errors.Add("协议名称不能为空");

        if (Fields.Count == 0)
            errors.Add("协议必须至少包含一个字段");

        // 检查字段名称唯一性
        var duplicateNames = Fields.GroupBy(f => f.Name)
            .Where(g => g.Count() > 1)
            .Select(g => g.Key);

        foreach (var name in duplicateNames)
            errors.Add($"字段名称重复: {name}");

        // 检查帧头字段
        if (!string.IsNullOrEmpty(FrameHeaderFieldName) && GetFrameHeaderField() == null)
            errors.Add($"指定的帧头字段不存在: {FrameHeaderFieldName}");

        // 检查长度字段
        if (!string.IsNullOrEmpty(LengthFieldName) && GetLengthField() == null)
            errors.Add($"指定的长度字段不存在: {LengthFieldName}");

        // 检查校验字段
        if (!string.IsNullOrEmpty(ChecksumFieldName) && GetChecksumField() == null)
            errors.Add($"指定的校验字段不存在: {ChecksumFieldName}");

        // 检查数据字段
        if (!string.IsNullOrEmpty(DataFieldName) && GetDataField() == null)
            errors.Add($"指定的数据字段不存在: {DataFieldName}");

        // 检查长度范围
        if (MinFrameLength <= 0)
            errors.Add("最小帧长度必须大于0");

        if (MaxFrameLength <= MinFrameLength)
            errors.Add("最大帧长度必须大于最小帧长度");

        return (errors.Count == 0, errors);
    }

    /// <summary>
    /// 计算固定字段的总长度
    /// </summary>
    /// <returns>固定字段总长度</returns>
    public int GetFixedFieldsLength()
    {
        return Fields.Where(f => f.Length > 0).Sum(f => f.Length);
    }

    /// <summary>
    /// 获取配置的字符串表示
    /// </summary>
    /// <returns>字符串表示</returns>
    public override string ToString()
    {
        return $"{Name} v{Version} ({Fields.Count} 字段)";
    }
}

/// <summary>
/// 校验范围配置
/// </summary>
public class ChecksumRangeConfiguration
{
    /// <summary>
    /// 排除的字段名称列表
    /// </summary>
    public List<string> ExcludedFields { get; set; } = new();

    /// <summary>
    /// 起始偏移量（相对于帧开始）
    /// </summary>
    public int StartOffset { get; set; } = 0;

    /// <summary>
    /// 结束偏移量（相对于帧结束，负数表示从末尾开始）
    /// </summary>
    public int EndOffset { get; set; } = 0;

    /// <summary>
    /// 是否排除帧头
    /// </summary>
    public bool ExcludeFrameHeader { get; set; } = false;

    /// <summary>
    /// 是否排除校验字段本身
    /// </summary>
    public bool ExcludeChecksumField { get; set; } = true;

    /// <summary>
    /// 创建排除帧头的校验范围配置
    /// </summary>
    /// <returns>校验范围配置</returns>
    public static ChecksumRangeConfiguration ExcludeFrameHeaderAndChecksum()
    {
        return new ChecksumRangeConfiguration
        {
            ExcludeFrameHeader = true,
            ExcludeChecksumField = true
        };
    }

    /// <summary>
    /// 创建排除指定字段的校验范围配置
    /// </summary>
    /// <param name="excludedFields">要排除的字段名称</param>
    /// <returns>校验范围配置</returns>
    public static ChecksumRangeConfiguration ExcludeFields(params string[] excludedFields)
    {
        return new ChecksumRangeConfiguration
        {
            ExcludedFields = excludedFields.ToList(),
            ExcludeChecksumField = true
        };
    }
}
