using Alicres.Protocol.Validators;
using FluentAssertions;
using Xunit;

namespace Alicres.Protocol.Tests.Validators;

/// <summary>
/// CRC16 校验器测试
/// </summary>
public class Crc16ValidatorTests
{
    private readonly Crc16Validator _validator;

    public Crc16ValidatorTests()
    {
        _validator = new Crc16Validator();
    }

    [Fact]
    public void ValidatorName_ShouldReturnCorrectName()
    {
        // Act
        var name = _validator.ValidatorName;

        // Assert
        name.Should().Be("CRC16-MODBUS");
    }

    [Fact]
    public void CalculateCrc16_WithKnownModbusData_ShouldReturnExpectedCrc()
    {
        // 使用已知的 Modbus 数据进行测试
        var data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };

        // Act
        var crc = Crc16Validator.CalculateCrc16(data);

        // Assert - 验证 CRC 计算是否一致
        crc.Should().NotBe(0xFFFF); // 不应该是初始值

        // 验证往返一致性
        var crcBytes = BitConverter.GetBytes(crc);
        var dataWithCrc = new byte[data.Length + 2];
        Array.Copy(data, dataWithCrc, data.Length);
        Array.Copy(crcBytes, 0, dataWithCrc, data.Length, 2);

        var validator = new Crc16Validator();
        validator.Validate(dataWithCrc).Should().BeTrue();
    }

    [Fact]
    public void CalculateChecksum_WithValidData_ShouldReturnCrcBytes()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };

        // Act
        var checksum = _validator.CalculateChecksum(data);

        // Assert
        checksum.Should().HaveCount(2);
        checksum.Should().NotBeNull();

        // 验证往返一致性
        var dataWithCrc = _validator.AddChecksum(data);
        _validator.Validate(dataWithCrc).Should().BeTrue();
    }

    [Fact]
    public void AddChecksum_WithValidData_ShouldAppendCrc()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };

        // Act
        var result = _validator.AddChecksum(data);

        // Assert
        result.Should().HaveCount(8);
        result.Take(6).Should().BeEquivalentTo(data);

        // 验证添加的 CRC 是正确的
        _validator.Validate(result).Should().BeTrue();
    }

    [Fact]
    public void RemoveChecksum_WithValidData_ShouldRemoveCrc()
    {
        // Arrange
        var dataWithCrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x84, 0x0A };

        // Act
        var result = _validator.RemoveChecksum(dataWithCrc);

        // Assert
        result.Should().HaveCount(6);
        result.Should().BeEquivalentTo(new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 });
    }

    [Fact]
    public void Validate_WithValidCrc_ShouldReturnTrue()
    {
        // Arrange - 创建有效的数据
        var originalData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };
        var dataWithValidCrc = _validator.AddChecksum(originalData);

        // Act
        var result = _validator.Validate(dataWithValidCrc);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithInvalidCrc_ShouldReturnFalse()
    {
        // Arrange
        var dataWithInvalidCrc = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00 };

        // Act
        var result = _validator.Validate(dataWithInvalidCrc);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void Validate_WithInsufficientData_ShouldReturnFalse()
    {
        // Arrange
        var insufficientData = new byte[] { 0x01 };

        // Act
        var result = _validator.Validate(insufficientData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task ValidateAsync_WithValidCrc_ShouldReturnTrue()
    {
        // Arrange - 创建有效的数据
        var originalData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };
        var dataWithValidCrc = _validator.AddChecksum(originalData);

        // Act
        var result = await _validator.ValidateAsync(dataWithValidCrc);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void RemoveChecksum_WithInsufficientData_ShouldThrowException()
    {
        // Arrange
        var insufficientData = new byte[] { 0x01 };

        // Act & Assert
        var action = () => _validator.RemoveChecksum(insufficientData);
        action.Should().Throw<ArgumentException>()
            .WithMessage("数据长度不足，无法移除CRC校验*");
    }

    [Fact]
    public void CalculateCrc16_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        var action = () => Crc16Validator.CalculateCrc16(null!);
        action.Should().Throw<ArgumentNullException>();
    }

    [Fact]
    public void CalculateCrc16_WithEmptyData_ShouldReturnInitialValue()
    {
        // Arrange
        var emptyData = Array.Empty<byte>();

        // Act
        var crc = Crc16Validator.CalculateCrc16(emptyData);

        // Assert
        crc.Should().Be(0xFFFF); // 初始值
    }

    [Fact]
    public void VerifyCrc16_WithValidAndInvalidData_ShouldReturnExpectedResult()
    {
        // 创建有效的数据
        var originalData = new byte[] { 0x01, 0x03, 0x00, 0x00, 0x00, 0x01 };
        var validator = new Crc16Validator();
        var validDataWithCrc = validator.AddChecksum(originalData);

        // 测试有效数据
        Crc16Validator.VerifyCrc16(validDataWithCrc).Should().BeTrue();

        // 创建无效数据（破坏 CRC）
        var invalidData = (byte[])validDataWithCrc.Clone();
        invalidData[^1] = 0x00; // 破坏最后一个字节

        // 测试无效数据
        Crc16Validator.VerifyCrc16(invalidData).Should().BeFalse();
    }

    [Theory]
    [InlineData(new byte[] { 0x01, 0x03 }, 0, 2)]
    [InlineData(new byte[] { 0x01, 0x03, 0x00, 0x00 }, 1, 2)]
    public void CalculateCrc16_WithOffsetAndCount_ShouldCalculateCorrectly(byte[] data, int offset, int count)
    {
        // Act
        var crc = Crc16Validator.CalculateCrc16(data, offset, count);

        // Assert
        crc.Should().NotBe(0xFFFF); // 应该不等于初始值
    }

    [Fact]
    public void CalculateCrc16_WithInvalidOffset_ShouldThrowException()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03 };

        // Act & Assert
        var action = () => Crc16Validator.CalculateCrc16(data, -1, 1);
        action.Should().Throw<ArgumentOutOfRangeException>();
    }

    [Fact]
    public void CalculateCrc16_WithInvalidCount_ShouldThrowException()
    {
        // Arrange
        var data = new byte[] { 0x01, 0x03 };

        // Act & Assert
        var action = () => Crc16Validator.CalculateCrc16(data, 0, 5);
        action.Should().Throw<ArgumentOutOfRangeException>();
    }
}
