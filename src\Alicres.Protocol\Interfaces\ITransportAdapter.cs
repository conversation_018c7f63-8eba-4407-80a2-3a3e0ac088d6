using Alicres.Protocol.Models.EventArgs;

namespace Alicres.Protocol.Interfaces;

/// <summary>
/// 传输适配器接口，抽象不同的传输层实现
/// </summary>
public interface ITransportAdapter : IDisposable
{
    /// <summary>
    /// 传输类型名称
    /// </summary>
    string TransportType { get; }

    /// <summary>
    /// 连接状态
    /// </summary>
    bool IsConnected { get; }

    /// <summary>
    /// 传输配置信息
    /// </summary>
    object? Configuration { get; }

    /// <summary>
    /// 打开连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功打开返回 true，否则返回 false</returns>
    Task<bool> OpenAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 关闭连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功关闭返回 true，否则返回 false</returns>
    Task<bool> CloseAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 发送数据
    /// </summary>
    /// <param name="data">待发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功发送返回 true，否则返回 false</returns>
    Task<bool> SendAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 接收数据
    /// </summary>
    /// <param name="buffer">接收缓冲区</param>
    /// <param name="offset">缓冲区偏移量</param>
    /// <param name="count">要接收的字节数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际接收的字节数</returns>
    Task<int> ReceiveAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    void ClearReceiveBuffer();

    /// <summary>
    /// 获取接收缓冲区中的可用字节数
    /// </summary>
    /// <returns>可用字节数</returns>
    int GetAvailableBytes();

    /// <summary>
    /// 数据接收事件
    /// </summary>
    event EventHandler<TransportDataEventArgs>? DataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    event EventHandler<TransportStatusEventArgs>? StatusChanged;

    /// <summary>
    /// 传输错误事件
    /// </summary>
    event EventHandler<TransportErrorEventArgs>? ErrorOccurred;
}
