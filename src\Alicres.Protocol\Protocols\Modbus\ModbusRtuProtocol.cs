using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models;
using Alicres.Protocol.Protocols.Modbus.Messages;
using Alicres.Protocol.Validators;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Protocols.Modbus;

/// <summary>
/// Modbus RTU 协议解析器
/// </summary>
public class ModbusRtuProtocol : AbstractProtocolParser
{
    /// <summary>
    /// 协议名称
    /// </summary>
    public override string ProtocolName => "ModbusRTU";

    /// <summary>
    /// 协议版本
    /// </summary>
    public override string ProtocolVersion => "1.0.0";

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public ModbusRtuProtocol(ILogger<ModbusRtuProtocol>? logger = null) : base(logger)
    {
        // 默认启用 CRC16 校验
        ValidationEnabled = true;
        Validator = new Crc16Validator();
    }

    /// <summary>
    /// 检查数据是否为完整的协议消息
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果是完整消息返回 true，否则返回 false</returns>
    public override bool IsCompleteMessage(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        // Modbus RTU 最小消息长度为4字节（地址+功能码+数据+CRC）
        if (data.Length < 4)
            return false;

        var expectedLength = GetExpectedMessageLength(data);
        if (expectedLength == -1)
            return false;

        return data.Length >= expectedLength;
    }

    /// <summary>
    /// 获取消息的预期长度
    /// </summary>
    /// <param name="data">消息数据的开始部分</param>
    /// <returns>消息的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedMessageLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < 2)
            return -1;

        var functionCode = data[1];

        // 检查是否为异常响应
        if ((functionCode & 0x80) != 0)
        {
            // 异常响应：地址(1) + 功能码(1) + 异常码(1) + CRC(2) = 5字节
            return 5;
        }

        // 根据功能码确定消息长度
        return functionCode switch
        {
            0x01 or 0x02 => GetReadBitsResponseLength(data), // 读取线圈/离散输入
            0x03 or 0x04 => GetReadRegistersResponseLength(data), // 读取寄存器
            0x05 => 8, // 写单个线圈：地址(1) + 功能码(1) + 地址(2) + 值(2) + CRC(2) = 8字节
            0x06 => 8, // 写单个寄存器：地址(1) + 功能码(1) + 地址(2) + 值(2) + CRC(2) = 8字节
            0x0F => GetWriteMultipleCoilsLength(data), // 写多个线圈
            0x10 => GetWriteMultipleRegistersLength(data), // 写多个寄存器
            _ => -1 // 未知功能码
        };
    }

    /// <summary>
    /// 获取读取位响应的长度
    /// </summary>
    private int GetReadBitsResponseLength(byte[] data)
    {
        if (data.Length < 3)
            return -1;

        var byteCount = data[2];
        // 地址(1) + 功能码(1) + 字节数(1) + 数据(N) + CRC(2)
        return 3 + byteCount + 2;
    }

    /// <summary>
    /// 获取读取寄存器响应的长度
    /// </summary>
    private int GetReadRegistersResponseLength(byte[] data)
    {
        if (data.Length < 3)
            return -1;

        var byteCount = data[2];
        // 地址(1) + 功能码(1) + 字节数(1) + 数据(N) + CRC(2)
        return 3 + byteCount + 2;
    }

    /// <summary>
    /// 获取写多个线圈消息的长度
    /// </summary>
    private int GetWriteMultipleCoilsLength(byte[] data)
    {
        if (data.Length < 6)
            return -1;

        // 检查是否为请求消息（包含字节数字段）
        if (data.Length >= 7)
        {
            var byteCount = data[6];
            // 地址(1) + 功能码(1) + 起始地址(2) + 数量(2) + 字节数(1) + 数据(N) + CRC(2)
            return 7 + byteCount + 2;
        }
        else
        {
            // 响应消息：地址(1) + 功能码(1) + 起始地址(2) + 数量(2) + CRC(2) = 8字节
            return 8;
        }
    }

    /// <summary>
    /// 获取写多个寄存器消息的长度
    /// </summary>
    private int GetWriteMultipleRegistersLength(byte[] data)
    {
        if (data.Length < 6)
            return -1;

        // 检查是否为请求消息（包含字节数字段）
        if (data.Length >= 7)
        {
            var byteCount = data[6];
            // 地址(1) + 功能码(1) + 起始地址(2) + 数量(2) + 字节数(1) + 数据(N) + CRC(2)
            return 7 + byteCount + 2;
        }
        else
        {
            // 响应消息：地址(1) + 功能码(1) + 起始地址(2) + 数量(2) + CRC(2) = 8字节
            return 8;
        }
    }

    /// <summary>
    /// 内部解析逻辑
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的协议消息</returns>
    protected override Task<IProtocolMessage?> ParseInternalAsync(byte[] data, CancellationToken cancellationToken)
    {
        try
        {
            if (data.Length < 4)
            {
                Logger?.LogWarning("Modbus RTU 数据长度不足: {Length} 字节", data.Length);
                return Task.FromResult<IProtocolMessage?>(null);
            }

            var slaveAddress = data[0];
            var functionCode = data[1];

            Logger?.LogDebug("解析 Modbus RTU 消息: 从站地址={SlaveAddress}, 功能码=0x{FunctionCode:X2}", 
                slaveAddress, functionCode);

            // 检查是否为异常响应
            if ((functionCode & 0x80) != 0)
            {
                return Task.FromResult<IProtocolMessage?>(new ModbusExceptionResponse(data));
            }

            // 根据功能码解析具体消息
            IProtocolMessage? message = functionCode switch
            {
                0x03 => ParseReadHoldingRegistersMessage(data),
                0x04 => ParseReadInputRegistersMessage(data),
                // 可以继续添加其他功能码的解析
                _ => null
            };

            if (message == null)
            {
                Logger?.LogWarning("不支持的 Modbus RTU 功能码: 0x{FunctionCode:X2}", functionCode);
            }

            return Task.FromResult(message);
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "解析 Modbus RTU 消息时发生错误");
            return Task.FromResult<IProtocolMessage?>(null);
        }
    }

    /// <summary>
    /// 解析读取保持寄存器消息
    /// </summary>
    private IProtocolMessage? ParseReadHoldingRegistersMessage(byte[] data)
    {
        if (data.Length == 8) // 请求消息长度
        {
            return new ModbusReadHoldingRegistersRequest(data);
        }
        else if (data.Length >= 5) // 响应消息最小长度
        {
            return new ModbusReadHoldingRegistersResponse(data);
        }

        return null;
    }

    /// <summary>
    /// 解析读取输入寄存器消息（功能码 0x04）
    /// </summary>
    private IProtocolMessage? ParseReadInputRegistersMessage(byte[] data)
    {
        // 输入寄存器的解析逻辑与保持寄存器类似
        // 这里可以创建专门的输入寄存器消息类，或者复用保持寄存器的类
        if (data.Length == 8) // 请求消息长度
        {
            // 可以创建 ModbusReadInputRegistersRequest 类
            return new ModbusReadHoldingRegistersRequest(data); // 临时复用
        }
        else if (data.Length >= 5) // 响应消息最小长度
        {
            // 可以创建 ModbusReadInputRegistersResponse 类
            return new ModbusReadHoldingRegistersResponse(data); // 临时复用
        }

        return null;
    }

    /// <summary>
    /// 内部序列化逻辑
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>序列化后的字节数组</returns>
    protected override Task<byte[]> SerializeInternalAsync(IProtocolMessage message, CancellationToken cancellationToken)
    {
        try
        {
            if (message is not ModbusMessage modbusMessage)
            {
                throw new ArgumentException($"消息类型不是 Modbus 消息: {message.GetType().Name}");
            }

            Logger?.LogDebug("序列化 Modbus RTU 消息: {MessageType}", message.MessageType);

            var data = modbusMessage.ToBytes();
            return Task.FromResult(data);
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "序列化 Modbus RTU 消息时发生错误");
            throw;
        }
    }
}
