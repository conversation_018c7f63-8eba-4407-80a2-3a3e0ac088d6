using System.Collections.Concurrent;
using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models.EventArgs;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Framing;

/// <summary>
/// 抽象消息帧处理器基类，提供帧处理的通用实现
/// </summary>
public abstract class AbstractMessageFraming : IMessageFraming
{
    /// <summary>
    /// 日志记录器
    /// </summary>
    protected readonly ILogger? Logger;

    /// <summary>
    /// 数据缓冲区
    /// </summary>
    protected readonly ConcurrentQueue<byte> DataBuffer;

    /// <summary>
    /// 帧计数器
    /// </summary>
    protected int FrameCounter;

    /// <summary>
    /// 锁对象
    /// </summary>
    protected readonly object LockObject = new();

    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public abstract string FramingName { get; }

    /// <summary>
    /// 帧模式
    /// </summary>
    public abstract FramingMode Mode { get; }

    /// <summary>
    /// 是否启用帧处理
    /// </summary>
    public bool IsEnabled { get; set; } = true;

    /// <summary>
    /// 完整帧提取事件
    /// </summary>
    public event EventHandler<FrameExtractedEventArgs>? FrameExtracted;

    /// <summary>
    /// 帧处理错误事件
    /// </summary>
    public event EventHandler<FramingErrorEventArgs>? FramingError;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    protected AbstractMessageFraming(ILogger? logger = null)
    {
        Logger = logger;
        DataBuffer = new ConcurrentQueue<byte>();
        FrameCounter = 0;
    }

    /// <summary>
    /// 处理接收到的数据，提取完整的消息帧
    /// </summary>
    /// <param name="data">接收到的原始数据</param>
    /// <returns>提取出的完整消息帧列表</returns>
    public virtual List<byte[]> ProcessIncomingData(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (!IsEnabled || data.Length == 0)
            return new List<byte[]>();

        try
        {
            Logger?.LogTrace("处理接收数据，长度: {Length} 字节", data.Length);

            // 将数据添加到缓冲区
            foreach (var b in data)
            {
                DataBuffer.Enqueue(b);
            }

            // 提取完整帧
            var frames = ExtractFramesFromBuffer();
            
            Logger?.LogTrace("从缓冲区提取了 {Count} 个完整帧", frames.Count);
            
            return frames;
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "处理接收数据时发生错误");
            OnFramingError(new FramingErrorEventArgs(ex, FramingName, data));
            return new List<byte[]>();
        }
    }

    /// <summary>
    /// 为发送的消息添加帧信息
    /// </summary>
    /// <param name="message">原始消息数据</param>
    /// <returns>添加帧信息后的数据</returns>
    public virtual byte[] FrameMessage(byte[] message)
    {
        ArgumentNullException.ThrowIfNull(message);

        if (!IsEnabled)
            return message;

        try
        {
            Logger?.LogTrace("为消息添加帧信息，原始长度: {Length} 字节", message.Length);
            
            var framedMessage = AddFrameToMessage(message);
            
            Logger?.LogTrace("帧信息添加完成，帧长度: {Length} 字节", framedMessage.Length);
            
            return framedMessage;
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "添加帧信息时发生错误");
            OnFramingError(new FramingErrorEventArgs(ex, FramingName, message));
            return message; // 返回原始消息
        }
    }

    /// <summary>
    /// 从帧数据中提取原始消息
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>提取出的原始消息数据</returns>
    public virtual byte[] UnframeMessage(byte[] framedData)
    {
        ArgumentNullException.ThrowIfNull(framedData);

        if (!IsEnabled)
            return framedData;

        try
        {
            Logger?.LogTrace("从帧数据中提取原始消息，帧长度: {Length} 字节", framedData.Length);
            
            var message = RemoveFrameFromMessage(framedData);
            
            Logger?.LogTrace("原始消息提取完成，消息长度: {Length} 字节", message.Length);
            
            return message;
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "提取原始消息时发生错误");
            OnFramingError(new FramingErrorEventArgs(ex, FramingName, framedData));
            return framedData; // 返回原始数据
        }
    }

    /// <summary>
    /// 检查数据是否包含完整的帧
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果包含完整帧返回 true，否则返回 false</returns>
    public abstract bool HasCompleteFrame(byte[] data);

    /// <summary>
    /// 获取帧的预期长度
    /// </summary>
    /// <param name="data">帧数据的开始部分</param>
    /// <returns>帧的预期总长度，如果无法确定返回 -1</returns>
    public abstract int GetExpectedFrameLength(byte[] data);

    /// <summary>
    /// 重置帧处理器状态
    /// </summary>
    public virtual void Reset()
    {
        lock (LockObject)
        {
            while (DataBuffer.TryDequeue(out _)) { }
            FrameCounter = 0;
            Logger?.LogDebug("帧处理器状态已重置");
        }
    }

    /// <summary>
    /// 从缓冲区中提取完整的帧
    /// </summary>
    /// <returns>提取出的完整帧列表</returns>
    protected virtual List<byte[]> ExtractFramesFromBuffer()
    {
        var frames = new List<byte[]>();
        
        lock (LockObject)
        {
            var bufferData = DataBuffer.ToArray();
            if (bufferData.Length == 0)
                return frames;

            var processedBytes = 0;
            
            while (processedBytes < bufferData.Length)
            {
                var remainingData = new byte[bufferData.Length - processedBytes];
                Array.Copy(bufferData, processedBytes, remainingData, 0, remainingData.Length);

                if (!HasCompleteFrame(remainingData))
                    break;

                var frameLength = GetExpectedFrameLength(remainingData);
                if (frameLength <= 0 || frameLength > remainingData.Length)
                    break;

                var frameData = new byte[frameLength];
                Array.Copy(remainingData, frameData, frameLength);
                
                frames.Add(frameData);
                processedBytes += frameLength;
                
                // 触发帧提取事件
                OnFrameExtracted(new FrameExtractedEventArgs(frameData, ++FrameCounter, FramingName));
            }

            // 从缓冲区中移除已处理的数据
            if (processedBytes > 0)
            {
                var newBuffer = new ConcurrentQueue<byte>();
                for (int i = processedBytes; i < bufferData.Length; i++)
                {
                    newBuffer.Enqueue(bufferData[i]);
                }
                
                // 替换缓冲区
                while (DataBuffer.TryDequeue(out _)) { }
                while (newBuffer.TryDequeue(out var b))
                {
                    DataBuffer.Enqueue(b);
                }
            }
        }

        return frames;
    }

    /// <summary>
    /// 为消息添加帧信息的具体实现
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <returns>添加帧信息后的数据</returns>
    protected abstract byte[] AddFrameToMessage(byte[] message);

    /// <summary>
    /// 从帧数据中移除帧信息的具体实现
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>移除帧信息后的原始消息</returns>
    protected abstract byte[] RemoveFrameFromMessage(byte[] framedData);

    /// <summary>
    /// 触发帧提取事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnFrameExtracted(FrameExtractedEventArgs e)
    {
        FrameExtracted?.Invoke(this, e);
    }

    /// <summary>
    /// 触发帧处理错误事件
    /// </summary>
    /// <param name="e">事件参数</param>
    protected virtual void OnFramingError(FramingErrorEventArgs e)
    {
        FramingError?.Invoke(this, e);
    }
}
