using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models;
using Alicres.Protocol.Validators;
using Alicres.Protocol.Framing;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Protocols.Custom;

/// <summary>
/// 自定义协议解析器
/// 支持协议格式：帧头(1字节) + 源地址(1字节) + 目标地址(1字节) + 命令码(1字节) + 数据长度(1字节) + 数据字段(N字节) + CRC16(2字节)
/// </summary>
public class CustomProtocolParser : AbstractProtocolParser
{
    /// <summary>
    /// 协议名称
    /// </summary>
    public override string ProtocolName => "CustomProtocol";

    /// <summary>
    /// 协议版本
    /// </summary>
    public override string ProtocolVersion => "1.0.0";

    /// <summary>
    /// 帧头字节
    /// </summary>
    public byte FrameHeader { get; set; } = 0x7E;

    /// <summary>
    /// 自定义CRC16校验器
    /// </summary>
    private readonly CustomCrc16Validator _customValidator;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="logger">日志记录器</param>
    public CustomProtocolParser(ILogger<CustomProtocolParser>? logger = null) : base(logger)
    {
        // 使用自定义的CRC16校验器（排除帧头）
        _customValidator = new CustomCrc16Validator();
        ValidationEnabled = true;
        Validator = _customValidator;

        // 配置自定义帧处理器
        MessageFraming = new CustomHeaderFraming(FrameHeader);
    }

    /// <summary>
    /// 检查数据是否为完整的协议消息
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果是完整消息返回 true，否则返回 false</returns>
    public override bool IsCompleteMessage(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        // 最小消息长度：帧头(1) + 源地址(1) + 目标地址(1) + 命令码(1) + 长度(1) + CRC16(2) = 7字节
        if (data.Length < 7)
            return false;

        // 检查帧头
        if (data[0] != FrameHeader)
            return false;

        // 检查长度字段
        if (data.Length < 5)
            return false;

        var declaredLength = data[4];
        
        // 验证声明的长度是否合理
        if (declaredLength < 7 || declaredLength > 255)
            return false;

        // 检查实际数据长度是否匹配声明的长度
        return data.Length >= declaredLength;
    }

    /// <summary>
    /// 获取消息的预期长度
    /// </summary>
    /// <param name="data">消息数据的开始部分</param>
    /// <returns>消息的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedMessageLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < 5)
            return -1;

        if (data[0] != FrameHeader)
            return -1;

        var declaredLength = data[4];
        
        if (declaredLength < 7 || declaredLength > 255)
            return -1;

        return declaredLength;
    }

    /// <summary>
    /// 内部解析逻辑
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的协议消息</returns>
    protected override Task<IProtocolMessage?> ParseInternalAsync(byte[] data, CancellationToken cancellationToken)
    {
        try
        {
            Logger?.LogDebug("开始解析自定义协议消息，数据长度: {Length} 字节", data.Length);

            // 验证最小长度
            if (data.Length < 7)
            {
                Logger?.LogWarning("数据长度不足: {Length} 字节，最小需要 7 字节", data.Length);
                return Task.FromResult<IProtocolMessage?>(null);
            }

            // 验证帧头
            if (data[0] != FrameHeader)
            {
                Logger?.LogWarning("无效的帧头: 0x{Header:X2}，期望: 0x{Expected:X2}", data[0], FrameHeader);
                return Task.FromResult<IProtocolMessage?>(null);
            }

            // 创建协议消息
            var message = new CustomProtocolMessage(data);

            Logger?.LogDebug("成功解析自定义协议消息: 源地址=0x{Source:X2}, 目标地址=0x{Target:X2}, 命令码=0x{Command:X2}",
                message.SourceAddress, message.TargetAddress, message.CommandCode);

            return Task.FromResult<IProtocolMessage?>(message);
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "解析自定义协议消息时发生错误");
            return Task.FromResult<IProtocolMessage?>(null);
        }
    }

    /// <summary>
    /// 内部序列化逻辑
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>序列化后的字节数组</returns>
    protected override Task<byte[]> SerializeInternalAsync(IProtocolMessage message, CancellationToken cancellationToken)
    {
        try
        {
            if (message is not CustomProtocolMessage customMessage)
            {
                throw new ArgumentException($"消息类型不是自定义协议消息: {message.GetType().Name}");
            }

            Logger?.LogDebug("序列化自定义协议消息: {MessageType}", message.MessageType);

            // 获取消息的字节数组（不包含CRC16）
            var dataWithoutCrc = customMessage.ToBytes();
            
            // 计算CRC16（排除帧头）
            var dataForCrc = customMessage.GetDataForCrcCalculation();
            var crc16 = _customValidator.CalculateCrc16(dataForCrc);
            
            // 设置CRC16
            customMessage.SetCrc16(crc16);
            
            // 获取最终的字节数组
            var finalData = customMessage.ToBytes();

            Logger?.LogDebug("序列化完成，数据长度: {Length} 字节，CRC16: 0x{Crc:X4}", finalData.Length, crc16);

            return Task.FromResult(finalData);
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "序列化自定义协议消息时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 创建请求消息
    /// </summary>
    /// <param name="sourceAddress">源地址</param>
    /// <param name="targetAddress">目标地址</param>
    /// <param name="commandCode">命令码</param>
    /// <param name="dataField">数据字段</param>
    /// <returns>自定义协议消息</returns>
    public CustomProtocolMessage CreateMessage(byte sourceAddress, byte targetAddress, byte commandCode, byte[]? dataField = null)
    {
        return new CustomProtocolMessage(sourceAddress, targetAddress, commandCode, dataField ?? Array.Empty<byte>());
    }

    /// <summary>
    /// 解析协议示例数据
    /// </summary>
    /// <param name="hexString">十六进制字符串，如 "7E F1 05 55 0A 00 00 10 F5 82"</param>
    /// <returns>解析后的协议消息</returns>
    public async Task<CustomProtocolMessage?> ParseFromHexStringAsync(string hexString)
    {
        try
        {
            // 移除空格并转换为字节数组
            var cleanHex = hexString.Replace(" ", "").Replace("-", "");
            var data = new byte[cleanHex.Length / 2];
            
            for (int i = 0; i < data.Length; i++)
            {
                data[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
            }

            var message = await ParseAsync(data);
            return message as CustomProtocolMessage;
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "从十六进制字符串解析协议消息时发生错误: {HexString}", hexString);
            return null;
        }
    }
}
