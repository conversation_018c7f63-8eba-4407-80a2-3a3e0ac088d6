using Alicres.Protocol.Configuration;
using Alicres.Protocol.Framing;
using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Parsers;

/// <summary>
/// 可配置的协议解析器，支持基于配置的协议解析
/// </summary>
public class ConfigurableProtocolParser : AbstractProtocolParser
{
    /// <summary>
    /// 协议配置
    /// </summary>
    public ProtocolConfiguration Configuration { get; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public override string ProtocolName => Configuration.Name;

    /// <summary>
    /// 协议版本
    /// </summary>
    public override string ProtocolVersion => Configuration.Version;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">协议配置</param>
    /// <param name="logger">日志记录器</param>
    public ConfigurableProtocolParser(ProtocolConfiguration configuration, ILogger<ConfigurableProtocolParser>? logger = null) : base(logger)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        
        var (isValid, errors) = configuration.Validate();
        if (!isValid)
        {
            throw new ArgumentException($"协议配置无效: {string.Join(", ", errors)}");
        }

        // 配置帧处理器
        MessageFraming = new ConfigurableFraming(configuration);

        // 配置校验器
        if (configuration.ValidatorType != null)
        {
            try
            {
                Validator = (IProtocolValidator?)Activator.CreateInstance(configuration.ValidatorType);
                ValidationEnabled = Validator != null;
            }
            catch (Exception ex)
            {
                Logger?.LogWarning(ex, "无法创建校验器实例: {ValidatorType}", configuration.ValidatorType.Name);
                ValidationEnabled = false;
            }
        }
    }

    /// <summary>
    /// 检查数据是否为完整的协议消息
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果是完整消息返回 true，否则返回 false</returns>
    public override bool IsCompleteMessage(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < Configuration.MinFrameLength)
            return false;

        return MessageFraming?.HasCompleteFrame(data) ?? false;
    }

    /// <summary>
    /// 获取消息的预期长度
    /// </summary>
    /// <param name="data">消息数据的开始部分</param>
    /// <returns>消息的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedMessageLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        return MessageFraming?.GetExpectedFrameLength(data) ?? -1;
    }

    /// <summary>
    /// 内部解析逻辑
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>解析后的协议消息</returns>
    protected override Task<IProtocolMessage?> ParseInternalAsync(byte[] data, CancellationToken cancellationToken)
    {
        try
        {
            Logger?.LogDebug("开始解析协议消息，数据长度: {Length} 字节", data.Length);

            // 验证最小长度
            if (data.Length < Configuration.MinFrameLength)
            {
                Logger?.LogWarning("数据长度不足: {Length} 字节，最小需要 {MinLength} 字节", data.Length, Configuration.MinFrameLength);
                return Task.FromResult<IProtocolMessage?>(null);
            }

            // 创建可配置协议消息
            var message = new ConfigurableProtocolMessage(Configuration, data);

            Logger?.LogDebug("成功解析协议消息: {Message}", message);

            return Task.FromResult<IProtocolMessage?>(message);
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "解析协议消息时发生错误");
            return Task.FromResult<IProtocolMessage?>(null);
        }
    }

    /// <summary>
    /// 内部序列化逻辑
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>序列化后的字节数组</returns>
    protected override Task<byte[]> SerializeInternalAsync(IProtocolMessage message, CancellationToken cancellationToken)
    {
        try
        {
            if (message is not ConfigurableProtocolMessage configurableMessage)
            {
                throw new ArgumentException($"消息类型不是可配置协议消息: {message.GetType().Name}");
            }

            Logger?.LogDebug("序列化协议消息: {MessageType}", message.MessageType);

            // 获取消息的字节数组
            var messageBytes = configurableMessage.ToBytes();

            // 如果启用了校验，计算并设置校验值
            if (ValidationEnabled && Validator != null && Configuration.ChecksumRange != null)
            {
                var checksumField = Configuration.GetChecksumField();
                if (checksumField != null)
                {
                    var dataForChecksum = GetDataForChecksum(messageBytes, Configuration.ChecksumRange);
                    var checksum = Validator.CalculateChecksum(dataForChecksum);
                    
                    // 更新校验字段
                    SetChecksumInMessage(configurableMessage, checksumField, checksum);
                    messageBytes = configurableMessage.ToBytes();
                }
            }

            Logger?.LogDebug("序列化完成，数据长度: {Length} 字节", messageBytes.Length);

            return Task.FromResult(messageBytes);
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "序列化协议消息时发生错误");
            throw;
        }
    }

    /// <summary>
    /// 创建协议消息
    /// </summary>
    /// <param name="fieldValues">字段值字典</param>
    /// <returns>协议消息</returns>
    public ConfigurableProtocolMessage CreateMessage(Dictionary<string, object?> fieldValues)
    {
        var message = new ConfigurableProtocolMessage(Configuration);
        
        foreach (var kvp in fieldValues)
        {
            message.SetFieldValue(kvp.Key, kvp.Value);
        }

        return message;
    }

    /// <summary>
    /// 创建协议消息
    /// </summary>
    /// <param name="fieldValuePairs">字段名和值的配对</param>
    /// <returns>协议消息</returns>
    public ConfigurableProtocolMessage CreateMessage(params (string FieldName, object? Value)[] fieldValuePairs)
    {
        var fieldValues = fieldValuePairs.ToDictionary(pair => pair.FieldName, pair => pair.Value);
        return CreateMessage(fieldValues);
    }

    /// <summary>
    /// 解析十六进制字符串
    /// </summary>
    /// <param name="hexString">十六进制字符串，如 "7E F1 05 55 0A 00 00 10 F5 82"</param>
    /// <returns>解析后的协议消息</returns>
    public async Task<ConfigurableProtocolMessage?> ParseFromHexStringAsync(string hexString)
    {
        try
        {
            // 移除空格并转换为字节数组
            var cleanHex = hexString.Replace(" ", "").Replace("-", "");
            var data = new byte[cleanHex.Length / 2];
            
            for (int i = 0; i < data.Length; i++)
            {
                data[i] = Convert.ToByte(cleanHex.Substring(i * 2, 2), 16);
            }

            var message = await ParseAsync(data);
            return message as ConfigurableProtocolMessage;
        }
        catch (Exception ex)
        {
            Logger?.LogError(ex, "从十六进制字符串解析协议消息时发生错误: {HexString}", hexString);
            return null;
        }
    }

    /// <summary>
    /// 获取用于校验的数据
    /// </summary>
    /// <param name="messageBytes">完整消息字节</param>
    /// <param name="checksumRange">校验范围配置</param>
    /// <returns>用于校验的数据</returns>
    private byte[] GetDataForChecksum(byte[] messageBytes, ChecksumRangeConfiguration checksumRange)
    {
        var startOffset = checksumRange.StartOffset;
        var endOffset = messageBytes.Length + checksumRange.EndOffset;

        // 处理排除帧头的情况
        if (checksumRange.ExcludeFrameHeader)
        {
            var frameHeaderField = Configuration.GetFrameHeaderField();
            if (frameHeaderField?.FixedValue != null)
            {
                startOffset = Math.Max(startOffset, frameHeaderField.FixedValue.Length);
            }
        }

        // 处理排除校验字段的情况
        if (checksumRange.ExcludeChecksumField)
        {
            var checksumField = Configuration.GetChecksumField();
            if (checksumField != null)
            {
                endOffset = Math.Min(endOffset, messageBytes.Length - checksumField.Length);
            }
        }

        // 处理排除指定字段的情况
        // 这里简化处理，实际实现可能需要更复杂的逻辑
        
        var length = endOffset - startOffset;
        if (length <= 0)
            return Array.Empty<byte>();

        var result = new byte[length];
        Array.Copy(messageBytes, startOffset, result, 0, length);
        
        return result;
    }

    /// <summary>
    /// 在消息中设置校验值
    /// </summary>
    /// <param name="message">协议消息</param>
    /// <param name="checksumField">校验字段描述符</param>
    /// <param name="checksum">校验值</param>
    private void SetChecksumInMessage(ConfigurableProtocolMessage message, ProtocolFieldDescriptor checksumField, byte[] checksum)
    {
        object checksumValue = checksumField.Length switch
        {
            1 => checksum[0],
            2 => checksumField.ByteOrder == ByteOrder.LittleEndian 
                ? (ushort)(checksum[0] | (checksum[1] << 8))
                : (ushort)((checksum[0] << 8) | checksum[1]),
            4 => checksumField.ByteOrder == ByteOrder.LittleEndian
                ? (uint)(checksum[0] | (checksum[1] << 8) | (checksum[2] << 16) | (checksum[3] << 24))
                : (uint)((checksum[0] << 24) | (checksum[1] << 16) | (checksum[2] << 8) | checksum[3]),
            _ => checksum
        };

        message.SetFieldValue(checksumField.Name, checksumValue);
    }
}
