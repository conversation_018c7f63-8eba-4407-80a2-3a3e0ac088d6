using Alicres.Protocol.Interfaces;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Framing;

/// <summary>
/// 长度前缀帧处理器，使用长度前缀来标识消息帧的长度
/// </summary>
public class LengthPrefixFraming : AbstractMessageFraming
{
    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public override string FramingName => "LengthPrefixFraming";

    /// <summary>
    /// 帧模式
    /// </summary>
    public override FramingMode Mode => FramingMode.LengthPrefix;

    /// <summary>
    /// 长度字段的字节数（1、2、4字节）
    /// </summary>
    public int LengthFieldSize { get; set; }

    /// <summary>
    /// 是否使用大端字节序
    /// </summary>
    public bool IsBigEndian { get; set; }

    /// <summary>
    /// 长度字段是否包含自身的长度
    /// </summary>
    public bool IncludeLengthFieldInLength { get; set; }

    /// <summary>
    /// 最大消息长度（防止恶意数据）
    /// </summary>
    public int MaxMessageLength { get; set; } = 65536;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="lengthFieldSize">长度字段的字节数</param>
    /// <param name="isBigEndian">是否使用大端字节序</param>
    /// <param name="includeLengthFieldInLength">长度字段是否包含自身的长度</param>
    /// <param name="logger">日志记录器</param>
    public LengthPrefixFraming(int lengthFieldSize = 2, bool isBigEndian = true, 
        bool includeLengthFieldInLength = false, ILogger? logger = null) : base(logger)
    {
        if (lengthFieldSize != 1 && lengthFieldSize != 2 && lengthFieldSize != 4)
            throw new ArgumentException("长度字段大小必须是1、2或4字节", nameof(lengthFieldSize));

        LengthFieldSize = lengthFieldSize;
        IsBigEndian = isBigEndian;
        IncludeLengthFieldInLength = includeLengthFieldInLength;
    }

    /// <summary>
    /// 检查数据是否包含完整的帧
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果包含完整帧返回 true，否则返回 false</returns>
    public override bool HasCompleteFrame(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < LengthFieldSize)
            return false;

        var messageLength = ReadLengthField(data);
        if (messageLength < 0 || messageLength > MaxMessageLength)
        {
            Logger?.LogWarning("无效的消息长度: {Length}", messageLength);
            return false;
        }

        var totalFrameLength = IncludeLengthFieldInLength ? messageLength : LengthFieldSize + messageLength;
        return data.Length >= totalFrameLength;
    }

    /// <summary>
    /// 获取帧的预期长度
    /// </summary>
    /// <param name="data">帧数据的开始部分</param>
    /// <returns>帧的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedFrameLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < LengthFieldSize)
            return -1;

        var messageLength = ReadLengthField(data);
        if (messageLength < 0 || messageLength > MaxMessageLength)
            return -1;

        return IncludeLengthFieldInLength ? messageLength : LengthFieldSize + messageLength;
    }

    /// <summary>
    /// 为消息添加帧信息的具体实现
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <returns>添加帧信息后的数据</returns>
    protected override byte[] AddFrameToMessage(byte[] message)
    {
        ArgumentNullException.ThrowIfNull(message);

        if (message.Length > MaxMessageLength)
        {
            throw new ArgumentException($"消息长度 {message.Length} 超过最大限制 {MaxMessageLength}");
        }

        var lengthValue = IncludeLengthFieldInLength ? message.Length + LengthFieldSize : message.Length;
        var lengthBytes = WriteLengthField(lengthValue);

        var framedMessage = new byte[LengthFieldSize + message.Length];
        Array.Copy(lengthBytes, framedMessage, LengthFieldSize);
        Array.Copy(message, 0, framedMessage, LengthFieldSize, message.Length);

        Logger?.LogTrace("为消息添加长度前缀 {Length}，帧长度: {FrameLength}", 
            lengthValue, framedMessage.Length);

        return framedMessage;
    }

    /// <summary>
    /// 从帧数据中移除帧信息的具体实现
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>移除帧信息后的原始消息</returns>
    protected override byte[] RemoveFrameFromMessage(byte[] framedData)
    {
        ArgumentNullException.ThrowIfNull(framedData);

        if (framedData.Length < LengthFieldSize)
        {
            Logger?.LogWarning("帧数据长度 {Length} 小于长度字段大小 {LengthFieldSize}", 
                framedData.Length, LengthFieldSize);
            return Array.Empty<byte>();
        }

        var messageLength = ReadLengthField(framedData);
        var expectedMessageLength = IncludeLengthFieldInLength ? messageLength - LengthFieldSize : messageLength;

        if (expectedMessageLength < 0 || expectedMessageLength > framedData.Length - LengthFieldSize)
        {
            Logger?.LogWarning("无效的消息长度: {MessageLength}, 帧数据长度: {FrameLength}", 
                expectedMessageLength, framedData.Length);
            return Array.Empty<byte>();
        }

        var message = new byte[expectedMessageLength];
        Array.Copy(framedData, LengthFieldSize, message, 0, expectedMessageLength);

        Logger?.LogTrace("从帧数据中移除长度前缀，消息长度: {Length}", message.Length);

        return message;
    }

    /// <summary>
    /// 读取长度字段的值
    /// </summary>
    /// <param name="data">包含长度字段的数据</param>
    /// <returns>长度值</returns>
    private int ReadLengthField(byte[] data)
    {
        if (data.Length < LengthFieldSize)
            return -1;

        return LengthFieldSize switch
        {
            1 => data[0],
            2 => IsBigEndian ? (data[0] << 8) | data[1] : data[0] | (data[1] << 8),
            4 => IsBigEndian 
                ? (data[0] << 24) | (data[1] << 16) | (data[2] << 8) | data[3]
                : data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24),
            _ => -1
        };
    }

    /// <summary>
    /// 写入长度字段的值
    /// </summary>
    /// <param name="length">长度值</param>
    /// <returns>长度字段的字节数组</returns>
    private byte[] WriteLengthField(int length)
    {
        var bytes = new byte[LengthFieldSize];

        switch (LengthFieldSize)
        {
            case 1:
                if (length > 255)
                    throw new ArgumentException($"长度 {length} 超过1字节字段的最大值255");
                bytes[0] = (byte)length;
                break;

            case 2:
                if (length > 65535)
                    throw new ArgumentException($"长度 {length} 超过2字节字段的最大值65535");
                if (IsBigEndian)
                {
                    bytes[0] = (byte)(length >> 8);
                    bytes[1] = (byte)(length & 0xFF);
                }
                else
                {
                    bytes[0] = (byte)(length & 0xFF);
                    bytes[1] = (byte)(length >> 8);
                }
                break;

            case 4:
                if (IsBigEndian)
                {
                    bytes[0] = (byte)(length >> 24);
                    bytes[1] = (byte)((length >> 16) & 0xFF);
                    bytes[2] = (byte)((length >> 8) & 0xFF);
                    bytes[3] = (byte)(length & 0xFF);
                }
                else
                {
                    bytes[0] = (byte)(length & 0xFF);
                    bytes[1] = (byte)((length >> 8) & 0xFF);
                    bytes[2] = (byte)((length >> 16) & 0xFF);
                    bytes[3] = (byte)(length >> 24);
                }
                break;
        }

        return bytes;
    }

    /// <summary>
    /// 获取配置信息的字符串表示
    /// </summary>
    /// <returns>配置信息</returns>
    public override string ToString()
    {
        return $"{FramingName} (长度字段: {LengthFieldSize} 字节, 大端: {IsBigEndian}, 包含长度字段: {IncludeLengthFieldInLength}, 最大长度: {MaxMessageLength})";
    }
}
