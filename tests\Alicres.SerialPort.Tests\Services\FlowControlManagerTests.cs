using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using System.Collections.Concurrent;
using Xunit;

namespace Alicres.SerialPort.Tests.Services;

/// <summary>
/// 流控制管理器测试类
/// </summary>
public class FlowControlManagerTests : IDisposable
{
    private readonly Mock<ILogger> _mockLogger;
    private readonly FlowControlManager _flowControlManager;

    /// <summary>
    /// 构造函数
    /// </summary>
    public FlowControlManagerTests()
    {
        _mockLogger = new Mock<ILogger>();
        var configuration = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600
        };
        _flowControlManager = new FlowControlManager(configuration, _mockLogger.Object);
        _flowControlManager.FlowControlType = FlowControlType.XonXoff;
    }

    /// <summary>
    /// 测试构造函数正确初始化
    /// </summary>
    [Fact]
    public void Constructor_WithValidParameters_ShouldInitializeCorrectly()
    {
        // Assert
        _flowControlManager.FlowControlType.Should().Be(FlowControlType.XonXoff);
        _flowControlManager.IsEnabled.Should().BeFalse(); // 默认未启用
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试启用流控制
    /// </summary>
    [Fact]
    public void Enable_ShouldSetIsEnabledToTrue()
    {
        // Act
        _flowControlManager.IsEnabled = true;

        // Assert
        _flowControlManager.IsEnabled.Should().BeTrue();
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试禁用流控制
    /// </summary>
    [Fact]
    public void Disable_ShouldSetIsEnabledToFalse()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;

        // Act
        _flowControlManager.IsEnabled = false;

        // Assert
        _flowControlManager.IsEnabled.Should().BeFalse();
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试设置发送速率限制
    /// </summary>
    [Fact]
    public void SetSendRateLimit_WithValidRate_ShouldUpdateLimit()
    {
        // Act
        _flowControlManager.SendRateLimit = 1000;

        // Assert
        _flowControlManager.SendRateLimit.Should().Be(1000);
    }

    /// <summary>
    /// 测试设置负数发送速率限制
    /// </summary>
    [Fact]
    public void SetSendRateLimit_WithNegativeRate_ShouldSetToZero()
    {
        // Act
        _flowControlManager.SendRateLimit = -1;

        // Assert - 负数会被设置为0
        _flowControlManager.SendRateLimit.Should().Be(0);
    }

    /// <summary>
    /// 测试检查是否可以发送数据（未启用流控制）
    /// </summary>
    [Fact]
    public void CanSend_WhenDisabled_ShouldReturnTrue()
    {
        // Act
        var result = _flowControlManager.CanSend(100);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// 测试检查是否可以发送数据（启用流控制）
    /// </summary>
    [Fact]
    public void CanSend_WhenEnabled_ShouldReturnTrue()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;

        // Act
        var result = _flowControlManager.CanSend(100);

        // Assert
        result.Should().BeTrue();
    }

    /// <summary>
    /// 测试记录发送数据
    /// </summary>
    [Fact]
    public void RecordSend_WithValidData_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.RecordSend(100);
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试记录发送数据负数长度
    /// </summary>
    [Fact]
    public void RecordSend_WithNegativeLength_ShouldNotThrow()
    {
        // Act & Assert - 方法内部会检查IsEnabled，未启用时直接返回
        var action = () => _flowControlManager.RecordSend(-1);
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试处理流控制数据（未启用）
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WhenDisabled_ShouldNotProcess()
    {
        // Arrange
        var data = new byte[] { 0x13 }; // XOFF

        // Act & Assert
        var action = () => _flowControlManager.ProcessFlowControlData(data);
        action.Should().NotThrow();
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试处理流控制数据（启用XON/XOFF）
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithXoffData_ShouldPauseFlow()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        var xoffData = new byte[] { 0x13 }; // XOFF

        // Act
        _flowControlManager.ProcessFlowControlData(xoffData);

        // Assert
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Paused);
    }

    /// <summary>
    /// 测试处理流控制数据（XON恢复）
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        var xoffData = new byte[] { 0x13 }; // XOFF
        var xonData = new byte[] { 0x11 };  // XON

        // Act
        _flowControlManager.ProcessFlowControlData(xoffData);
        _flowControlManager.ProcessFlowControlData(xonData);

        // Assert
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试处理空流控制数据
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithNullData_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.ProcessFlowControlData(null!);
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试处理空流控制数据数组
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithEmptyData_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.ProcessFlowControlData(Array.Empty<byte>());
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试处理非XON/XOFF流控制类型
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM2" };
        var rtsCtsManager = new FlowControlManager(configuration, _mockLogger.Object);
        rtsCtsManager.FlowControlType = FlowControlType.RtsCts;
        rtsCtsManager.IsEnabled = true;
        var xoffData = new byte[] { 0x13 }; // XOFF

        // Act
        rtsCtsManager.ProcessFlowControlData(xoffData);

        // Assert
        rtsCtsManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
        rtsCtsManager.Dispose();
    }

    /// <summary>
    /// 测试获取统计信息
    /// </summary>
    [Fact]
    public void GetStatistics_ShouldReturnValidStatistics()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        _flowControlManager.RecordSend(100);

        // Act
        var statistics = _flowControlManager.GetStatistics();

        // Assert
        statistics.Should().NotBeNull();
        statistics.FlowControlType.Should().Be(FlowControlType.XonXoff);
        statistics.IsEnabled.Should().BeTrue();
        statistics.CurrentStatus.Should().Be(FlowControlStatus.Normal);
        statistics.TotalBytesSent.Should().Be(100);
    }

    /// <summary>
    /// 测试不同流控制类型的构造
    /// </summary>
    [Theory]
    [InlineData(FlowControlType.None)]
    [InlineData(FlowControlType.XonXoff)]
    [InlineData(FlowControlType.RtsCts)]
    [InlineData(FlowControlType.Both)]
    public void Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(FlowControlType flowControlType)
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM3" };

        // Act
        using var manager = new FlowControlManager(configuration, _mockLogger.Object);
        manager.FlowControlType = flowControlType;

        // Assert
        manager.FlowControlType.Should().Be(flowControlType);
        manager.IsEnabled.Should().BeFalse();
        manager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试释放资源
    /// </summary>
    [Fact]
    public void Dispose_ShouldNotThrow()
    {
        // Act & Assert
        var action = () => _flowControlManager.Dispose();
        action.Should().NotThrow();
    }

    /// <summary>
    /// 测试多次释放资源
    /// </summary>
    [Fact]
    public void Dispose_CalledMultipleTimes_ShouldNotThrow()
    {
        // Act & Assert
        _flowControlManager.Dispose();
        var action = () => _flowControlManager.Dispose();
        action.Should().NotThrow();
    }

    #region 边界条件和并发测试补充 - 第一阶段

    /// <summary>
    /// 测试极限发送速率限制
    /// </summary>
    [Theory]
    [InlineData(1)]      // 极低速率
    [InlineData(1000000)] // 极高速率
    [InlineData(int.MaxValue)] // 最大值
    public void SendRateLimit_ExtremeValues_ShouldHandleCorrectly(int rateLimit)
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        _flowControlManager.SendRateLimit = rateLimit;

        // Act & Assert
        _flowControlManager.SendRateLimit.Should().Be(rateLimit);

        // 测试是否可以发送
        var canSend = _flowControlManager.CanSend(100);
        canSend.Should().BeTrue(); // 第一次发送应该总是可以的
    }

    /// <summary>
    /// 测试发送速率限制的精确性
    /// </summary>
    [Fact]
    public void CanSend_WithRateLimit_ShouldEnforceTimingAccurately()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        _flowControlManager.SendRateLimit = 1000; // 1000 字节/秒

        var dataSize = 500; // 500字节，应该需要0.5秒间隔

        // Act - 第一次发送
        var canSendFirst = _flowControlManager.CanSend(dataSize);
        canSendFirst.Should().BeTrue();

        _flowControlManager.RecordSend(dataSize);

        // 立即尝试第二次发送
        var canSendImmediate = _flowControlManager.CanSend(dataSize);
        canSendImmediate.Should().BeFalse("应该受到速率限制");

        // 等待足够时间后再次尝试
        Thread.Sleep(600); // 等待0.6秒
        var canSendAfterWait = _flowControlManager.CanSend(dataSize);
        canSendAfterWait.Should().BeTrue("等待足够时间后应该可以发送");
    }

    /// <summary>
    /// 测试XON/XOFF字符的边界情况
    /// </summary>
    [Theory]
    [InlineData(new byte[] { 0x11, 0x13, 0x11 })] // XON, XOFF, XON 序列
    [InlineData(new byte[] { 0x13, 0x13, 0x11 })] // 重复XOFF, XON
    [InlineData(new byte[] { 0x11, 0x11, 0x13 })] // 重复XON, XOFF
    public void ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(byte[] sequence)
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        var initialStatus = _flowControlManager.CurrentStatus;

        // Act
        _flowControlManager.ProcessFlowControlData(sequence);

        // Assert
        // 最终状态应该基于最后一个有效的流控制字符
        var lastControlChar = sequence.LastOrDefault(b => b == 0x11 || b == 0x13);
        var expectedStatus = lastControlChar == 0x13 ? FlowControlStatus.Paused : FlowControlStatus.Normal;

        _flowControlManager.CurrentStatus.Should().Be(expectedStatus);
    }

    /// <summary>
    /// 测试混合数据中的流控制字符识别
    /// </summary>
    [Fact]
    public void ProcessFlowControlData_MixedWithRegularData_ShouldExtractControlCharsCorrectly()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        var mixedData = new byte[] { 0x48, 0x65, 0x6C, 0x13, 0x6C, 0x6F, 0x11, 0x21 }; // "Hel[XOFF]lo[XON]!"

        // Act
        _flowControlManager.ProcessFlowControlData(mixedData);

        // Assert
        // 最后的XON应该使状态恢复正常
        _flowControlManager.CurrentStatus.Should().Be(FlowControlStatus.Normal);
    }

    /// <summary>
    /// 测试并发流控制状态变更
    /// </summary>
    [Fact]
    public async Task ProcessFlowControlData_ConcurrentStatusChanges_ShouldBeThreadSafe()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        var tasks = new List<Task>();
        var statusChanges = new ConcurrentBag<FlowControlStatus>();

        // 监听状态变化
        _flowControlManager.StatusChanged += (sender, e) =>
        {
            statusChanges.Add(e.NewStatus);
        };

        // Act - 并发发送XON/XOFF命令
        for (int i = 0; i < 20; i++)
        {
            var isXoff = i % 2 == 0;
            tasks.Add(Task.Run(() =>
            {
                var data = new byte[] { (byte)(isXoff ? 0x13 : 0x11) };
                _flowControlManager.ProcessFlowControlData(data);
                Thread.Sleep(Random.Shared.Next(1, 10));
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        statusChanges.Should().NotBeEmpty();
        _flowControlManager.CurrentStatus.Should().BeOneOf(FlowControlStatus.Normal, FlowControlStatus.Paused);
    }

    /// <summary>
    /// 测试RTS/CTS流控制的并发操作
    /// </summary>
    [Fact]
    public async Task SetRtsFlowControl_ConcurrentOperations_ShouldBeThreadSafe()
    {
        // Arrange
        var configuration = new SerialPortConfiguration { PortName = "COM_TEST" };
        using var rtsManager = new FlowControlManager(configuration, _mockLogger.Object);
        rtsManager.FlowControlType = FlowControlType.RtsCts;
        rtsManager.IsEnabled = true;

        var tasks = new List<Task>();
        var statusChanges = new ConcurrentBag<FlowControlStatus>();

        rtsManager.StatusChanged += (sender, e) =>
        {
            statusChanges.Add(e.NewStatus);
        };

        // Act - 并发设置RTS状态
        for (int i = 0; i < 50; i++)
        {
            var isPaused = i % 2 == 0;
            tasks.Add(Task.Run(() =>
            {
                rtsManager.SetRtsFlowControl(isPaused);
                Thread.Sleep(Random.Shared.Next(1, 5));
            }));
        }

        await Task.WhenAll(tasks);

        // Assert
        rtsManager.CurrentStatus.Should().BeOneOf(FlowControlStatus.Normal, FlowControlStatus.Paused);
    }

    /// <summary>
    /// 测试高频率的CanSend调用性能
    /// </summary>
    [Fact]
    public void CanSend_HighFrequencyCalls_ShouldMaintainPerformance()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        _flowControlManager.SendRateLimit = 10000; // 高速率限制

        const int callCount = 10000;
        var stopwatch = System.Diagnostics.Stopwatch.StartNew();

        // Act
        for (int i = 0; i < callCount; i++)
        {
            _flowControlManager.CanSend(100);
        }

        stopwatch.Stop();

        // Assert
        var avgTimePerCall = stopwatch.Elapsed.TotalMilliseconds / callCount;
        avgTimePerCall.Should().BeLessThan(0.1, "每次CanSend调用应该在0.1ms内完成");

        Console.WriteLine($"CanSend性能测试: {callCount} 次调用, 平均 {avgTimePerCall:F4}ms/次");
    }

    /// <summary>
    /// 测试统计信息在高负载下的准确性
    /// </summary>
    [Fact]
    public void GetStatistics_UnderHighLoad_ShouldProvideAccurateData()
    {
        // Arrange
        _flowControlManager.IsEnabled = true;
        _flowControlManager.SendRateLimit = 5000;

        var totalBytesSent = 0;
        var sendCount = 100;

        // Act
        for (int i = 0; i < sendCount; i++)
        {
            var dataSize = Random.Shared.Next(50, 200);
            if (_flowControlManager.CanSend(dataSize))
            {
                _flowControlManager.RecordSend(dataSize);
                totalBytesSent += dataSize;
            }

            // 偶尔处理流控制数据
            if (i % 10 == 0)
            {
                var controlData = new byte[] { (byte)(i % 2 == 0 ? 0x13 : 0x11) };
                _flowControlManager.ProcessFlowControlData(controlData);
            }
        }

        var statistics = _flowControlManager.GetStatistics();

        // Assert
        statistics.Should().NotBeNull();
        statistics.TotalBytesSent.Should().Be(totalBytesSent);
        statistics.IsEnabled.Should().BeTrue();
        statistics.SendRateLimit.Should().Be(5000);
    }

    #endregion

    /// <summary>
    /// 释放测试资源
    /// </summary>
    public void Dispose()
    {
        _flowControlManager?.Dispose();
    }
}
