﻿<?xml version="1.0" encoding="utf-8"?>
<TestRun id="28ec04d4-49df-44c5-bf57-dadb39c64894" name="wa<PERSON><PERSON><PERSON><PERSON>@JD-ITA028088-PC 2025-06-12 13:28:51" runUser="AUXGROUP\wangzhuhui" xmlns="http://microsoft.com/schemas/VisualStudio/TeamTest/2010">
  <Times creation="2025-06-12T13:28:51.1135206+08:00" queuing="2025-06-12T13:28:51.1135209+08:00" start="2025-06-12T13:28:49.2793443+08:00" finish="2025-06-12T13:28:53.1181835+08:00" />
  <TestSettings name="default" id="2de86cd0-ee95-453c-b338-6876e6b63d7a">
    <Deployment runDeploymentRoot="wangz<PERSON>hui_JD-ITA028088-PC_2025-06-12_13_28_51" />
  </TestSettings>
  <Results>
    <UnitTestResult executionId="951a4cfe-92d0-486d-8d2b-14affd768105" testId="2e50dc24-0e76-792e-ccef-ba135fad6d20" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses" computerName="JD-ITA028088-PC" duration="00:00:00.0018711" startTime="2025-06-12T13:28:51.1722865+08:00" endTime="2025-06-12T13:28:51.1722865+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="951a4cfe-92d0-486d-8d2b-14affd768105" />
    <UnitTestResult executionId="90852709-4922-4b8e-8502-633e7fb1c280" testId="19c767b9-00f8-d409-00da-eeea99ad6661" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0000370" startTime="2025-06-12T13:28:51.1560849+08:00" endTime="2025-06-12T13:28:51.1560849+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="90852709-4922-4b8e-8502-633e7fb1c280" />
    <UnitTestResult executionId="f483fa16-2005-45e1-b2c5-60249c894262" testId="6c69255c-6ff2-34a3-9fdf-65012918c8be" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004357" startTime="2025-06-12T13:28:51.1287190+08:00" endTime="2025-06-12T13:28:51.1287190+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f483fa16-2005-45e1-b2c5-60249c894262" />
    <UnitTestResult executionId="5ff61378-6a9b-4588-a8fe-6fdc4e791607" testId="5dbf9991-d128-405c-e119-2c5c8fb571d8" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;48 65 6C 6C 6&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0143247" startTime="2025-06-12T13:28:51.0812743+08:00" endTime="2025-06-12T13:28:51.0812744+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5ff61378-6a9b-4588-a8fe-6fdc4e791607" />
    <UnitTestResult executionId="f46d47a0-f22a-4aa8-beed-cb98a489d1d7" testId="44298843-f215-2386-e136-f2a25ffedf0e" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 1000, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000074" startTime="2025-06-12T13:28:51.1084015+08:00" endTime="2025-06-12T13:28:51.1084017+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f46d47a0-f22a-4aa8-beed-cb98a489d1d7" />
    <UnitTestResult executionId="add52d9c-4e63-44e6-bbd4-ac5115bf5d0b" testId="82d8705e-825e-feba-4da5-acda164c6f53" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002841" startTime="2025-06-12T13:28:51.1144058+08:00" endTime="2025-06-12T13:28:51.1144059+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="add52d9c-4e63-44e6-bbd4-ac5115bf5d0b" />
    <UnitTestResult executionId="eebb5d20-dbf6-4e55-a8e1-28cced0f71c3" testId="023e96e9-d51b-a358-64ce-ba11d0feba6d" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001292" startTime="2025-06-12T13:28:51.1151314+08:00" endTime="2025-06-12T13:28:51.1151315+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="eebb5d20-dbf6-4e55-a8e1-28cced0f71c3" />
    <UnitTestResult executionId="01325f81-d45b-4639-9e8d-93b06d3f7c39" testId="0acc92da-f9c6-47d9-c5a2-760ab204265e" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 6, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000058" startTime="2025-06-12T13:28:51.0986800+08:00" endTime="2025-06-12T13:28:51.0986801+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="01325f81-d45b-4639-9e8d-93b06d3f7c39" />
    <UnitTestResult executionId="745578c4-57a7-44ce-9f64-cd69ecc95c30" testId="f4fbaad3-6781-c8aa-cfe1-bc48e5a2111b" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Dispose_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003214" startTime="2025-06-12T13:28:51.1698576+08:00" endTime="2025-06-12T13:28:51.1698576+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="745578c4-57a7-44ce-9f64-cd69ecc95c30" />
    <UnitTestResult executionId="96aba62f-8818-47c5-87c3-90c4887632e1" testId="de18f2eb-3df5-540f-0a3a-47950916d770" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002817" startTime="2025-06-12T13:28:51.1247564+08:00" endTime="2025-06-12T13:28:51.1247565+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="96aba62f-8818-47c5-87c3-90c4887632e1" />
    <UnitTestResult executionId="a8d95230-93c6-4a7d-8e70-169769baa4de" testId="e5f24540-0924-e65b-6f16-4ab793a71012" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.SerialPortServiceOptions_ShouldHaveCorrectDefaults" computerName="JD-ITA028088-PC" duration="00:00:00.0006403" startTime="2025-06-12T13:28:51.1337279+08:00" endTime="2025-06-12T13:28:51.1337279+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a8d95230-93c6-4a7d-8e70-169769baa4de" />
    <UnitTestResult executionId="3f3cfffa-799e-4115-9d4c-0c4d05d8898c" testId="81eb87c9-cb57-f039-ec3e-433c7ad89d3b" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithExistingPort_ShouldReturnService" computerName="JD-ITA028088-PC" duration="00:00:00.0055311" startTime="2025-06-12T13:28:51.1467473+08:00" endTime="2025-06-12T13:28:51.1467473+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3f3cfffa-799e-4115-9d4c-0c4d05d8898c" />
    <UnitTestResult executionId="69e1b7ce-3be8-4c06-bf14-f3de9eeec5de" testId="90a5f1aa-f90c-1c7c-8cfb-a59b5defb741" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 1024)" computerName="JD-ITA028088-PC" duration="00:00:00.0012155" startTime="2025-06-12T13:28:51.2445780+08:00" endTime="2025-06-12T13:28:51.2445780+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="69e1b7ce-3be8-4c06-bf14-f3de9eeec5de" />
    <UnitTestResult executionId="a034f87e-9513-4865-99ea-7007653f5432" testId="2cc2a28b-0583-c385-8580-90889ea0fa8d" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004036" startTime="2025-06-12T13:28:51.1294464+08:00" endTime="2025-06-12T13:28:51.1294465+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a034f87e-9513-4865-99ea-7007653f5432" />
    <UnitTestResult executionId="81d44f91-05de-46bc-ac9d-8e0765c01b00" testId="cffe2f0d-7498-e2dd-fb74-c52cfbb78304" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: Both)" computerName="JD-ITA028088-PC" duration="00:00:00.0000313" startTime="2025-06-12T13:28:51.1494414+08:00" endTime="2025-06-12T13:28:51.1494414+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="81d44f91-05de-46bc-ac9d-8e0765c01b00" />
    <UnitTestResult executionId="e9cfbda9-4706-4621-90b9-f3c6e9695a82" testId="b1444560-e97c-3484-c5b5-7415dcdaf44b" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: XonXoff)" computerName="JD-ITA028088-PC" duration="00:00:00.0000441" startTime="2025-06-12T13:28:51.1492300+08:00" endTime="2025-06-12T13:28:51.1492300+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e9cfbda9-4706-4621-90b9-f3c6e9695a82" />
    <UnitTestResult executionId="7ff8156d-54f7-40b6-b3ed-e7861812cac9" testId="7255bb30-60f6-a3b4-e259-af25a7d0a5b7" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToRead_WhenNotConnected_ShouldReturnZero" computerName="JD-ITA028088-PC" duration="00:00:00.0003181" startTime="2025-06-12T13:28:51.1510812+08:00" endTime="2025-06-12T13:28:51.1510812+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7ff8156d-54f7-40b6-b3ed-e7861812cac9" />
    <UnitTestResult executionId="85463075-0783-4067-b047-526e86f270a0" testId="4797f9c7-926d-5ab1-d952-801179405c81" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;INVALID&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0038430" startTime="2025-06-12T13:28:51.7544452+08:00" endTime="2025-06-12T13:28:51.7544454+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="85463075-0783-4067-b047-526e86f270a0" />
    <UnitTestResult executionId="a4555635-a85f-412b-9e10-077685d6537c" testId="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 4096, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000077" startTime="2025-06-12T13:28:51.1043365+08:00" endTime="2025-06-12T13:28:51.1043366+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a4555635-a85f-412b-9e10-077685d6537c" />
    <UnitTestResult executionId="ea998325-802c-4092-ac73-1d7366faa88c" testId="586a2248-3398-cdf2-2aaf-e284666f9fc1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0115268" startTime="2025-06-12T13:28:51.0816420+08:00" endTime="2025-06-12T13:28:51.0816420+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ea998325-802c-4092-ac73-1d7366faa88c" />
    <UnitTestResult executionId="fe4b6b6d-bee5-445e-8e8b-61f43bce57db" testId="0110b712-a0ab-bd1c-e53f-1f5be5b0e0fa" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 10000)" computerName="JD-ITA028088-PC" duration="00:00:00.0003052" startTime="2025-06-12T13:28:51.1416598+08:00" endTime="2025-06-12T13:28:51.1416599+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fe4b6b6d-bee5-445e-8e8b-61f43bce57db" />
    <UnitTestResult executionId="3b98bd82-8f26-4ecd-b8d6-9bea56c0164e" testId="ccb6e43b-ed87-41fb-aacf-8532bac21ec1" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: DropNewest)" computerName="JD-ITA028088-PC" duration="00:00:00.0006572" startTime="2025-06-12T13:28:51.1475832+08:00" endTime="2025-06-12T13:28:51.1475833+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3b98bd82-8f26-4ecd-b8d6-9bea56c0164e" />
    <UnitTestResult executionId="348f548d-6db4-4946-b3e8-97c5ebc09019" testId="bfdab111-1736-9094-751d-b681895868a8" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;   &quot;, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001626" startTime="2025-06-12T13:28:51.1217291+08:00" endTime="2025-06-12T13:28:51.1217291+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="348f548d-6db4-4946-b3e8-97c5ebc09019" />
    <UnitTestResult executionId="4de4d1d4-c3c5-4d3e-87d0-89b61329cce5" testId="35ae527b-65f4-e761-c5e9-42b376ef79c1" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_MixedWithRegularData_ShouldExtractControlCharsCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001508" startTime="2025-06-12T13:28:51.8580743+08:00" endTime="2025-06-12T13:28:51.8580743+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4de4d1d4-c3c5-4d3e-87d0-89b61329cce5" />
    <UnitTestResult executionId="df9d9e58-fdc9-481c-b136-202e243aa8db" testId="eb395603-826c-2aec-bdbc-d35ee9d4654e" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_NullAndEmptyParameters_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.0037990" startTime="2025-06-12T13:28:51.4997416+08:00" endTime="2025-06-12T13:28:51.4997417+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="df9d9e58-fdc9-481c-b136-202e243aa8db" />
    <UnitTestResult executionId="a3e99d5d-f5dd-4dd6-9499-4ffb83d90be7" testId="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0011853" startTime="2025-06-12T13:28:51.1367236+08:00" endTime="2025-06-12T13:28:51.1367237+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a3e99d5d-f5dd-4dd6-9499-4ffb83d90be7" />
    <UnitTestResult executionId="dc4fa79d-97af-4d71-9f7a-bd4fad276943" testId="53a79683-6e18-bdcc-2942-0ae631602802" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 7, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000044" startTime="2025-06-12T13:28:51.0993870+08:00" endTime="2025-06-12T13:28:51.0993871+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dc4fa79d-97af-4d71-9f7a-bd4fad276943" />
    <UnitTestResult executionId="b8e27a8d-7e6f-4095-a619-9e6d69403772" testId="54caa2f7-9288-7523-076d-6fecd3569986" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConfigurationUpdate_ShouldApplyCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0018846" startTime="2025-06-12T13:28:51.2690782+08:00" endTime="2025-06-12T13:28:51.2690783+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b8e27a8d-7e6f-4095-a619-9e6d69403772" />
    <UnitTestResult executionId="147c43d5-1fa4-4f03-8854-fc8b62b6a2c6" testId="de7a5bdb-03df-5bef-5375-0d9de7fb33cd" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48 65 6C 6C 6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0004294" startTime="2025-06-12T13:28:51.1359929+08:00" endTime="2025-06-12T13:28:51.1359929+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="147c43d5-1fa4-4f03-8854-fc8b62b6a2c6" />
    <UnitTestResult executionId="ceeae204-bfe1-473f-a87b-b78491b735ae" testId="0e388063-0d1d-fee8-e535-f6ea426ca6a0" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;GG&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0006512" startTime="2025-06-12T13:28:51.1054472+08:00" endTime="2025-06-12T13:28:51.1054473+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ceeae204-bfe1-473f-a87b-b78491b735ae" />
    <UnitTestResult executionId="22242042-b953-42b6-a003-211b8e2dc127" testId="c18a85de-caa6-f92b-dd42-c491eddd8d5a" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_BufferOverflow_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.6294421" startTime="2025-06-12T13:28:51.7469541+08:00" endTime="2025-06-12T13:28:51.7469544+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="22242042-b953-42b6-a003-211b8e2dc127" />
    <UnitTestResult executionId="3524b761-a242-43f7-aa7b-431e2b114664" testId="3d3291e3-98dd-e9e2-de72-914857c1022d" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithValidService_ShouldAddToCollection" computerName="JD-ITA028088-PC" duration="00:00:00.0103408" startTime="2025-06-12T13:28:51.1681742+08:00" endTime="2025-06-12T13:28:51.1681743+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3524b761-a242-43f7-aa7b-431e2b114664" />
    <UnitTestResult executionId="dbe9734a-4d67-4839-9f9f-e0ee4fc6aa52" testId="05265970-e28e-f5df-e7d7-9126a86d207f" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastAsync_WithNullData_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0014832" startTime="2025-06-12T13:28:51.1700604+08:00" endTime="2025-06-12T13:28:51.1700604+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dbe9734a-4d67-4839-9f9f-e0ee4fc6aa52" />
    <UnitTestResult executionId="672746ff-99ba-4793-93da-3ee85364ee58" testId="3b43f857-4c69-124a-f1e5-4526c8f72ed1" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000043" startTime="2025-06-12T13:28:51.1046673+08:00" endTime="2025-06-12T13:28:51.1046674+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="672746ff-99ba-4793-93da-3ee85364ee58" />
    <UnitTestResult executionId="3d4f7dd1-9ad2-498c-ae6a-a291aed1d1fb" testId="97ad4d87-64e8-3d1f-2000-33d34d4e461d" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_RapidConnectDisconnect_ShouldHandleStably" computerName="JD-ITA028088-PC" duration="00:00:00.2146369" startTime="2025-06-12T13:28:51.4958279+08:00" endTime="2025-06-12T13:28:51.4958283+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3d4f7dd1-9ad2-498c-ae6a-a291aed1d1fb" />
    <UnitTestResult executionId="63ecf14e-772d-41b1-aee2-74073bbd57b8" testId="c4b6fa98-2242-c1ba-efc6-ec174c1be4e2" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\0&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0022212" startTime="2025-06-12T13:28:51.2536334+08:00" endTime="2025-06-12T13:28:51.2536335+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="63ecf14e-772d-41b1-aee2-74073bbd57b8" />
    <UnitTestResult executionId="7064f45b-fe9d-48ae-88b9-b5c3e37e5fdf" testId="c34da281-7d78-d48e-fd36-b17e7d8c79e7" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [], expectedHex: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0001174" startTime="2025-06-12T13:28:51.1260253+08:00" endTime="2025-06-12T13:28:51.1260254+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7064f45b-fe9d-48ae-88b9-b5c3e37e5fdf" />
    <UnitTestResult executionId="b46962e9-2c63-452d-8239-a5efddd62907" testId="08d3fc81-b996-b7a6-e1cd-78cabc053881" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0129421" startTime="2025-06-12T13:28:51.0805642+08:00" endTime="2025-06-12T13:28:51.0805642+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b46962e9-2c63-452d-8239-a5efddd62907" />
    <UnitTestResult executionId="15677d44-7fa2-4608-bbed-273fe4a276d7" testId="57f89734-de5a-0489-86e3-f364df300ca2" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004559" startTime="2025-06-12T13:28:51.1286098+08:00" endTime="2025-06-12T13:28:51.1286099+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="15677d44-7fa2-4608-bbed-273fe4a276d7" />
    <UnitTestResult executionId="f9bcea77-6c5e-4ea7-aab9-eb4ddbde4863" testId="acca298d-8d66-e074-22dd-fdf90888635f" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearSendBuffer_WhenNotConnected_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0007083" startTime="2025-06-12T13:28:51.1498360+08:00" endTime="2025-06-12T13:28:51.1498361+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f9bcea77-6c5e-4ea7-aab9-eb4ddbde4863" />
    <UnitTestResult executionId="bfe8f9d8-bf46-4c5a-b1bc-b6fd445a81ae" testId="d99254fa-0691-194c-a6ad-0465dd755790" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 9600)" computerName="JD-ITA028088-PC" duration="00:00:00.0006891" startTime="2025-06-12T13:28:51.2648636+08:00" endTime="2025-06-12T13:28:51.2648637+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bfe8f9d8-bf46-4c5a-b1bc-b6fd445a81ae" />
    <UnitTestResult executionId="2398c596-00b2-4a2c-818e-573fa8fb4361" testId="28df04bd-39fc-1cce-6c87-14cb33ebc5ed" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetRtsFlowControl_ConcurrentOperations_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.0124898" startTime="2025-06-12T13:28:51.8704715+08:00" endTime="2025-06-12T13:28:51.8704716+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2398c596-00b2-4a2c-818e-573fa8fb4361" />
    <UnitTestResult executionId="b1087388-e3e4-4c77-b34e-653603d6524e" testId="57a78de4-cb96-8d86-813e-ab82acf74480" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 9, shouldTriggerWarning: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0005061" startTime="2025-06-12T13:28:51.1543975+08:00" endTime="2025-06-12T13:28:51.1543976+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b1087388-e3e4-4c77-b34e-653603d6524e" />
    <UnitTestResult executionId="2e8b7ba4-c25b-486a-97aa-6cb2ce1ae5e2" testId="06f82529-1ce7-16b1-604c-9f0392f7d160" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0013601" startTime="2025-06-12T13:28:51.7505026+08:00" endTime="2025-06-12T13:28:51.7505028+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2e8b7ba4-c25b-486a-97aa-6cb2ce1ae5e2" />
    <UnitTestResult executionId="5f72bc09-0928-40f7-82ec-7c0e78bfe51d" testId="b5d0270e-cd4b-dce1-3f1c-951f7852f9a7" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_BasicLifecycle_ShouldWorkCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0024771" startTime="2025-06-12T13:28:51.2685428+08:00" endTime="2025-06-12T13:28:51.2685429+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5f72bc09-0928-40f7-82ec-7c0e78bfe51d" />
    <UnitTestResult executionId="594f8804-5ffa-41ce-a24f-3705fc620b1a" testId="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToString_ShouldReturnFormattedString" computerName="JD-ITA028088-PC" duration="00:00:00.0017602" startTime="2025-06-12T13:28:51.1374903+08:00" endTime="2025-06-12T13:28:51.1374903+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="594f8804-5ffa-41ce-a24f-3705fc620b1a" />
    <UnitTestResult executionId="43e19725-a803-445e-8465-8a99aa11c356" testId="26c01f72-2f55-6301-3660-bbcccfc220bf" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 7, shouldTriggerWarning: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0002375" startTime="2025-06-12T13:28:51.1548147+08:00" endTime="2025-06-12T13:28:51.1548147+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="43e19725-a803-445e-8465-8a99aa11c356" />
    <UnitTestResult executionId="2841655f-c4bf-4603-a8e3-2da35a914f82" testId="80209ebf-9964-6643-698c-8cc7c39137a7" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 512)" computerName="JD-ITA028088-PC" duration="00:00:00.0009852" startTime="2025-06-12T13:28:51.2774601+08:00" endTime="2025-06-12T13:28:51.2774602+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2841655f-c4bf-4603-a8e3-2da35a914f82" />
    <UnitTestResult executionId="69b78690-82a5-40c5-8a31-f5208c2de4e0" testId="0d5b3495-7932-d5af-331e-630ce28c78dd" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0003616" startTime="2025-06-12T13:28:51.1289752+08:00" endTime="2025-06-12T13:28:51.1289753+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="69b78690-82a5-40c5-8a31-f5208c2de4e0" />
    <UnitTestResult executionId="7166a037-ae8d-40a1-946f-a9bfa7f2a40f" testId="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 9600, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001420" startTime="2025-06-12T13:28:51.1129610+08:00" endTime="2025-06-12T13:28:51.1129612+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7166a037-ae8d-40a1-946f-a9bfa7f2a40f" />
    <UnitTestResult executionId="d9608f4e-6585-410d-98a5-4fccb6321341" testId="15548509-32f5-1f17-61af-b5d469801c2e" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001810" startTime="2025-06-12T13:28:51.1716850+08:00" endTime="2025-06-12T13:28:51.1716850+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d9608f4e-6585-410d-98a5-4fccb6321341" />
    <UnitTestResult executionId="321c0449-aa58-4c80-a701-e06c6781de51" testId="d095da48-d2a1-b869-306b-68ec74fb6a86" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 8, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000077" startTime="2025-06-12T13:28:51.0982920+08:00" endTime="2025-06-12T13:28:51.0982921+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="321c0449-aa58-4c80-a701-e06c6781de51" />
    <UnitTestResult executionId="8a07da2e-c89c-4aee-bf09-43e6d0b7fee8" testId="1110dce6-cdc2-edbd-83e1-76acc885583d" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004293" startTime="2025-06-12T13:28:51.1516140+08:00" endTime="2025-06-12T13:28:51.1516140+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8a07da2e-c89c-4aee-bf09-43e6d0b7fee8" />
    <UnitTestResult executionId="86431e36-fdf8-470f-92b0-193de547cf03" testId="54e12c3f-6b43-5687-a842-a3d35e6a7a19" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\t&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0011212" startTime="2025-06-12T13:28:51.2571662+08:00" endTime="2025-06-12T13:28:51.2571663+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="86431e36-fdf8-470f-92b0-193de547cf03" />
    <UnitTestResult executionId="5685f135-9271-4cd9-8d27-76409933fbb5" testId="48f760e6-1514-1162-1f47-005a4a4b8f9d" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 115200)" computerName="JD-ITA028088-PC" duration="00:00:00.0010312" startTime="2025-06-12T13:28:51.2621465+08:00" endTime="2025-06-12T13:28:51.2621466+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5685f135-9271-4cd9-8d27-76409933fbb5" />
    <UnitTestResult executionId="b4b837ab-c5b9-4992-88b1-8971f07c2337" testId="2f29d2d9-c8a3-7a41-59a4-b408ec3f6dd9" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortManager_BasicOperations_ShouldWorkCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0095342" startTime="2025-06-12T13:28:51.2539621+08:00" endTime="2025-06-12T13:28:51.2539621+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b4b837ab-c5b9-4992-88b1-8971f07c2337" />
    <UnitTestResult executionId="6fc05425-af77-49a3-9568-e9613286622b" testId="746694ab-c99f-c4df-9347-d40ac323bdff" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidOffset_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0604558" startTime="2025-06-12T13:28:51.1377229+08:00" endTime="2025-06-12T13:28:51.1377229+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6fc05425-af77-49a3-9568-e9613286622b" />
    <UnitTestResult executionId="59560606-8719-4f19-a61e-e1b044afe6cd" testId="aa267d8a-b773-ddc4-6cfe-031c9347f3d1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0019606" startTime="2025-06-12T13:28:51.1187584+08:00" endTime="2025-06-12T13:28:51.1187585+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="59560606-8719-4f19-a61e-e1b044afe6cd" />
    <UnitTestResult executionId="afe412f9-f55e-4489-abd0-56061771ac02" testId="f3202fa7-6b71-5d20-4401-3d8bc9668755" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0010847" startTime="2025-06-12T13:28:51.1269552+08:00" endTime="2025-06-12T13:28:51.1269553+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="afe412f9-f55e-4489-abd0-56061771ac02" />
    <UnitTestResult executionId="8bc32029-ca05-4803-ab10-9a719e7a0c4c" testId="abc2d729-2b39-bae1-e7d9-85d1bd974a73" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM1&quot;, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000095" startTime="2025-06-12T13:28:51.1242860+08:00" endTime="2025-06-12T13:28:51.1242861+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8bc32029-ca05-4803-ab10-9a719e7a0c4c" />
    <UnitTestResult executionId="a9c81fd1-7c7d-4477-8189-e5b34eb1097b" testId="d030f382-42f0-6302-7045-2cfd01c44112" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithNegativeRate_ShouldSetToZero" computerName="JD-ITA028088-PC" duration="00:00:00.0003808" startTime="2025-06-12T13:28:51.8578277+08:00" endTime="2025-06-12T13:28:51.8578277+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a9c81fd1-7c7d-4477-8189-e5b34eb1097b" />
    <UnitTestResult executionId="fd487815-0c6c-4623-ade8-aa2bd6049305" testId="db95ccd0-c126-7f48-a28b-a633c62602b2" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 100)" computerName="JD-ITA028088-PC" duration="00:00:00.0016678" startTime="2025-06-12T13:28:51.7562126+08:00" endTime="2025-06-12T13:28:51.7562128+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fd487815-0c6c-4623-ade8-aa2bd6049305" />
    <UnitTestResult executionId="43b8a1ae-ea2f-4a63-97f9-177f13be5a36" testId="6ff352e2-9dcf-35a2-90fb-231d5f1f5bba" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_CalledMultipleTimes_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0004070" startTime="2025-06-12T13:28:51.1539298+08:00" endTime="2025-06-12T13:28:51.1539299+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="43b8a1ae-ea2f-4a63-97f9-177f13be5a36" />
    <UnitTestResult executionId="6c1458e6-4f42-421e-a453-d182049b2cc6" testId="f26d999c-5247-a0ac-b5ab-848e393d4022" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 300)" computerName="JD-ITA028088-PC" duration="00:00:00.0015201" startTime="2025-06-12T13:28:51.2611886+08:00" endTime="2025-06-12T13:28:51.2611887+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6c1458e6-4f42-421e-a453-d182049b2cc6" />
    <UnitTestResult executionId="3e137971-669a-44c1-b858-b19d0344f52d" testId="ad145a9e-531c-61f2-55a9-e48b910a979e" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0006085" startTime="2025-06-12T13:28:51.1481906+08:00" endTime="2025-06-12T13:28:51.1481907+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3e137971-669a-44c1-b858-b19d0344f52d" />
    <UnitTestResult executionId="c37643f8-d529-436b-bc97-011fe05c58c8" testId="bed1de91-05bf-0085-34af-7bee2cd33ea7" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullConfigureOptions_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0007420" startTime="2025-06-12T13:28:51.1332853+08:00" endTime="2025-06-12T13:28:51.1332854+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c37643f8-d529-436b-bc97-011fe05c58c8" />
    <UnitTestResult executionId="0b1e9da5-5ece-410c-a077-9ce5764114e3" testId="9bd2b2f8-4184-1ac4-6817-df59f52f3eae" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0002984" startTime="2025-06-12T13:28:51.1656249+08:00" endTime="2025-06-12T13:28:51.1656250+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0b1e9da5-5ece-410c-a077-9ce5764114e3" />
    <UnitTestResult executionId="33dff329-eefd-40af-976f-1d7c9e051786" testId="2a46be50-f303-bb70-1536-236543d44d59" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_HighFrequencyCalls_ShouldMaintainPerformance" computerName="JD-ITA028088-PC" duration="00:00:00.0045091" startTime="2025-06-12T13:28:51.1630553+08:00" endTime="2025-06-12T13:28:51.1630554+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="33dff329-eefd-40af-976f-1d7c9e051786" />
    <UnitTestResult executionId="1953f434-f65b-4a5b-90ad-2cefdbaca545" testId="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_Default_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0005292" startTime="2025-06-12T13:28:51.1409276+08:00" endTime="2025-06-12T13:28:51.1409276+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1953f434-f65b-4a5b-90ad-2cefdbaca545" />
    <UnitTestResult executionId="9b4317ef-7d09-4146-a35c-aa19092d1902" testId="fa202986-794c-673e-db28-2db703cc0fc9" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0005832" startTime="2025-06-12T13:28:51.1189207+08:00" endTime="2025-06-12T13:28:51.1189207+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9b4317ef-7d09-4146-a35c-aa19092d1902" />
    <UnitTestResult executionId="e55a6481-2379-479d-949d-7cd97d2b6a37" testId="78f63ed6-a82b-1dc7-8333-4f5a85338c27" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0003476" startTime="2025-06-12T13:28:51.1292187+08:00" endTime="2025-06-12T13:28:51.1292188+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e55a6481-2379-479d-949d-7cd97d2b6a37" />
    <UnitTestResult executionId="40e7a617-4afd-4fe8-8ae4-81f79ff6a6e1" testId="4910faa5-5592-c319-7394-2de698b2c0c7" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0001014" startTime="2025-06-12T13:28:51.1272787+08:00" endTime="2025-06-12T13:28:51.1272788+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="40e7a617-4afd-4fe8-8ae4-81f79ff6a6e1" />
    <UnitTestResult executionId="f38269ab-ce5b-4a19-8379-12e2fd5f7a63" testId="e0e718ff-4c78-ae1f-949d-1594d0430302" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullServiceProvider_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004242" startTime="2025-06-12T13:28:51.1720826+08:00" endTime="2025-06-12T13:28:51.1720827+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f38269ab-ce5b-4a19-8379-12e2fd5f7a63" />
    <UnitTestResult executionId="28439401-bc8b-47fb-a069-4af76f28506a" testId="ff8dcbb8-c854-51a3-cf7a-a1ca3b1fcc29" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullServices_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004491" startTime="2025-06-12T13:28:51.1339259+08:00" endTime="2025-06-12T13:28:51.1339260+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="28439401-bc8b-47fb-a069-4af76f28506a" />
    <UnitTestResult executionId="33203fdd-8290-4e18-8316-d739cb25f474" testId="ceb4348a-7f39-5209-f0c5-47a92f0ccf52" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" computerName="JD-ITA028088-PC" duration="00:00:00.0008108" startTime="2025-06-12T13:28:51.1504152+08:00" endTime="2025-06-12T13:28:51.1504152+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="33203fdd-8290-4e18-8316-d739cb25f474" />
    <UnitTestResult executionId="01b7f89e-5363-4bbb-ae6c-907d5a235f44" testId="1e4bdd15-076b-9af7-45c3-530c1fecf022" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.Constructor_ShouldInitializeWithDefaultValues" computerName="JD-ITA028088-PC" duration="00:00:00.0055221" startTime="2025-06-12T13:28:51.1308967+08:00" endTime="2025-06-12T13:28:51.1308968+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="01b7f89e-5363-4bbb-ae6c-907d5a235f44" />
    <UnitTestResult executionId="fc8712a6-ba7f-4562-a4d8-3bb7bac7fe3b" testId="37b241c9-a28c-3630-63d5-22833c81cd74" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0001258" startTime="2025-06-12T13:28:51.1311420+08:00" endTime="2025-06-12T13:28:51.1311420+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="fc8712a6-ba7f-4562-a4d8-3bb7bac7fe3b" />
    <UnitTestResult executionId="8ed6e52a-ba87-4b97-a840-3453858381a7" testId="02b715c1-0002-be47-2b1c-d64152d8f12d" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 65536)" computerName="JD-ITA028088-PC" duration="00:00:00.0015762" startTime="2025-06-12T13:28:51.2512800+08:00" endTime="2025-06-12T13:28:51.2512802+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8ed6e52a-ba87-4b97-a840-3453858381a7" />
    <UnitTestResult executionId="12b23232-17d9-4999-a956-52600de5ab32" testId="0ba56fbe-43b1-2a63-5322-5cc9943a2bc2" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithValidData_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003441" startTime="2025-06-12T13:28:51.1434033+08:00" endTime="2025-06-12T13:28:51.1434034+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="12b23232-17d9-4999-a956-52600de5ab32" />
    <UnitTestResult executionId="66a06abc-2e9d-44fa-b53c-2eaeaf78a76d" testId="4e85762c-ae91-2b9e-76be-257c688fb9fa" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtMaxCapacity_ShouldHandleCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004519" startTime="2025-06-12T13:28:51.4585595+08:00" endTime="2025-06-12T13:28:51.4585596+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="66a06abc-2e9d-44fa-b53c-2eaeaf78a76d" />
    <UnitTestResult executionId="0b372984-b1f9-4b6b-bd67-13cd603c083d" testId="df64e4db-ecba-2113-fb43-2182e90a55d5" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithEmptyQueue_ShouldReturnFalse" computerName="JD-ITA028088-PC" duration="00:00:00.0002830" startTime="2025-06-12T13:28:52.9602499+08:00" endTime="2025-06-12T13:28:52.9602500+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0b372984-b1f9-4b6b-bd67-13cd603c083d" />
    <UnitTestResult executionId="48ec0c7c-e89f-4517-a05e-f4404800f234" testId="f758c623-141e-3413-11d2-f683cfe5a47c" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidCount_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0013729" startTime="2025-06-12T13:28:51.1654073+08:00" endTime="2025-06-12T13:28:51.1654074+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="48ec0c7c-e89f-4517-a05e-f4404800f234" />
    <UnitTestResult executionId="a0c4c48e-a845-454a-ba8e-4ceafdc85dc1" testId="f0dd822e-110a-9423-89f0-2a6bc0510f84" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 1024, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001076" startTime="2025-06-12T13:28:51.1031519+08:00" endTime="2025-06-12T13:28:51.1031520+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a0c4c48e-a845-454a-ba8e-4ceafdc85dc1" />
    <UnitTestResult executionId="7a6db02b-97f4-480b-ab3f-de2d6337c076" testId="4593fd1a-0307-349e-dd30-e0a7cd2c3a3b" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConnectionLost_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.0755847" startTime="2025-06-12T13:28:51.1625484+08:00" endTime="2025-06-12T13:28:51.1625484+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7a6db02b-97f4-480b-ab3f-de2d6337c076" />
    <UnitTestResult executionId="c44acc57-6086-42fe-a4fd-f57f4c9e9eec" testId="f9be5f74-0f42-c1ca-06ce-4ff54de6dea3" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenDisabled_ShouldReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0001455" startTime="2025-06-12T13:28:51.1633255+08:00" endTime="2025-06-12T13:28:51.1633255+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c44acc57-6086-42fe-a4fd-f57f4c9e9eec" />
    <UnitTestResult executionId="a6a58689-7796-40f3-92de-857f273496ac" testId="d2dd8c1b-a46c-c63e-2bb9-e11f3f0fe385" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullLogger_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0004540" startTime="2025-06-12T13:28:51.1561912+08:00" endTime="2025-06-12T13:28:51.1561913+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a6a58689-7796-40f3-92de-857f273496ac" />
    <UnitTestResult executionId="41f649f7-0d77-49a3-b648-1de2c5f4ad21" testId="3fede858-ef69-906c-1781-83551e8174e8" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Dispose_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003725" startTime="2025-06-12T13:28:51.1588589+08:00" endTime="2025-06-12T13:28:51.1588590+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="41f649f7-0d77-49a3-b648-1de2c5f4ad21" />
    <UnitTestResult executionId="dcc65334-d407-4ef1-9398-ad9da5ac40a4" testId="cf59dfeb-fc64-c423-a638-29288238b6c4" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow" computerName="JD-ITA028088-PC" duration="00:00:00.0001487" startTime="2025-06-12T13:28:51.1570874+08:00" endTime="2025-06-12T13:28:51.1570875+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dcc65334-d407-4ef1-9398-ad9da5ac40a4" />
    <UnitTestResult executionId="7f8c7092-e2cf-4a70-ae7c-24391b81c0c6" testId="c46d3460-b4d6-d480-98b9-89cb5e5d84d1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0006866" startTime="2025-06-12T13:28:51.1198966+08:00" endTime="2025-06-12T13:28:51.1198966+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7f8c7092-e2cf-4a70-ae7c-24391b81c0c6" />
    <UnitTestResult executionId="aa08870c-74a0-4845-9108-7e8e42100887" testId="ea26ec81-d8c7-5fae-e953-a047af719c17" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastTextAsync_WithNullText_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0011503" startTime="2025-06-12T13:28:51.1718826+08:00" endTime="2025-06-12T13:28:51.1718826+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="aa08870c-74a0-4845-9108-7e8e42100887" />
    <UnitTestResult executionId="48704f38-56e5-48fb-89bd-da0d86b6575f" testId="fcf1ca95-bb0c-f451-5094-8b054adedf81" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002341" startTime="2025-06-12T13:28:51.1663503+08:00" endTime="2025-06-12T13:28:51.1663503+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="48704f38-56e5-48fb-89bd-da0d86b6575f" />
    <UnitTestResult executionId="154122c8-8007-4e45-bb9b-3415fbc287f9" testId="faacff99-6157-5e13-4b0f-4d4468903363" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48:65:6C:6C:6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0263690" startTime="2025-06-12T13:28:51.1348863+08:00" endTime="2025-06-12T13:28:51.1348863+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="154122c8-8007-4e45-bb9b-3415fbc287f9" />
    <UnitTestResult executionId="980ca5f0-9ad2-457e-8b63-561c7b01dfa2" testId="cd2d846a-4253-c669-9172-ae3f3e9c95ea" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [19, 19, 17])" computerName="JD-ITA028088-PC" duration="00:00:00.0000420" startTime="2025-06-12T13:28:51.1522591+08:00" endTime="2025-06-12T13:28:51.1522592+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="980ca5f0-9ad2-457e-8b63-561c7b01dfa2" />
    <UnitTestResult executionId="156d39ce-902e-4d57-87e9-5a2b3eb75bb6" testId="700480ab-f9a3-c14c-3862-c09cc3b0f4a7" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" computerName="JD-ITA028088-PC" duration="00:00:00.0002980" startTime="2025-06-12T13:28:52.5773782+08:00" endTime="2025-06-12T13:28:52.5773783+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="156d39ce-902e-4d57-87e9-5a2b3eb75bb6" />
    <UnitTestResult executionId="e8afc1d1-c70c-410f-9150-52ad02030166" testId="b25ea050-dc5b-ed4b-30ec-a1476f14ecc6" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_RepeatedOperations_ShouldNotLeakMemory" computerName="JD-ITA028088-PC" duration="00:00:00.0516507" startTime="2025-06-12T13:28:51.8099559+08:00" endTime="2025-06-12T13:28:51.8099562+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e8afc1d1-c70c-410f-9150-52ad02030166" />
    <UnitTestResult executionId="3b181b05-9fba-4552-84e4-d60c3651c8d2" testId="bfffdd04-e7b6-b0a1-e537-a650f2f9df27" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 9, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001603" startTime="2025-06-12T13:28:51.0978712+08:00" endTime="2025-06-12T13:28:51.0978714+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3b181b05-9fba-4552-84e4-d60c3651c8d2" />
    <UnitTestResult executionId="2c40d0d5-44a6-420e-ad3f-66168307d7a8" testId="b3f503b4-beb7-1162-dc16-c72eb6d05b08" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Enable_ShouldSetIsEnabledToTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0001533" startTime="2025-06-12T13:28:51.1535085+08:00" endTime="2025-06-12T13:28:51.1535086+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2c40d0d5-44a6-420e-ad3f-66168307d7a8" />
    <UnitTestResult executionId="a65cd216-e109-4577-82cf-e939c8bfffff" testId="5bb814e9-4a25-51d9-60b7-6112270973bf" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithExistingPort_ShouldRemoveAndReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0003471" startTime="2025-06-12T13:28:51.1689684+08:00" endTime="2025-06-12T13:28:51.1689685+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a65cd216-e109-4577-82cf-e939c8bfffff" />
    <UnitTestResult executionId="cc465c13-7644-4691-8df3-15d540c8a1d8" testId="9510bd6c-2b16-38c6-12a7-a2ae238d0e20" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldReturnServiceCollection" computerName="JD-ITA028088-PC" duration="00:00:00.0006220" startTime="2025-06-12T13:28:51.1317385+08:00" endTime="2025-06-12T13:28:51.1317386+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cc465c13-7644-4691-8df3-15d540c8a1d8" />
    <UnitTestResult executionId="cb6076f8-7296-47ca-94ef-38396ad9c6ce" testId="1a863009-7253-b831-45ba-4599991a1e23" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;ASCII&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0751875" startTime="2025-06-12T13:28:51.1623186+08:00" endTime="2025-06-12T13:28:51.1623188+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cb6076f8-7296-47ca-94ef-38396ad9c6ce" />
    <UnitTestResult executionId="2fa9b714-292d-4634-b91e-b67ef1b9e17c" testId="2bba4be0-2bc3-6003-e0d8-d93e1c6880cb" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002959" startTime="2025-06-12T13:28:51.1048848+08:00" endTime="2025-06-12T13:28:51.1048848+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2fa9b714-292d-4634-b91e-b67ef1b9e17c" />
    <UnitTestResult executionId="98018e43-c3f3-4d7f-946b-50d165e2025e" testId="b1ab4e5a-1abe-d4fc-3bce-7b8f40d0cefc" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.SerialPortDataException_ShouldInheritFromSerialPortException" computerName="JD-ITA028088-PC" duration="00:00:00.0012015" startTime="2025-06-12T13:28:51.1093116+08:00" endTime="2025-06-12T13:28:51.1093117+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="98018e43-c3f3-4d7f-946b-50d165e2025e" />
    <UnitTestResult executionId="004da334-5d0c-4ed3-8716-7d28a8a368fb" testId="3da8ccb5-36b2-5b31-1537-436bec0c05cc" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 1000)" computerName="JD-ITA028088-PC" duration="00:00:00.0000619" startTime="2025-06-12T13:28:51.1426927+08:00" endTime="2025-06-12T13:28:51.1426928+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="004da334-5d0c-4ed3-8716-7d28a8a368fb" />
    <UnitTestResult executionId="b2b38f9d-082b-45f1-9a2c-83932420a109" testId="4d4c87b9-bd77-56b0-acd7-7c0c49cbc08f" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WithRateLimit_ShouldEnforceTimingAccurately" computerName="JD-ITA028088-PC" duration="00:00:00.6021368" startTime="2025-06-12T13:28:51.8566156+08:00" endTime="2025-06-12T13:28:51.8566158+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b2b38f9d-082b-45f1-9a2c-83932420a109" />
    <UnitTestResult executionId="a8ad11e8-7cea-484b-a88a-165d59a61012" testId="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: ThrowException)" computerName="JD-ITA028088-PC" duration="00:00:00.0022068" startTime="2025-06-12T13:28:51.1465046+08:00" endTime="2025-06-12T13:28:51.1465047+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a8ad11e8-7cea-484b-a88a-165d59a61012" />
    <UnitTestResult executionId="4882b1f4-0ac8-42f3-90ef-d5ac08e393fd" testId="beab0b90-bded-b34e-a1b0-0889a62c7bc7" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithNullBuffer_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0010402" startTime="2025-06-12T13:28:51.1590688+08:00" endTime="2025-06-12T13:28:51.1590688+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4882b1f4-0ac8-42f3-90ef-d5ac08e393fd" />
    <UnitTestResult executionId="0361fda0-200a-4482-9cf3-d7791051e25a" testId="2660b25c-0ab3-7226-1818-3de3d93f7e8b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001391" startTime="2025-06-12T13:28:51.1039751+08:00" endTime="2025-06-12T13:28:51.1039752+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0361fda0-200a-4482-9cf3-d7791051e25a" />
    <UnitTestResult executionId="6b2046c0-85a2-4643-b100-66b095c332a0" testId="d796aa36-66a9-9e1e-199e-e7f8579e7312" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000077" startTime="2025-06-12T13:28:51.1135972+08:00" endTime="2025-06-12T13:28:51.1135974+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6b2046c0-85a2-4643-b100-66b095c332a0" />
    <UnitTestResult executionId="dc00f163-f17c-423a-a4c6-6c6b9d44f75b" testId="5a625eaf-ce33-5cc0-168a-ac8f3f113fa3" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002708" startTime="2025-06-12T13:28:51.1244302+08:00" endTime="2025-06-12T13:28:51.1244302+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dc00f163-f17c-423a-a4c6-6c6b9d44f75b" />
    <UnitTestResult executionId="06f62de8-f7fa-49fb-9175-e18d27d99abc" testId="cac85d36-7607-899b-f9db-0914e3049caf" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAvailablePorts_ShouldReturnPortArray" computerName="JD-ITA028088-PC" duration="00:00:00.0003785" startTime="2025-06-12T13:28:51.1687635+08:00" endTime="2025-06-12T13:28:51.1687635+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="06f62de8-f7fa-49fb-9175-e18d27d99abc" />
    <UnitTestResult executionId="e379bf2f-5dc1-41ab-9a8a-82ef24e023a7" testId="7c790003-2d63-4b93-840e-28312682c36d" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;&quot;, expectedText: &quot;&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0004362" startTime="2025-06-12T13:28:51.1369163+08:00" endTime="2025-06-12T13:28:51.1369164+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e379bf2f-5dc1-41ab-9a8a-82ef24e023a7" />
    <UnitTestResult executionId="1716f3df-9103-4cc8-a508-560f6504a2d1" testId="db250e33-9c97-4756-436a-16d6e8cf414f" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 0, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0003462" startTime="2025-06-12T13:28:51.1074676+08:00" endTime="2025-06-12T13:28:51.1074676+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1716f3df-9103-4cc8-a508-560f6504a2d1" />
    <UnitTestResult executionId="7971baf9-36eb-4180-949e-3435cbc38b32" testId="06b280c7-f3e9-b8e7-377e-97f90aa891c1" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithNonExistentPort_ShouldReturnNull" computerName="JD-ITA028088-PC" duration="00:00:00.0001387" startTime="2025-06-12T13:28:51.1694579+08:00" endTime="2025-06-12T13:28:51.1694580+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7971baf9-36eb-4180-949e-3435cbc38b32" />
    <UnitTestResult executionId="8ae0a0f5-6735-4f26-9d0d-aaa93a2d2f33" testId="1b9f49aa-b9ff-fd05-f03b-43ccb23d82e6" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0012344" startTime="2025-06-12T13:28:51.1283760+08:00" endTime="2025-06-12T13:28:51.1283760+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8ae0a0f5-6735-4f26-9d0d-aaa93a2d2f33" />
    <UnitTestResult executionId="13fb118d-878d-4ff9-8a2d-d6cb32cc65ab" testId="14978f86-a584-09a6-4ff2-659b440bf384" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConcurrentOperations_ShouldHandleParallelRequests" computerName="JD-ITA028088-PC" duration="00:00:00.0051523" startTime="2025-06-12T13:28:51.2625027+08:00" endTime="2025-06-12T13:28:51.2625029+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="13fb118d-878d-4ff9-8a2d-d6cb32cc65ab" />
    <UnitTestResult executionId="856a7197-a784-4d05-9482-d438fd6d48ca" testId="66753aa0-12f4-9a94-0af2-e1d64e8dae4a" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_HighFrequencyOperations_ShouldMaintainPerformance" computerName="JD-ITA028088-PC" duration="00:00:00.0109128" startTime="2025-06-12T13:28:52.5760696+08:00" endTime="2025-06-12T13:28:52.5760697+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="856a7197-a784-4d05-9482-d438fd6d48ca" />
    <UnitTestResult executionId="8262aa92-33a9-47f9-add1-9b357b999643" testId="4e851d5d-05a7-2eac-92f0-8bfc94b85dc6" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 4096)" computerName="JD-ITA028088-PC" duration="00:00:00.0753677" startTime="2025-06-12T13:28:51.1606874+08:00" endTime="2025-06-12T13:28:51.1606874+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8262aa92-33a9-47f9-add1-9b357b999643" />
    <UnitTestResult executionId="f13d4551-8316-4083-8e89-17e52b888c34" testId="89904fc7-f945-148c-427f-28dd272d907e" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXoffData_ShouldPauseFlow" computerName="JD-ITA028088-PC" duration="00:00:00.0001594" startTime="2025-06-12T13:28:51.1496386+08:00" endTime="2025-06-12T13:28:51.1496387+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f13d4551-8316-4083-8e89-17e52b888c34" />
    <UnitTestResult executionId="b874fd75-c40f-4171-bf4f-cc3e0203f906" testId="b777246c-f407-ff52-0009-26e9cffd3bc2" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0114620" startTime="2025-06-12T13:28:51.0722401+08:00" endTime="2025-06-12T13:28:51.0722630+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b874fd75-c40f-4171-bf4f-cc3e0203f906" />
    <UnitTestResult executionId="236c29c6-af4d-4f80-b603-bfaced854f7f" testId="a17ba197-677f-433a-d98b-90b6dba91665" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: None)" computerName="JD-ITA028088-PC" duration="00:00:00.0002530" startTime="2025-06-12T13:28:51.1483924+08:00" endTime="2025-06-12T13:28:51.1483924+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="236c29c6-af4d-4f80-b603-bfaced854f7f" />
    <UnitTestResult executionId="54ac636a-76ea-4aa8-ac62-59ca6295cdfc" testId="8caa8b5b-b84c-d1d7-e2b2-05bff9553bb9" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_FrequentCalls_ShouldNotImpactPerformance" computerName="JD-ITA028088-PC" duration="00:00:00.0009086" startTime="2025-06-12T13:28:52.5770702+08:00" endTime="2025-06-12T13:28:52.5770703+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="54ac636a-76ea-4aa8-ac62-59ca6295cdfc" />
    <UnitTestResult executionId="d5f59d92-725d-483e-85d2-8c66dde92f40" testId="8d9dd9a9-c289-1130-7c61-b67125a5dbe3" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\ud83d\ude80\ud83d\udd25\ud83d\udcbb&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0009967" startTime="2025-06-12T13:28:51.2582509+08:00" endTime="2025-06-12T13:28:51.2582511+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d5f59d92-725d-483e-85d2-8c66dde92f40" />
    <UnitTestResult executionId="435b4082-692b-469f-9e74-8e6076616f58" testId="5bacd699-c1c3-a30f-c688-788c48f5fa12" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_AdvancedBuffering_ShouldConfigureCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0019591" startTime="2025-06-12T13:28:51.2645684+08:00" endTime="2025-06-12T13:28:51.2645685+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="435b4082-692b-469f-9e74-8e6076616f58" />
    <UnitTestResult executionId="65858a03-ba53-4f09-8434-1a3683e6aa77" testId="29cc443b-5843-75d0-329d-fdd89478ba0f" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithNullData_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0005031" startTime="2025-06-12T13:28:52.9610674+08:00" endTime="2025-06-12T13:28:52.9610675+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="65858a03-ba53-4f09-8434-1a3683e6aa77" />
    <UnitTestResult executionId="71c524bd-8402-4ef0-bfda-b250c344a5f4" testId="ac78eccc-7e0e-a33e-3154-a1d2a9e455e3" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.SerialPortConfigurationException_ShouldInheritFromSerialPortException" computerName="JD-ITA028088-PC" duration="00:00:00.0009255" startTime="2025-06-12T13:28:51.1096319+08:00" endTime="2025-06-12T13:28:51.1096320+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="71c524bd-8402-4ef0-bfda-b250c344a5f4" />
    <UnitTestResult executionId="cb954fd6-e22b-4e21-9114-335e8d0fc307" testId="f60c8033-5a87-0f3a-3088-48468fd9301b" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0003462" startTime="2025-06-12T13:28:51.1267094+08:00" endTime="2025-06-12T13:28:51.1267094+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cb954fd6-e22b-4e21-9114-335e8d0fc307" />
    <UnitTestResult executionId="a9f8007a-3938-499a-b9e4-adf6f220cf50" testId="edf58ff9-246f-e14c-f25f-39068185472a" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithOptions_ShouldRegisterServicesAndOptions" computerName="JD-ITA028088-PC" duration="00:00:00.0339753" startTime="2025-06-12T13:28:51.1312609+08:00" endTime="2025-06-12T13:28:51.1312609+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a9f8007a-3938-499a-b9e4-adf6f220cf50" />
    <UnitTestResult executionId="54107aa8-b055-40f6-9943-450babf218b8" testId="f7c5e95f-be71-08d0-bb43-352b994d7d2c" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueDequeue_ConcurrentOperations_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.1009009" startTime="2025-06-12T13:28:51.4580264+08:00" endTime="2025-06-12T13:28:51.4580266+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="54107aa8-b055-40f6-9943-450babf218b8" />
    <UnitTestResult executionId="cbbc7a44-7da1-4587-b383-863e172349f4" testId="c828ecfa-33bf-6434-f923-b9cfd77032bc" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;COM999&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0019651" startTime="2025-06-12T13:28:51.7490473+08:00" endTime="2025-06-12T13:28:51.7490474+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cbbc7a44-7da1-4587-b383-863e172349f4" />
    <UnitTestResult executionId="bab80e04-23e1-4d34-b316-21d378dc088f" testId="a70006d6-2422-b7b2-466f-65fda27fd805" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithDataInQueue_ShouldReturnData" computerName="JD-ITA028088-PC" duration="00:00:00.0004387" startTime="2025-06-12T13:28:51.3569853+08:00" endTime="2025-06-12T13:28:51.3569854+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bab80e04-23e1-4d34-b316-21d378dc088f" />
    <UnitTestResult executionId="ab6d0263-d868-4d43-b673-075fb2f37bb3" testId="cb5b6170-9791-0b05-807b-fb298b58c5d8" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WhenDisabled_ShouldNotProcess" computerName="JD-ITA028088-PC" duration="00:00:00.0006593" startTime="2025-06-12T13:28:51.1650810+08:00" endTime="2025-06-12T13:28:51.1650812+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ab6d0263-d868-4d43-b673-075fb2f37bb3" />
    <UnitTestResult executionId="a1f7083d-4f9a-4a60-83e9-864d6ba466d1" testId="acb3181c-ee8d-5c46-618c-09ac65f019a4" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullLogger_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0009018" startTime="2025-06-12T13:28:51.1396459+08:00" endTime="2025-06-12T13:28:51.1396460+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a1f7083d-4f9a-4a60-83e9-864d6ba466d1" />
    <UnitTestResult executionId="123b1d91-9bc8-40df-8ed7-a743152d9c8d" testId="74f91c1b-75f8-0352-e93b-da086f3f05ba" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 17, 19])" computerName="JD-ITA028088-PC" duration="00:00:00.0003401" startTime="2025-06-12T13:28:51.1518258+08:00" endTime="2025-06-12T13:28:51.1518259+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="123b1d91-9bc8-40df-8ed7-a743152d9c8d" />
    <UnitTestResult executionId="ff135a22-b5e4-4120-85a2-4125d045f933" testId="a0d6040b-1605-8c04-788d-3eef62fbd2d3" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 1000)" computerName="JD-ITA028088-PC" duration="00:00:00.0002021" startTime="2025-06-12T13:28:51.1525332+08:00" endTime="2025-06-12T13:28:51.1525333+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ff135a22-b5e4-4120-85a2-4125d045f933" />
    <UnitTestResult executionId="a167a5e3-547c-4190-8c17-908649edfc45" testId="c92fb4d1-98fd-ec97-499d-551eebba0847" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.ContainsPort_WithExistingPort_ShouldReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0003006" startTime="2025-06-12T13:28:51.1685451+08:00" endTime="2025-06-12T13:28:51.1685452+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a167a5e3-547c-4190-8c17-908649edfc45" />
    <UnitTestResult executionId="a13c6255-bd64-438a-908d-917eef4346d3" testId="03bac210-5121-4552-6b4a-8ad9c8a4430a" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0007735" startTime="2025-06-12T13:28:52.5660072+08:00" endTime="2025-06-12T13:28:52.5660080+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a13c6255-bd64-438a-908d-917eef4346d3" />
    <UnitTestResult executionId="a3f52aad-dafb-4ab8-8310-166a05c0cc4d" testId="2f010a59-e386-1c48-7a96-6eaf300b4a00" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.GetDataCopy_ShouldReturnIndependentCopy" computerName="JD-ITA028088-PC" duration="00:00:00.0015423" startTime="2025-06-12T13:28:51.1384502+08:00" endTime="2025-06-12T13:28:51.1384502+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a3f52aad-dafb-4ab8-8310-166a05c0cc4d" />
    <UnitTestResult executionId="3517ac61-c3e2-479f-a17d-3192098a8ca5" testId="d72a782f-3fee-0055-ced6-0e57470253f9" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearDataQueue_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0013738" startTime="2025-06-12T13:28:51.1430986+08:00" endTime="2025-06-12T13:28:51.1430987+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3517ac61-c3e2-479f-a17d-3192098a8ca5" />
    <UnitTestResult executionId="b42c2cf0-b170-4f25-9e86-4b0e360f5495" testId="3154c378-cb14-2e6f-72c2-17b34c4e8da5" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConcurrentAccess_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.0026644" startTime="2025-06-12T13:28:51.9157082+08:00" endTime="2025-06-12T13:28:51.9157095+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b42c2cf0-b170-4f25-9e86-4b0e360f5495" />
    <UnitTestResult executionId="21a971ed-ba0a-457e-a36b-64a0fceabdd1" testId="2b64ca9d-2218-a1bd-b07d-cbcc8dde3d16" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 8, shouldTriggerWarning: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000600" startTime="2025-06-12T13:28:51.1558787+08:00" endTime="2025-06-12T13:28:51.1558787+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="21a971ed-ba0a-457e-a36b-64a0fceabdd1" />
    <UnitTestResult executionId="b86d42a9-56c5-46a6-a7d2-606fcea76d22" testId="befe512a-b39c-f7de-f215-29ab09232596" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002295" startTime="2025-06-12T13:28:51.1263537+08:00" endTime="2025-06-12T13:28:51.1263538+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b86d42a9-56c5-46a6-a7d2-606fcea76d22" />
    <UnitTestResult executionId="faf204e2-d0b2-4967-aec7-7f5b126f8326" testId="bac682fb-bc9a-5ee1-6f8f-e6c1b404c020" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_ShouldReturnValidStatistics" computerName="JD-ITA028088-PC" duration="00:00:00.0002899" startTime="2025-06-12T13:28:51.1568957+08:00" endTime="2025-06-12T13:28:51.1568957+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="faf204e2-d0b2-4967-aec7-7f5b126f8326" />
    <UnitTestResult executionId="5d9e7d07-daf3-4d7a-b099-5c5cd25c8751" testId="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: 0, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0001873" startTime="2025-06-12T13:28:51.1175042+08:00" endTime="2025-06-12T13:28:51.1175044+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5d9e7d07-daf3-4d7a-b099-5c5cd25c8751" />
    <UnitTestResult executionId="e2ec51a3-e283-4b88-85ed-271039ff6355" testId="7356f68e-ad7b-97c4-f64c-2536928bb3c9" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 0)" computerName="JD-ITA028088-PC" duration="00:00:00.0020937" startTime="2025-06-12T13:28:51.2725496+08:00" endTime="2025-06-12T13:28:51.2725497+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e2ec51a3-e283-4b88-85ed-271039ff6355" />
    <UnitTestResult executionId="9a4b964d-9098-46eb-80ca-3f61b7739556" testId="55695b9c-8fa7-75b9-c4de-aa77143fb3c0" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse" computerName="JD-ITA028088-PC" duration="00:00:00.0001462" startTime="2025-06-12T13:28:51.1726746+08:00" endTime="2025-06-12T13:28:51.1726746+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9a4b964d-9098-46eb-80ca-3f61b7739556" />
    <UnitTestResult executionId="ea304460-f273-4447-8cc0-ccd625a0ff50" testId="4ba3dac7-6a62-cb49-022a-20a7d4d37087" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\r\n&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0012403" startTime="2025-06-12T13:28:51.2559593+08:00" endTime="2025-06-12T13:28:51.2559594+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ea304460-f273-4447-8cc0-ccd625a0ff50" />
    <UnitTestResult executionId="f10df243-2e9a-4378-a3e1-f256099c8356" testId="ed73d661-31f4-564a-3bca-e7bd1da12bc7" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [72, 101, 108, 108, 111], expectedHex: &quot;48 65 6C 6C 6F&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000183" startTime="2025-06-12T13:28:51.1262474+08:00" endTime="2025-06-12T13:28:51.1262475+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f10df243-2e9a-4378-a3e1-f256099c8356" />
    <UnitTestResult executionId="c0dd221e-c429-4ca9-8bb2-29a5ae4fd146" testId="65c4cae6-02b9-cdd9-6a8b-d3eb8e0242f1" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0019339" startTime="2025-06-12T13:28:51.1403363+08:00" endTime="2025-06-12T13:28:51.1403364+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c0dd221e-c429-4ca9-8bb2-29a5ae4fd146" />
    <UnitTestResult executionId="596ed513-f9f2-4941-a47d-1ae1917a4898" testId="d3314a3a-784e-50f7-5f7e-7145efc09018" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithBufferOverflow_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0014461" startTime="2025-06-12T13:28:51.1541174+08:00" endTime="2025-06-12T13:28:51.1541175+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="596ed513-f9f2-4941-a47d-1ae1917a4898" />
    <UnitTestResult executionId="d21a69d5-82cf-4bad-bfcf-49a239d2113c" testId="e33e1140-ec5a-c360-ef7c-351a69dc550b" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_DeviceRemoved_ShouldDetectAndRecover" computerName="JD-ITA028088-PC" duration="00:00:00.1027709" startTime="2025-06-12T13:28:51.9129005+08:00" endTime="2025-06-12T13:28:51.9129009+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d21a69d5-82cf-4bad-bfcf-49a239d2113c" />
    <UnitTestResult executionId="88ef615a-dfb4-4113-b398-143a01ba4419" testId="8c638f55-8f9a-3b1d-dff8-8169a30d369f" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 5, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0114994" startTime="2025-06-12T13:28:51.0818335+08:00" endTime="2025-06-12T13:28:51.0818336+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="88ef615a-dfb4-4113-b398-143a01ba4419" />
    <UnitTestResult executionId="2b75dc1b-2c4d-4040-af5b-0b3fd120f53b" testId="a33546b2-5014-1109-76f2-708037dcb465" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithText_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0006937" startTime="2025-06-12T13:28:51.1407371+08:00" endTime="2025-06-12T13:28:51.1407371+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2b75dc1b-2c4d-4040-af5b-0b3fd120f53b" />
    <UnitTestResult executionId="95e9f56d-aa8c-49de-a0fd-944792f53ca1" testId="2b557e92-a648-4516-f450-d8ea7736c72f" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-8&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0012140" startTime="2025-06-12T13:28:51.2446891+08:00" endTime="2025-06-12T13:28:51.2446892+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="95e9f56d-aa8c-49de-a0fd-944792f53ca1" />
    <UnitTestResult executionId="5bee4dc3-b042-4780-a2c3-03558bd1df59" testId="902ef6cf-b62e-58ea-9022-da49556e41c5" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0011951" startTime="2025-06-12T13:28:51.2703893+08:00" endTime="2025-06-12T13:28:51.2703895+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5bee4dc3-b042-4780-a2c3-03558bd1df59" />
    <UnitTestResult executionId="f41ea628-190b-45f5-804f-648770491ee4" testId="e3460a9b-1799-f6b6-2f2c-a356970be8b9" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenEnabled_ShouldReturnTrue" computerName="JD-ITA028088-PC" duration="00:00:00.0001510" startTime="2025-06-12T13:28:51.1666751+08:00" endTime="2025-06-12T13:28:51.1666752+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f41ea628-190b-45f5-804f-648770491ee4" />
    <UnitTestResult executionId="de3ffdc6-9302-447a-ae87-fca11e606ea2" testId="8043c734-3566-ddb7-1d4a-07a44a746bbb" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-32&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.1265073" startTime="2025-06-12T13:28:51.2429640+08:00" endTime="2025-06-12T13:28:51.2429641+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="de3ffdc6-9302-447a-ae87-fca11e606ea2" />
    <UnitTestResult executionId="dbd57971-76ce-43ac-80f7-aae087abc314" testId="ae04b4e0-cf08-4697-35c4-f48e7c5131c5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;你好&quot;, expectedText: &quot;你好&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000113" startTime="2025-06-12T13:28:51.1372976+08:00" endTime="2025-06-12T13:28:51.1372976+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="dbd57971-76ce-43ac-80f7-aae087abc314" />
    <UnitTestResult executionId="08fec686-f47f-4e92-a861-da18eaa21a04" testId="e39f649e-7c27-616e-c761-ed717edf3ede" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithNegativeLength_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0005971" startTime="2025-06-12T13:28:51.8572861+08:00" endTime="2025-06-12T13:28:51.8572862+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="08fec686-f47f-4e92-a861-da18eaa21a04" />
    <UnitTestResult executionId="ac795a2a-df3d-4b0d-8451-876a99143895" testId="d0e94aff-761a-b752-8ea1-b05e263e6baa" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" computerName="JD-ITA028088-PC" duration="00:00:00.0001998" startTime="2025-06-12T13:28:52.5776446+08:00" endTime="2025-06-12T13:28:52.5776447+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ac795a2a-df3d-4b0d-8451-876a99143895" />
    <UnitTestResult executionId="c1573627-16c7-432f-8c6b-33f0841cbed9" testId="8b2e14a6-d10e-1c5b-c949-007826a28517" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetAvailablePorts_ShouldReturnPortArray" computerName="JD-ITA028088-PC" duration="00:00:00.0018160" startTime="2025-06-12T13:28:51.1478036+08:00" endTime="2025-06-12T13:28:51.1478037+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c1573627-16c7-432f-8c6b-33f0841cbed9" />
    <UnitTestResult executionId="277a5674-6ab2-472b-a810-ee8c942c5000" testId="bc61f3c1-ffcd-b602-c10b-5bb52b1c6806" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.SerialPortConnectionException_ShouldInheritFromSerialPortException" computerName="JD-ITA028088-PC" duration="00:00:00.0011737" startTime="2025-06-12T13:28:51.1094893+08:00" endTime="2025-06-12T13:28:51.1094894+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="277a5674-6ab2-472b-a810-ee8c942c5000" />
    <UnitTestResult executionId="002c796d-f469-45d5-9194-8f49a9f9d9ff" testId="456b7034-8cd0-175d-c38d-784450059613" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 115200, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000044" startTime="2025-06-12T13:28:51.1139575+08:00" endTime="2025-06-12T13:28:51.1139576+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="002c796d-f469-45d5-9194-8f49a9f9d9ff" />
    <UnitTestResult executionId="ec149ca5-f104-4c27-8a64-3f5b4f3fac87" testId="7498a7db-5111-4360-9445-2e4763e8cd4e" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004126" startTime="2025-06-12T13:28:51.1281280+08:00" endTime="2025-06-12T13:28:51.1281281+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ec149ca5-f104-4c27-8a64-3f5b4f3fac87" />
    <UnitTestResult executionId="b1fd1d0e-b51e-40cf-90a0-036e12b513fe" testId="43a8f9d1-3c2f-114d-d7c3-2fa82d3ccdfd" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0003830" startTime="2025-06-12T13:28:51.1714466+08:00" endTime="2025-06-12T13:28:51.1714467+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b1fd1d0e-b51e-40cf-90a0-036e12b513fe" />
    <UnitTestResult executionId="71c4dece-95bd-4771-b82b-7421f15e4100" testId="4f9d435a-f0d2-5d93-e815-ec19ec4acd79" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentBufferOperations_WithEventHandlers_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.0399803" startTime="2025-06-12T13:28:52.6176629+08:00" endTime="2025-06-12T13:28:52.6176630+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="71c4dece-95bd-4771-b82b-7421f15e4100" />
    <UnitTestResult executionId="3d162973-bc99-4a7d-9baf-5fd6d98f8714" testId="fc11fc4c-e912-c68a-17af-aad770dde13c" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithNullData_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0003099" startTime="2025-06-12T13:28:51.1566896+08:00" endTime="2025-06-12T13:28:51.1566896+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3d162973-bc99-4a7d-9baf-5fd6d98f8714" />
    <UnitTestResult executionId="053b2eb4-41e0-4329-b10f-f3ed2409f2f7" testId="65132280-9071-90ad-f1d3-b2bec385e31a" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 1000)" computerName="JD-ITA028088-PC" duration="00:00:00.0010002" startTime="2025-06-12T13:28:51.7573115+08:00" endTime="2025-06-12T13:28:51.7573116+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="053b2eb4-41e0-4329-b10f-f3ed2409f2f7" />
    <UnitTestResult executionId="40919078-a161-43af-a6d4-658ca7f7a722" testId="fc3b21c3-0f8b-a760-763b-ba136c1f3355" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0000974" startTime="2025-06-12T13:28:51.1209508+08:00" endTime="2025-06-12T13:28:51.1209508+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="40919078-a161-43af-a6d4-658ca7f7a722" />
    <UnitTestResult executionId="168099a9-6e39-4818-bec3-9375f0acdfe0" testId="dee417d5-57ab-c1ce-a824-beeb5380368e" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_ContinuousOverflow_ShouldMaintainStability" computerName="JD-ITA028088-PC" duration="00:00:00.0009246" startTime="2025-06-12T13:28:51.1485693+08:00" endTime="2025-06-12T13:28:51.1485693+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="168099a9-6e39-4818-bec3-9375f0acdfe0" />
    <UnitTestResult executionId="e913fa99-79a2-42cc-a265-f665a32aec07" testId="eddea9f3-c4cb-08bd-7c5f-ec33540ba04f" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithLargeDataVolume_ShouldHandleMemoryPressure" computerName="JD-ITA028088-PC" duration="00:00:00.0005961" startTime="2025-06-12T13:28:52.9605231+08:00" endTime="2025-06-12T13:28:52.9605232+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e913fa99-79a2-42cc-a265-f665a32aec07" />
    <UnitTestResult executionId="69503e35-8d0c-410c-9790-a62ced883f0a" testId="c53401be-8da1-5d73-868a-6420974db0e4" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;-&quot;, uppercase: True, expectedHex: &quot;48-65-6C&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0002170" startTime="2025-06-12T13:28:51.1390567+08:00" endTime="2025-06-12T13:28:51.1390567+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="69503e35-8d0c-410c-9790-a62ced883f0a" />
    <UnitTestResult executionId="f4942b0c-890d-4cac-a648-9ab751950f15" testId="86618079-abf9-30c9-2492-2a4b2efcc170" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullText_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0003691" startTime="2025-06-12T13:28:51.1411309+08:00" endTime="2025-06-12T13:28:51.1411309+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f4942b0c-890d-4cac-a648-9ab751950f15" />
    <UnitTestResult executionId="59715640-d613-4669-9d1a-02aaa3f4ad02" testId="09ce9de4-34a5-edec-6b47-7870a0ac8152" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0002464" startTime="2025-06-12T13:28:51.1070753+08:00" endTime="2025-06-12T13:28:51.1070754+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="59715640-d613-4669-9d1a-02aaa3f4ad02" />
    <UnitTestResult executionId="5aee73b7-257b-4698-b3a9-217bfd9e4803" testId="5fcd1008-efe6-9593-2351-b4703f8be1f5" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" computerName="JD-ITA028088-PC" duration="00:00:00.0002931" startTime="2025-06-12T13:28:52.9613871+08:00" endTime="2025-06-12T13:28:52.9613874+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5aee73b7-257b-4698-b3a9-217bfd9e4803" />
    <UnitTestResult executionId="b3638f59-b174-4f17-ac35-931184296751" testId="c6821c0d-34ef-41c3-52bd-004793cc5855" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 100)" computerName="JD-ITA028088-PC" duration="00:00:00.1264941" startTime="2025-06-12T13:28:51.2427264+08:00" endTime="2025-06-12T13:28:51.2427265+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b3638f59-b174-4f17-ac35-931184296751" />
    <UnitTestResult executionId="38c4ab3e-6cd1-49e4-b8da-f2500b76fa14" testId="5772937f-dba3-ddd7-fc6d-303fd2ade221" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: null, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000099" startTime="2025-06-12T13:28:51.1239637+08:00" endTime="2025-06-12T13:28:51.1239638+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="38c4ab3e-6cd1-49e4-b8da-f2500b76fa14" />
    <UnitTestResult executionId="79312642-ebe0-4557-afee-293aba75d3a4" testId="40f0c48a-0bc6-7691-1bcb-bf225bd03b25" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 3000, maxAttempts: 5, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000112" startTime="2025-06-12T13:28:51.1196243+08:00" endTime="2025-06-12T13:28:51.1196244+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="79312642-ebe0-4557-afee-293aba75d3a4" />
    <UnitTestResult executionId="9887d580-9162-4e56-8989-75cb8b86c18a" testId="c029ac10-2236-0f3e-f243-4fb9689c404b" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 4, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000050" startTime="2025-06-12T13:28:51.0989835+08:00" endTime="2025-06-12T13:28:51.0989836+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9887d580-9162-4e56-8989-75cb8b86c18a" />
    <UnitTestResult executionId="1b4cfb1d-7d13-4b0f-a92b-06efb77e6640" testId="b8418e21-906c-bab7-f26a-6263d8be1dd9" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;中文测试&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0009150" startTime="2025-06-12T13:28:51.2562119+08:00" endTime="2025-06-12T13:28:51.2562119+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1b4cfb1d-7d13-4b0f-a92b-06efb77e6640" />
    <UnitTestResult executionId="2deea3dc-49a2-4144-9155-2a3a6e020abf" testId="3d8895f0-2b0a-046b-ed9b-3a2696b478f8" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterRequiredServices" computerName="JD-ITA028088-PC" duration="00:00:00.0018344" startTime="2025-06-12T13:28:51.1314749+08:00" endTime="2025-06-12T13:28:51.1314749+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2deea3dc-49a2-4144-9155-2a3a6e020abf" />
    <UnitTestResult executionId="5679179c-ed63-494e-966b-92bea6dae946" testId="c783c277-08d3-0e65-e123-287a21c8b95a" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0003061" startTime="2025-06-12T13:28:51.1182079+08:00" endTime="2025-06-12T13:28:51.1182080+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5679179c-ed63-494e-966b-92bea6dae946" />
    <UnitTestResult executionId="2f3a54d3-b0b3-49ab-b535-b62a8450e606" testId="68fa167d-968d-2375-d00a-3a724af909d8" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;:&quot;, uppercase: False, expectedHex: &quot;48:65:6c&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0002799" startTime="2025-06-12T13:28:51.1386657+08:00" endTime="2025-06-12T13:28:51.1386658+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2f3a54d3-b0b3-49ab-b535-b62a8450e606" />
    <UnitTestResult executionId="e9b3d25c-7a5f-48ed-b2e2-345ced6350a1" testId="186e3f0a-894f-97e9-d204-455dc212b0c8" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithValidConfiguration_ShouldUpdateConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0004633" startTime="2025-06-12T13:28:51.1604104+08:00" endTime="2025-06-12T13:28:51.1604106+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e9b3d25c-7a5f-48ed-b2e2-345ced6350a1" />
    <UnitTestResult executionId="4fe9fac6-b171-41db-b2fd-55150a4436e8" testId="4a656b7f-67e3-8f8d-6a98-8264a3231c8f" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 19, 17])" computerName="JD-ITA028088-PC" duration="00:00:00.0017228" startTime="2025-06-12T13:28:51.1513943+08:00" endTime="2025-06-12T13:28:51.1513944+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4fe9fac6-b171-41db-b2fd-55150a4436e8" />
    <UnitTestResult executionId="94d7a81d-42e3-41a2-a338-ee366d18cf5c" testId="374deec9-2627-2caa-434b-b1767297fddb" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Disable_ShouldSetIsEnabledToFalse" computerName="JD-ITA028088-PC" duration="00:00:00.0011585" startTime="2025-06-12T13:28:51.1428965+08:00" endTime="2025-06-12T13:28:51.1428965+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="94d7a81d-42e3-41a2-a338-ee366d18cf5c" />
    <UnitTestResult executionId="4223c3e6-e9f9-4c16-a9a9-ba4229d74a8a" testId="8b38b793-1bda-2cf1-fc52-ca85a8e00e41" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WhenResolved_ShouldCreateValidInstance" computerName="JD-ITA028088-PC" duration="00:00:00.0031032" startTime="2025-06-12T13:28:51.1335271+08:00" endTime="2025-06-12T13:28:51.1335271+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4223c3e6-e9f9-4c16-a9a9-ba4229d74a8a" />
    <UnitTestResult executionId="aa86879e-2c67-4336-81a3-8bd578a73f8f" testId="c4f03241-cdba-e70d-96b7-1a09d3cf8bfb" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 2147483647)" computerName="JD-ITA028088-PC" duration="00:00:00.0001887" startTime="2025-06-12T13:28:51.1556826+08:00" endTime="2025-06-12T13:28:51.1556826+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="aa86879e-2c67-4336-81a3-8bd578a73f8f" />
    <UnitTestResult executionId="6ecbbd78-5a7a-4784-8cb6-1fc5015a6a23" testId="69868bb1-a9df-9b47-1d6a-43a9bb280b16" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithDuplicatePortName_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0012193" startTime="2025-06-12T13:28:51.1696603+08:00" endTime="2025-06-12T13:28:51.1696604+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6ecbbd78-5a7a-4784-8cb6-1fc5015a6a23" />
    <UnitTestResult executionId="cc38378f-31dd-4efc-9f67-ad132e5c489b" testId="8927fc38-5352-5a92-ac17-c0b53db402c5" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;&quot;, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0002500" startTime="2025-06-12T13:28:51.1234699+08:00" endTime="2025-06-12T13:28:51.1234700+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cc38378f-31dd-4efc-9f67-ad132e5c489b" />
    <UnitTestResult executionId="4fe0a3fe-f465-400b-93e0-d987f77ff978" testId="5de9e3f2-f5fe-88a8-b3fb-d72657ad04d9" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\&quot;'\\&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0010783" startTime="2025-06-12T13:28:51.2594038+08:00" endTime="2025-06-12T13:28:51.2594039+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="4fe0a3fe-f465-400b-93e0-d987f77ff978" />
    <UnitTestResult executionId="9cedcbe3-c06a-4b9c-8712-04448c82ace4" testId="7f56e2f6-e85d-2d67-fab2-f0a6cfe57e1f" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithEmptyData_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0004034" startTime="2025-06-12T13:28:51.1581502+08:00" endTime="2025-06-12T13:28:51.1581503+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9cedcbe3-c06a-4b9c-8712-04448c82ace4" />
    <UnitTestResult executionId="a1f017c2-bc37-42b1-b653-62dffdd5950e" testId="2e5792b5-5830-a1a1-6907-f9e6eeb32c23" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendAsync_WithNullData_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0019335" startTime="2025-06-12T13:28:51.1584518+08:00" endTime="2025-06-12T13:28:51.1584519+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a1f017c2-bc37-42b1-b653-62dffdd5950e" />
    <UnitTestResult executionId="57cbd7dd-8302-4419-8168-f1c44c28f98c" testId="bdd00514-c674-7fbe-6276-f883dc5e0af9" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_ResourceContention_ShouldNotDeadlock" computerName="JD-ITA028088-PC" duration="00:00:00.2458766" startTime="2025-06-12T13:28:51.3564576+08:00" endTime="2025-06-12T13:28:51.3564578+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="57cbd7dd-8302-4419-8168-f1c44c28f98c" />
    <UnitTestResult executionId="cd002d67-472f-496e-865f-0c4cbb4ab0ca" testId="54219804-c561-b035-0838-e2a41e644606" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [255, 0, 171], expectedHex: &quot;FF 00 AB&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0028608" startTime="2025-06-12T13:28:51.1250860+08:00" endTime="2025-06-12T13:28:51.1250861+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cd002d67-472f-496e-865f-0c4cbb4ab0ca" />
    <UnitTestResult executionId="e73b1314-780e-4ea9-ba76-585b4371e840" testId="a094d57c-350b-c7d9-7ef4-ab145f0776e7" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 30000)" computerName="JD-ITA028088-PC" duration="00:00:00.0007565" startTime="2025-06-12T13:28:51.2692854+08:00" endTime="2025-06-12T13:28:51.2692855+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e73b1314-780e-4ea9-ba76-585b4371e840" />
    <UnitTestResult executionId="7fede1e0-bf93-4149-b3d7-652d3e317010" testId="11d85114-8736-9f44-b948-699605b5a326" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0030285" startTime="2025-06-12T13:28:51.2810712+08:00" endTime="2025-06-12T13:28:51.2810713+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7fede1e0-bf93-4149-b3d7-652d3e317010" />
    <UnitTestResult executionId="5ad454bf-094f-4664-a0fa-e0b55245716d" testId="147438f0-3f17-1c20-cfc2-e5a56643d7cc" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithValidRate_ShouldUpdateLimit" computerName="JD-ITA028088-PC" duration="00:00:00.0001146" startTime="2025-06-12T13:28:51.1658652+08:00" endTime="2025-06-12T13:28:51.1658653+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5ad454bf-094f-4664-a0fa-e0b55245716d" />
    <UnitTestResult executionId="a7bef6a0-e63e-49d0-baf3-2877d351c46e" testId="53690872-c918-73be-c68f-532030de60cd" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.SerialPortException_ShouldInheritFromException" computerName="JD-ITA028088-PC" duration="00:00:00.0002460" startTime="2025-06-12T13:28:51.1219965+08:00" endTime="2025-06-12T13:28:51.1219965+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a7bef6a0-e63e-49d0-baf3-2877d351c46e" />
    <UnitTestResult executionId="b5c49b4e-1c8e-47b5-b435-19572f80b3a2" testId="11e2ecbb-54ae-ecc5-771b-56b899b2be70" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 2147483647)" computerName="JD-ITA028088-PC" duration="00:00:00.0011142" startTime="2025-06-12T13:28:51.2688093+08:00" endTime="2025-06-12T13:28:51.2688094+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b5c49b4e-1c8e-47b5-b435-19572f80b3a2" />
    <UnitTestResult executionId="0ab0e4ae-c583-4b71-b001-86205ec10814" testId="f7c3f6f4-f082-0f10-2964-f125578d9325" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithDuplicatePortName_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0075161" startTime="2025-06-12T13:28:51.1586615+08:00" endTime="2025-06-12T13:28:51.1586616+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0ab0e4ae-c583-4b71-b001-86205ec10814" />
    <UnitTestResult executionId="8d32a697-f63a-4e4c-a378-07e8fbdfacdf" testId="be06675f-3c25-5bc8-de68-febd1ec325bb" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNullOrEmptyPortName_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0610420" startTime="2025-06-12T13:28:51.1388629+08:00" endTime="2025-06-12T13:28:51.1388629+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8d32a697-f63a-4e4c-a378-07e8fbdfacdf" />
    <UnitTestResult executionId="366e9726-c63a-45b3-b4a5-1cb3eeb77f59" testId="ef67f86c-3dd1-1171-22b1-c007547c1494" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithNullConfiguration_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0003445" startTime="2025-06-12T13:28:51.1665700+08:00" endTime="2025-06-12T13:28:51.1665701+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="366e9726-c63a-45b3-b4a5-1cb3eeb77f59" />
    <UnitTestResult executionId="21cf7d0f-1286-4d2c-af70-d35d0548cb18" testId="1ec54028-be89-592f-c08c-8fb797e62772" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: RtsCts)" computerName="JD-ITA028088-PC" duration="00:00:00.0020181" startTime="2025-06-12T13:28:51.1480060+08:00" endTime="2025-06-12T13:28:51.1480060+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="21cf7d0f-1286-4d2c-af70-d35d0548cb18" />
    <UnitTestResult executionId="24051586-8a7f-46d4-925a-01f37e5efe6b" testId="969900f1-f9c4-1336-3ebc-d975003306b5" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_ShouldReturnValidConfiguration" computerName="JD-ITA028088-PC" duration="00:00:00.0003258" startTime="2025-06-12T13:28:51.1147164+08:00" endTime="2025-06-12T13:28:51.1147164+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="24051586-8a7f-46d4-925a-01f37e5efe6b" />
    <UnitTestResult executionId="2135a337-7707-4004-ad78-b62937102a94" testId="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_ShouldReturnCorrectInformation" computerName="JD-ITA028088-PC" duration="00:00:00.0006706" startTime="2025-06-12T13:28:52.9598345+08:00" endTime="2025-06-12T13:28:52.9598348+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2135a337-7707-4004-ad78-b62937102a94" />
    <UnitTestResult executionId="14d9a404-6723-4106-b773-2cb40cd2b5cc" testId="9c726bc3-a469-3d93-f1bf-fce1b2658615" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ConcurrentStatusChanges_ShouldBeThreadSafe" computerName="JD-ITA028088-PC" duration="00:00:00.1358809" startTime="2025-06-12T13:28:51.2556805+08:00" endTime="2025-06-12T13:28:51.2556806+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="14d9a404-6723-4106-b773-2cb40cd2b5cc" />
    <UnitTestResult executionId="df76f3ad-03c5-46fe-b2f0-64b9bd9786c4" testId="f4bd691f-6e1a-eb6a-8014-5eb6cc5c57a1" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff" computerName="JD-ITA028088-PC" duration="00:00:00.0001840" startTime="2025-06-12T13:28:51.1635427+08:00" endTime="2025-06-12T13:28:51.1635428+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="df76f3ad-03c5-46fe-b2f0-64b9bd9786c4" />
    <UnitTestResult executionId="942e1254-937e-45fe-9117-d2ec39c3b0ae" testId="31b4aa6a-561a-ec27-c6d2-f1426e253b35" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithValidData_ShouldAddToQueue" computerName="JD-ITA028088-PC" duration="00:00:00.0006594" startTime="2025-06-12T13:28:51.1500305+08:00" endTime="2025-06-12T13:28:51.1500306+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="942e1254-937e-45fe-9117-d2ec39c3b0ae" />
    <UnitTestResult executionId="5420184d-4913-407a-ad79-910cb3f27f68" testId="3a22456d-b134-6108-c30d-835c62639107" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithZeroLengthData_ShouldHandleCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002479" startTime="2025-06-12T13:28:52.6179606+08:00" endTime="2025-06-12T13:28:52.6179607+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5420184d-4913-407a-ad79-910cb3f27f68" />
    <UnitTestResult executionId="3fb62bde-3ee4-4331-8019-ab2371c6ec41" testId="8e0d6f2f-fb0e-4668-6fa9-8f21c6f9155a" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0016002" startTime="2025-06-12T13:28:51.1225210+08:00" endTime="2025-06-12T13:28:51.1225211+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3fb62bde-3ee4-4331-8019-ab2371c6ec41" />
    <UnitTestResult executionId="f5298454-bf7c-4649-905e-b6a59620193d" testId="f5c5b75e-4e7c-c2d4-8d83-637641048e9e" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_UnderHighLoad_ShouldProvideAccurateData" computerName="JD-ITA028088-PC" duration="00:00:00.0652192" startTime="2025-06-12T13:28:51.1424670+08:00" endTime="2025-06-12T13:28:51.1424670+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f5298454-bf7c-4649-905e-b6a59620193d" />
    <UnitTestResult executionId="f31d4da9-ff2e-4327-8bab-3e593a205bb3" testId="c4268a3e-dbd0-ce2a-3f14-43313d1251f3" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0001161" startTime="2025-06-12T13:28:51.1214322+08:00" endTime="2025-06-12T13:28:51.1214323+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f31d4da9-ff2e-4327-8bab-3e593a205bb3" />
    <UnitTestResult executionId="b803f819-7453-492b-9bb2-a33756d49939" testId="ed1c132d-d00f-bdaf-6b94-61ad7a3557e9" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 100000)" computerName="JD-ITA028088-PC" duration="00:00:00.0648557" startTime="2025-06-12T13:28:51.1412393+08:00" endTime="2025-06-12T13:28:51.1412393+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b803f819-7453-492b-9bb2-a33756d49939" />
    <UnitTestResult executionId="0ab4eff7-c64c-4c96-a69e-c30050869cc9" testId="fcb4374f-81fa-4153-7c7a-49168b68b53b" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;&quot;, uppercase: True, expectedHex: &quot;48656C&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0000135" startTime="2025-06-12T13:28:51.1392467+08:00" endTime="2025-06-12T13:28:51.1392467+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0ab4eff7-c64c-4c96-a69e-c30050869cc9" />
    <UnitTestResult executionId="0218deab-57bc-4b46-a68a-722b0a753224" testId="d42f65dc-b483-693d-547e-03998b4ce142" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM2&quot;, expected: True)" computerName="JD-ITA028088-PC" duration="00:00:00.0000164" startTime="2025-06-12T13:28:51.1237180+08:00" endTime="2025-06-12T13:28:51.1237181+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0218deab-57bc-4b46-a68a-722b0a753224" />
    <UnitTestResult executionId="0e86de02-514a-4c74-b00d-b91e2cf04993" testId="ca5223c1-4903-c112-ec50-72cd8a818e1b" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 4096)" computerName="JD-ITA028088-PC" duration="00:00:00.0014922" startTime="2025-06-12T13:28:51.2760064+08:00" endTime="2025-06-12T13:28:51.2760065+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="0e86de02-514a-4c74-b00d-b91e2cf04993" />
    <UnitTestResult executionId="456c9b97-59fe-403c-8146-b5675e6f85a1" testId="4f700146-33f1-ec69-f82a-0bacec577d1e" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_LongRunningOperations_ShouldNotLeakMemory" computerName="JD-ITA028088-PC" duration="00:00:00.3405198" startTime="2025-06-12T13:28:52.9588255+08:00" endTime="2025-06-12T13:28:52.9588258+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="456c9b97-59fe-403c-8146-b5675e6f85a1" />
    <UnitTestResult executionId="cd317320-8601-42e2-bf47-66d7310b229b" testId="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48-65-6C-6C-6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0001134" startTime="2025-06-12T13:28:51.1363201+08:00" endTime="2025-06-12T13:28:51.1363201+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cd317320-8601-42e2-bf47-66d7310b229b" />
    <UnitTestResult executionId="5ddb6b77-7b75-42ee-ba2e-2203aa85dd75" testId="ee05eb54-5290-18f9-2d55-e832c1a8888d" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0004257" startTime="2025-06-12T13:28:51.1307135+08:00" endTime="2025-06-12T13:28:51.1307136+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="5ddb6b77-7b75-42ee-ba2e-2203aa85dd75" />
    <UnitTestResult executionId="03e8e3ff-b244-4b38-85c3-0a488c20cc8b" testId="387aec6f-b8f3-0719-a44f-14efd8e3f6b0" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_CalledMultipleTimes_ShouldNotDuplicateServices" computerName="JD-ITA028088-PC" duration="00:00:00.0005939" startTime="2025-06-12T13:28:51.1329466+08:00" endTime="2025-06-12T13:28:51.1329467+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="03e8e3ff-b244-4b38-85c3-0a488c20cc8b" />
    <UnitTestResult executionId="d5676689-d1fa-4acf-afbf-43e1c86b23a6" testId="e7971ba4-b797-4f77-bdc8-05995b20ff35" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 262144)" computerName="JD-ITA028088-PC" duration="00:00:00.0017269" startTime="2025-06-12T13:28:51.2496307+08:00" endTime="2025-06-12T13:28:51.2496309+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d5676689-d1fa-4acf-afbf-43e1c86b23a6" />
    <UnitTestResult executionId="7a92ebcf-8e2c-41e1-89c3-1f6ff4865c41" testId="0a76b3a1-8066-016c-7b64-ba09c55edde9" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithoutLogging_ShouldStillWork" computerName="JD-ITA028088-PC" duration="00:00:00.0004747" startTime="2025-06-12T13:28:51.1347624+08:00" endTime="2025-06-12T13:28:51.1347624+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7a92ebcf-8e2c-41e1-89c3-1f6ff4865c41" />
    <UnitTestResult executionId="839ebac8-74fe-4233-ae16-09704387a8c1" testId="62c90d57-0073-79e7-453d-174a3cf7a9c9" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort" computerName="JD-ITA028088-PC" duration="00:00:00.0003151" startTime="2025-06-12T13:28:51.1724837+08:00" endTime="2025-06-12T13:28:51.1724837+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="839ebac8-74fe-4233-ae16-09704387a8c1" />
    <UnitTestResult executionId="a90549ac-6ab1-4c20-913d-6197aea08c55" testId="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48656C6C6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" computerName="JD-ITA028088-PC" duration="00:00:00.0058455" startTime="2025-06-12T13:28:51.1365222+08:00" endTime="2025-06-12T13:28:51.1365222+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a90549ac-6ab1-4c20-913d-6197aea08c55" />
    <UnitTestResult executionId="f1459385-fa3b-43a1-a29b-9ab0fcc604c9" testId="d864081d-48a6-ba7d-caf4-2925abe987a3" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToWrite_WhenNotConnected_ShouldReturnZero" computerName="JD-ITA028088-PC" duration="00:00:00.0002435" startTime="2025-06-12T13:28:51.1661184+08:00" endTime="2025-06-12T13:28:51.1661185+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f1459385-fa3b-43a1-a29b-9ab0fcc604c9" />
    <UnitTestResult executionId="d82d7e64-ddd0-451c-be61-e57b887e29e7" testId="f543d2e9-4ced-9d03-4307-68823ad67ec5" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithByteArray_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0011101" startTime="2025-06-12T13:28:51.1405459+08:00" endTime="2025-06-12T13:28:51.1405459+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d82d7e64-ddd0-451c-be61-e57b887e29e7" />
    <UnitTestResult executionId="f9eb8fff-1db1-46b7-8ce6-02a963f7c8c1" testId="330fed7e-e885-a7aa-44dc-e8f169ebddaf" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 100)" computerName="JD-ITA028088-PC" duration="00:00:00.0023352" startTime="2025-06-12T13:28:51.2670871+08:00" endTime="2025-06-12T13:28:51.2670872+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f9eb8fff-1db1-46b7-8ce6-02a963f7c8c1" />
    <UnitTestResult executionId="3e233ac1-9f60-46ea-9319-8ee5122d0c79" testId="e835e9bc-7afc-0744-d083-42c92aa262b2" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0004579" startTime="2025-06-12T13:28:51.1303621+08:00" endTime="2025-06-12T13:28:51.1303621+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3e233ac1-9f60-46ea-9319-8ee5122d0c79" />
    <UnitTestResult executionId="81c9296d-c5c0-4f41-9878-bfca472ba128" testId="b8844c7c-ca09-4104-11ef-326b5bcd433f" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0005099" startTime="2025-06-12T13:28:51.1306032+08:00" endTime="2025-06-12T13:28:51.1306032+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="81c9296d-c5c0-4f41-9878-bfca472ba128" />
    <UnitTestResult executionId="a0471f0c-bcf7-4216-b789-2a2763cf8982" testId="33597ab1-9d34-65c5-b0a1-f1a694190e93" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: -1, maxAttempts: 0, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0000149" startTime="2025-06-12T13:28:51.1193741+08:00" endTime="2025-06-12T13:28:51.1193742+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a0471f0c-bcf7-4216-b789-2a2763cf8982" />
    <UnitTestResult executionId="64df28b5-af1f-4dae-beb0-dbddbda3fd53" testId="e95c7136-7996-a786-e59e-522991193040" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterServicesWithCorrectLifetime" computerName="JD-ITA028088-PC" duration="00:00:00.0009478" startTime="2025-06-12T13:28:51.1342598+08:00" endTime="2025-06-12T13:28:51.1342598+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="64df28b5-af1f-4dae-beb0-dbddbda3fd53" />
    <UnitTestResult executionId="7b5f71ae-9eb4-4c66-ada1-dc705ac90313" testId="011894a4-ce24-7e54-f1d5-7f0ac286b44b" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.QueueUsagePercentage_ShouldCalculateCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0003267" startTime="2025-06-12T13:28:52.5664839+08:00" endTime="2025-06-12T13:28:52.5664839+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="7b5f71ae-9eb4-4c66-ada1-dc705ac90313" />
    <UnitTestResult executionId="c1942e08-bf50-40fb-91cb-97ae5191a149" testId="35f2d5bf-cf12-732f-0809-1a84cafae57a" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;&quot;, expectedBytes: [])" computerName="JD-ITA028088-PC" duration="00:00:00.0002382" startTime="2025-06-12T13:28:51.1351793+08:00" endTime="2025-06-12T13:28:51.1351794+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c1942e08-bf50-40fb-91cb-97ae5191a149" />
    <UnitTestResult executionId="6750ba6b-d1d8-421b-bf13-3df008f5dc5b" testId="5cd769ba-e210-24e0-902c-6f839ac6a5e1" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_HighLoadOperations_ShouldMaintainReasonableCpuUsage" computerName="JD-ITA028088-PC" duration="00:00:01.0036471" startTime="2025-06-12T13:28:52.5638522+08:00" endTime="2025-06-12T13:28:52.5638524+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6750ba6b-d1d8-421b-bf13-3df008f5dc5b" />
    <UnitTestResult executionId="ea985ef5-c935-4ffc-839e-df416cf26f85" testId="08289273-2527-c675-4a6a-0bcbc2ae148c" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 2)" computerName="JD-ITA028088-PC" duration="00:00:00.0009120" startTime="2025-06-12T13:28:51.2735506+08:00" endTime="2025-06-12T13:28:51.2735507+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="ea985ef5-c935-4ffc-839e-df416cf26f85" />
    <UnitTestResult executionId="3562bc63-5af3-4a74-a605-10d4e8dce297" testId="a81de412-4838-6623-61fa-67ddc3188238" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;Hello&quot;, expectedText: &quot;Hello&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0001918" startTime="2025-06-12T13:28:51.1371096+08:00" endTime="2025-06-12T13:28:51.1371097+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="3562bc63-5af3-4a74-a605-10d4e8dce297" />
    <UnitTestResult executionId="87014817-6c8d-413f-93bd-5c93d2357ee5" testId="99219d16-4665-2168-0835-d11459e2febf" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-16&quot;)" computerName="JD-ITA028088-PC" duration="00:00:00.0008813" startTime="2025-06-12T13:28:51.2450491+08:00" endTime="2025-06-12T13:28:51.2450492+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="87014817-6c8d-413f-93bd-5c93d2357ee5" />
    <UnitTestResult executionId="309e1113-bc06-44d2-93ac-8d360107a624" testId="10ecc65a-1d98-44c2-5793-97f0d54a583d" testName="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: -1, expected: False)" computerName="JD-ITA028088-PC" duration="00:00:00.0001090" startTime="2025-06-12T13:28:51.1088792+08:00" endTime="2025-06-12T13:28:51.1088793+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="309e1113-bc06-44d2-93ac-8d360107a624" />
    <UnitTestResult executionId="d4f3d44d-0ddd-4126-b8d1-c19ba4be7075" testId="d554c12b-5ed2-23ff-fe49-9271ab3c4030" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0129491" startTime="2025-06-12T13:28:51.0814924+08:00" endTime="2025-06-12T13:28:51.0814924+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d4f3d44d-0ddd-4126-b8d1-c19ba4be7075" />
    <UnitTestResult executionId="854e87df-ad4f-47c8-9734-35ad6faadb6e" testId="259912bc-7987-fbbd-0222-56d9037315ca" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.BufferManagement_WhenAdvancedBufferingDisabled_ShouldReturnDefaults" computerName="JD-ITA028088-PC" duration="00:00:00.0023830" startTime="2025-06-12T13:28:51.1414353+08:00" endTime="2025-06-12T13:28:51.1414354+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="854e87df-ad4f-47c8-9734-35ad6faadb6e" />
    <UnitTestResult executionId="66e70a07-ed21-45ec-b70a-57ba03fb632a" testId="eec8e6e9-5b71-12c7-7998-04c7b1e88c20" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0002669" startTime="2025-06-12T13:28:51.1222768+08:00" endTime="2025-06-12T13:28:51.1222769+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="66e70a07-ed21-45ec-b70a-57ba03fb632a" />
    <UnitTestResult executionId="e74c2ad3-dcbe-4219-9465-2375063ab271" testId="ad521c37-2834-6634-6bda-bfb366ff0094" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_Default_ShouldInitializeCorrectly" computerName="JD-ITA028088-PC" duration="00:00:00.0001318" startTime="2025-06-12T13:28:51.1295768+08:00" endTime="2025-06-12T13:28:51.1295769+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e74c2ad3-dcbe-4219-9465-2375063ab271" />
    <UnitTestResult executionId="f9d77b38-a326-4db9-86dc-948a6a9b68c8" testId="dca00415-9a9b-5e9a-5d7c-36ae82b195dc" testName="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.GlobalReconnectOptions_ShouldHaveCorrectDefaults" computerName="JD-ITA028088-PC" duration="00:00:00.0005236" startTime="2025-06-12T13:28:51.1319770+08:00" endTime="2025-06-12T13:28:51.1319770+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="f9d77b38-a326-4db9-86dc-948a6a9b68c8" />
    <UnitTestResult executionId="62484ed8-a61c-4519-8158-70fcbee2c35d" testId="66354075-61e8-8db6-93a7-d9e160dce397" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_WithCancellation_ShouldHandleGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.1013544" startTime="2025-06-12T13:28:51.5600235+08:00" endTime="2025-06-12T13:28:51.5600238+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="62484ed8-a61c-4519-8158-70fcbee2c35d" />
    <UnitTestResult executionId="2ad8ed61-f6f2-4517-abb4-6ed6e0ef9fad" testId="2b3d1d5d-a9e9-c463-03be-c93fc127b2b3" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendTextAsync_WithNullText_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0017580" startTime="2025-06-12T13:28:51.1628561+08:00" endTime="2025-06-12T13:28:51.1628561+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="2ad8ed61-f6f2-4517-abb4-6ed6e0ef9fad" />
    <UnitTestResult executionId="8b311a84-a207-458a-b5a3-77fd55d0dec9" testId="92f29400-6caf-fff2-71d7-c6b032343516" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 65536)" computerName="JD-ITA028088-PC" duration="00:00:00.0008197" startTime="2025-06-12T13:28:51.2779639+08:00" endTime="2025-06-12T13:28:51.2779640+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="8b311a84-a207-458a-b5a3-77fd55d0dec9" />
    <UnitTestResult executionId="6d590fb8-4e45-49f6-b6d6-8afaeb3bef10" testId="c199ae11-7eed-7c3a-2d66-da1a2b9b2453" testName="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ErrorHandling_ShouldHandleErrorsGracefully" computerName="JD-ITA028088-PC" duration="00:00:00.0035041" startTime="2025-06-12T13:28:51.2574490+08:00" endTime="2025-06-12T13:28:51.2574491+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="6d590fb8-4e45-49f6-b6d6-8afaeb3bef10" />
    <UnitTestResult executionId="116872a1-322e-4713-b696-e46e173933be" testId="d8f02cf7-433b-61ec-c449-566982f978ff" testName="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" computerName="JD-ITA028088-PC" duration="00:00:00.0002274" startTime="2025-06-12T13:28:51.1394366+08:00" endTime="2025-06-12T13:28:51.1394367+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="116872a1-322e-4713-b696-e46e173933be" />
    <UnitTestResult executionId="b6f91c14-4f53-420c-9389-249b03d93677" testId="40dc612e-fcad-d21a-6d8f-c2c13cc86897" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 921600)" computerName="JD-ITA028088-PC" duration="00:00:00.0007926" startTime="2025-06-12T13:28:51.2630141+08:00" endTime="2025-06-12T13:28:51.2630142+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b6f91c14-4f53-420c-9389-249b03d93677" />
    <UnitTestResult executionId="9cc44abf-80b9-4f94-8f9a-426622018fb7" testId="94919b74-043c-ac5d-87f1-0587b8f46ef7" testName="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithNullService_ShouldThrowException" computerName="JD-ITA028088-PC" duration="00:00:00.0003703" startTime="2025-06-12T13:28:51.1691650+08:00" endTime="2025-06-12T13:28:51.1691650+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="9cc44abf-80b9-4f94-8f9a-426622018fb7" />
    <UnitTestResult executionId="b35c16a4-bc05-4057-bf8b-53b8b1a5f50d" testId="a2862178-237f-01ca-e3a5-f47ceb7b9484" testName="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearReceiveBuffer_WhenNotConnected_ShouldNotThrow" computerName="JD-ITA028088-PC" duration="00:00:00.0007494" startTime="2025-06-12T13:28:51.1502229+08:00" endTime="2025-06-12T13:28:51.1502229+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="b35c16a4-bc05-4057-bf8b-53b8b1a5f50d" />
    <UnitTestResult executionId="c1caab48-ab2e-4ad8-819e-bb07f642eee7" testId="b531b8c7-1b6c-8c36-b996-64028f4f0324" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 1200)" computerName="JD-ITA028088-PC" duration="00:00:00.0007950" startTime="2025-06-12T13:28:51.2638844+08:00" endTime="2025-06-12T13:28:51.2638845+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="c1caab48-ab2e-4ad8-819e-bb07f642eee7" />
    <UnitTestResult executionId="bcaa04c6-4416-44be-b279-9047882a5b59" testId="2a2a8bb1-f3b5-0d08-191a-eec8cd12af85" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 1)" computerName="JD-ITA028088-PC" duration="00:00:00.0008092" startTime="2025-06-12T13:28:51.2744349+08:00" endTime="2025-06-12T13:28:51.2744350+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="bcaa04c6-4416-44be-b279-9047882a5b59" />
    <UnitTestResult executionId="e8cfdeba-d733-4811-9181-db61d7ae2706" testId="03e7ae61-8ff6-c0f3-9702-96401e863ad1" testName="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" computerName="JD-ITA028088-PC" duration="00:00:00.0004720" startTime="2025-06-12T13:28:51.1148784+08:00" endTime="2025-06-12T13:28:51.1148785+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e8cfdeba-d733-4811-9181-db61d7ae2706" />
    <UnitTestResult executionId="d3135893-3855-418d-a8c0-e94ffe6d14ce" testId="d915a464-2e21-829b-f426-6772ddf55055" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ClearQueue_WithDataInQueue_ShouldEmptyQueue" computerName="JD-ITA028088-PC" duration="00:00:00.0001715" startTime="2025-06-12T13:28:52.6181791+08:00" endTime="2025-06-12T13:28:52.6181791+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="d3135893-3855-418d-a8c0-e94ffe6d14ce" />
    <UnitTestResult executionId="682cbfe2-6ba2-4a28-93b2-0d10cf70fa4c" testId="c8b6c16e-78a6-4e53-1410-df1171a01492" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: -1)" computerName="JD-ITA028088-PC" duration="00:00:00.0000455" startTime="2025-06-12T13:28:51.1537324+08:00" endTime="2025-06-12T13:28:51.1537325+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="682cbfe2-6ba2-4a28-93b2-0d10cf70fa4c" />
    <UnitTestResult executionId="e901b97f-cac2-413a-8687-e9c48ea02430" testId="d205cea9-26a0-cb1a-deb8-bd301be91a7a" testName="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 0)" computerName="JD-ITA028088-PC" duration="00:00:00.0002992" startTime="2025-06-12T13:28:51.1520088+08:00" endTime="2025-06-12T13:28:51.1520088+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="e901b97f-cac2-413a-8687-e9c48ea02430" />
    <UnitTestResult executionId="1eaf5a49-ead2-4577-9091-12dabb3b9e1d" testId="520ee81f-3aa8-ca94-4ff6-979e424be6fe" testName="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 5000)" computerName="JD-ITA028088-PC" duration="00:00:00.0007888" startTime="2025-06-12T13:28:51.7587026+08:00" endTime="2025-06-12T13:28:51.7587027+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="1eaf5a49-ead2-4577-9091-12dabb3b9e1d" />
    <UnitTestResult executionId="cb230eff-db4e-43ab-a358-a3195c93bb82" testId="f9ae6db7-303a-9c26-b311-d84f4bd95208" testName="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1000000)" computerName="JD-ITA028088-PC" duration="00:00:00.0002402" startTime="2025-06-12T13:28:51.1546280+08:00" endTime="2025-06-12T13:28:51.1546280+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="cb230eff-db4e-43ab-a358-a3195c93bb82" />
    <UnitTestResult executionId="a09e4035-40d5-4596-a7f6-c789b39b17a5" testId="2759dc48-2d09-a5a8-b7c3-335908e8d07b" testName="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 131072)" computerName="JD-ITA028088-PC" duration="00:00:00.0026839" startTime="2025-06-12T13:28:51.2478019+08:00" endTime="2025-06-12T13:28:51.2478021+08:00" testType="13cdc9d9-ddb5-4fa4-a97d-d965ccfc6d4b" outcome="Passed" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" relativeResultsDirectory="a09e4035-40d5-4596-a7f6-c789b39b17a5" />
  </Results>
  <TestDefinitions>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WhenDisabled_ShouldNotProcess" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cb5b6170-9791-0b05-807b-fb298b58c5d8">
      <Execution id="ab6d0263-d868-4d43-b673-075fb2f37bb3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WhenDisabled_ShouldNotProcess" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;-&quot;, uppercase: True, expectedHex: &quot;48-65-6C&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c53401be-8da1-5d73-868a-6420974db0e4">
      <Execution id="69503e35-8d0c-410c-9790-a62ced883f0a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConfigurationUpdate_ShouldApplyCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="54caa2f7-9288-7523-076d-6fecd3569986">
      <Execution id="b8e27a8d-7e6f-4095-a619-9e6d69403772" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_ConfigurationUpdate_ShouldApplyCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM1&quot;, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="abc2d729-2b39-bae1-e7d9-85d1bd974a73">
      <Execution id="8bc32029-ca05-4803-ab10-9a719e7a0c4c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;Hello&quot;, expectedText: &quot;Hello&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a81de412-4838-6623-61fa-67ddc3188238">
      <Execution id="3562bc63-5af3-4a74-a605-10d4e8dce297" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 3000, maxAttempts: 5, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="40f0c48a-0bc6-7691-1bcb-bf225bd03b25">
      <Execution id="79312642-ebe0-4557-afee-293aba75d3a4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8e0d6f2f-fb0e-4668-6fa9-8f21c6f9155a">
      <Execution id="3fb62bde-3ee4-4331-8019-ab2371c6ec41" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;&quot;, expectedBytes: [])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="35f2d5bf-cf12-732f-0809-1a84cafae57a">
      <Execution id="c1942e08-bf50-40fb-91cb-97ae5191a149" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="586a2248-3398-cdf2-2aaf-e284666f9fc1">
      <Execution id="ea998325-802c-4092-ac73-1d7366faa88c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48656C6C6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c">
      <Execution id="a90549ac-6ab1-4c20-913d-6197aea08c55" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fa202986-794c-673e-db28-2db703cc0fc9">
      <Execution id="9b4317ef-7d09-4146-a35c-aa19092d1902" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendTextAsync_WithNullText_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2b3d1d5d-a9e9-c463-03be-c93fc127b2b3">
      <Execution id="2ad8ed61-f6f2-4517-abb4-6ed6e0ef9fad" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="SendTextAsync_WithNullText_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 9, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bfffdd04-e7b6-b0a1-e537-a650f2f9df27">
      <Execution id="3b181b05-9fba-4552-84e4-d60c3651c8d2" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 65536)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="92f29400-6caf-fff2-71d7-c6b032343516">
      <Execution id="8b311a84-a207-458a-b5a3-77fd55d0dec9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ConcurrentStatusChanges_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="9c726bc3-a469-3d93-f1bf-fce1b2658615">
      <Execution id="14d9a404-6723-4106-b773-2cb40cd2b5cc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ConcurrentStatusChanges_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 4, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c029ac10-2236-0f3e-f243-4fb9689c404b">
      <Execution id="9887d580-9162-4e56-8989-75cb8b86c18a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48 65 6C 6C 6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="de7a5bdb-03df-5bef-5375-0d9de7fb33cd">
      <Execution id="147c43d5-1fa4-4f03-8854-fc8b62b6a2c6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 262144)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e7971ba4-b797-4f77-bdc8-05995b20ff35">
      <Execution id="d5676689-d1fa-4acf-afbf-43e1c86b23a6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_LargeDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f3202fa7-6b71-5d20-4401-3d8bc9668755">
      <Execution id="afe412f9-f55e-4489-abd0-56061771ac02" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.Constructor_ShouldInitializeWithDefaultValues" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1e4bdd15-076b-9af7-45c3-530c1fecf022">
      <Execution id="01b7f89e-5363-4bbb-ae6c-907d5a235f44" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="Constructor_ShouldInitializeWithDefaultValues" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;48 65 6C 6C 6&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5dbf9991-d128-405c-e119-2c5c8fb571d8">
      <Execution id="5ff61378-6a9b-4588-a8fe-6fdc4e791607" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithInvalidHex_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.SerialPortConnectionException_ShouldInheritFromSerialPortException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bc61f3c1-ffcd-b602-c10b-5bb52b1c6806">
      <Execution id="277a5674-6ab2-472b-a810-ee8c942c5000" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="SerialPortConnectionException_ShouldInheritFromSerialPortException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="82d8705e-825e-feba-4da5-acda164c6f53">
      <Execution id="add52d9c-4e63-44e6-bbd4-ac5115bf5d0b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ErrorHandling_ShouldHandleErrorsGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c199ae11-7eed-7c3a-2d66-da1a2b9b2453">
      <Execution id="6d590fb8-4e45-49f6-b6d6-8afaeb3bef10" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_ErrorHandling_ShouldHandleErrorsGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;:&quot;, uppercase: False, expectedHex: &quot;48:65:6c&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="68fa167d-968d-2375-d00a-3a724af909d8">
      <Execution id="2f3a54d3-b0b3-49ab-b535-b62a8450e606" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearSendBuffer_WhenNotConnected_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="acca298d-8d66-e074-22dd-fdf90888635f">
      <Execution id="f9bcea77-6c5e-4ea7-aab9-eb4ddbde4863" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ClearSendBuffer_WhenNotConnected_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastAsync_WithNullData_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="05265970-e28e-f5df-e7d7-9126a86d207f">
      <Execution id="dbe9734a-4d67-4839-9f9f-e0ee4fc6aa52" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="BroadcastAsync_WithNullData_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;   &quot;, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bfdab111-1736-9094-751d-b681895868a8">
      <Execution id="348f548d-6db4-4946-b3e8-97c5ebc09019" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 1000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="65132280-9071-90ad-f1d3-b2bec385e31a">
      <Execution id="053b2eb4-41e0-4329-b10f-f3ed2409f2f7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 7, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="53a79683-6e18-bdcc-2942-0ae631602802">
      <Execution id="dc4fa79d-97af-4d71-9f7a-bd4fad276943" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="15548509-32f5-1f17-61af-b5d469801c2e">
      <Execution id="d9608f4e-6585-410d-98a5-4fccb6321341" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Constructor_WithValidParameters_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="08d3fc81-b996-b7a6-e1cd-78cabc053881">
      <Execution id="b46962e9-2c63-452d-8239-a5efddd62907" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.SendAsync_WithNullData_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2e5792b5-5830-a1a1-6907-f9e6eeb32c23">
      <Execution id="a1f017c2-bc37-42b1-b653-62dffdd5950e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="SendAsync_WithNullData_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullLogger_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="acb3181c-ee8d-5c46-618c-09ac65f019a4">
      <Execution id="a1f7083d-4f9a-4a60-83e9-864d6ba466d1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Constructor_WithNullLogger_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="03e7ae61-8ff6-c0f3-9702-96401e863ad1">
      <Execution id="e8cfdeba-d733-4811-9181-db61d7ae2706" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithNullBuffer_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="beab0b90-bded-b34e-a1b0-0889a62c7bc7">
      <Execution id="4882b1f4-0ac8-42f3-90ef-d5ac08e393fd" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithNullBuffer_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 1024, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f0dd822e-110a-9423-89f0-2a6bc0510f84">
      <Execution id="a0c4c48e-a845-454a-ba8e-4ceafdc85dc1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\r\n&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4ba3dac7-6a62-cb49-022a-20a7d4d37087">
      <Execution id="ea304460-f273-4447-8cc0-ccd625a0ff50" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ad521c37-2834-6634-6bda-bfb366ff0094">
      <Execution id="e74c2ad3-dcbe-4219-9465-2375063ab271" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="37b241c9-a28c-3630-63d5-22833c81cd74">
      <Execution id="fc8712a6-ba7f-4562-a4d8-3bb7bac7fe3b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="CreateDefault_WithEmptyPortName_ShouldReturnInvalidConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 7, shouldTriggerWarning: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="26c01f72-2f55-6301-3660-bbcccfc220bf">
      <Execution id="43e19725-a803-445e-8465-8a99aa11c356" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithValidRate_ShouldUpdateLimit" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="147438f0-3f17-1c20-cfc2-e5a56643d7cc">
      <Execution id="5ad454bf-094f-4664-a0fa-e0b55245716d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SetSendRateLimit_WithValidRate_ShouldUpdateLimit" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_HighFrequencyCalls_ShouldMaintainPerformance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2a46be50-f303-bb70-1536-236543d44d59">
      <Execution id="33dff329-eefd-40af-976f-1d7c9e051786" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_HighFrequencyCalls_ShouldMaintainPerformance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithOptions_ShouldRegisterServicesAndOptions" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="edf58ff9-246f-e14c-f25f-39068185472a">
      <Execution id="a9f8007a-3938-499a-b9e4-adf6f220cf50" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithOptions_ShouldRegisterServicesAndOptions" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ClearQueue_WithDataInQueue_ShouldEmptyQueue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d915a464-2e21-829b-f426-6772ddf55055">
      <Execution id="d3135893-3855-418d-a8c0-e94ffe6d14ce" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ClearQueue_WithDataInQueue_ShouldEmptyQueue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithValidData_ShouldAddToQueue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="31b4aa6a-561a-ec27-c6d2-f1426e253b35">
      <Execution id="942e1254-937e-45fe-9117-d2ec39c3b0ae" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithValidData_ShouldAddToQueue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2bba4be0-2bc3-6003-e0d8-d93e1c6880cb">
      <Execution id="2fa9b714-292d-4634-b91e-b67ef1b9e17c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.SerialPortDataException_ShouldInheritFromSerialPortException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b1ab4e5a-1abe-d4fc-3bce-7b8f40d0cefc">
      <Execution id="98018e43-c3f3-4d7f-946b-50d165e2025e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="SerialPortDataException_ShouldInheritFromSerialPortException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetSendRateLimit_WithNegativeRate_ShouldSetToZero" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d030f382-42f0-6302-7045-2cfd01c44112">
      <Execution id="a9c81fd1-7c7d-4477-8189-e5b34eb1097b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SetSendRateLimit_WithNegativeRate_ShouldSetToZero" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\0&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c4b6fa98-2242-c1ba-efc6-ec174c1be4e2">
      <Execution id="63ecf14e-772d-41b1-aee2-74073bbd57b8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_FrequentCalls_ShouldNotImpactPerformance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8caa8b5b-b84c-d1d7-e2b2-05bff9553bb9">
      <Execution id="54ac636a-76ea-4aa8-ac62-59ca6295cdfc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="GetStatistics_FrequentCalls_ShouldNotImpactPerformance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-16&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="99219d16-4665-2168-0835-d11459e2febf">
      <Execution id="87014817-6c8d-413f-93bd-5c93d2357ee5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.CreateDefault_ShouldReturnValidConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="969900f1-f9c4-1336-3ebc-d975003306b5">
      <Execution id="24051586-8a7f-46d4-925a-01f37e5efe6b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="CreateDefault_ShouldReturnValidConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.ContainsPort_WithExistingPort_ShouldReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c92fb4d1-98fd-ec97-499d-551eebba0847">
      <Execution id="a167a5e3-547c-4190-8c17-908649edfc45" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="ContainsPort_WithExistingPort_ShouldReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3b43f857-4c69-124a-f1e5-4526c8f72ed1">
      <Execution id="672746ff-99ba-4793-93da-3ee85364ee58" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;&quot;, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8927fc38-5352-5a92-ac17-c0b53db402c5">
      <Execution id="cc38378f-31dd-4efc-9f67-ad132e5c489b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 131072)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2759dc48-2d09-a5a8-b7c3-335908e8d07b">
      <Execution id="a09e4035-40d5-4596-a7f6-c789b39b17a5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_LargeDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\t&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="54e12c3f-6b43-5687-a842-a3d35e6a7a19">
      <Execution id="86431e36-fdf8-470f-92b0-193de547cf03" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WhenResolved_ShouldCreateValidInstance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8b38b793-1bda-2cf1-fc52-ca85a8e00e41">
      <Execution id="4223c3e6-e9f9-4c16-a9a9-ba4229d74a8a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WhenResolved_ShouldCreateValidInstance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: ThrowException)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad">
      <Execution id="a8ad11e8-7cea-484b-a88a-165d59a61012" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d796aa36-66a9-9e1e-199e-e7f8579e7312">
      <Execution id="6b2046c0-85a2-4643-b100-66b095c332a0" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [], expectedHex: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c34da281-7d78-d48e-fd36-b17e7d8c79e7">
      <Execution id="7064f45b-fe9d-48ae-88b9-b5c3e37e5fdf" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearReceiveBuffer_WhenNotConnected_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a2862178-237f-01ca-e3a5-f47ceb7b9484">
      <Execution id="b35c16a4-bc05-4057-bf8b-53b8b1a5f50d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ClearReceiveBuffer_WhenNotConnected_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c783c277-08d3-0e65-e123-287a21c8b95a">
      <Execution id="5679179c-ed63-494e-966b-92bea6dae946" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 300)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f26d999c-5247-a0ac-b5ab-848e393d4022">
      <Execution id="6c1458e6-4f42-421e-a453-d182049b2cc6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1b9f49aa-b9ff-fd05-f03b-43ccb23d82e6">
      <Execution id="8ae0a0f5-6735-4f26-9d0d-aaa93a2d2f33" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: RtsCts)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1ec54028-be89-592f-c08c-8fb797e62772">
      <Execution id="21cf7d0f-1286-4d2c-af70-d35d0548cb18" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 0, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="db250e33-9c97-4756-436a-16d6e8cf414f">
      <Execution id="1716f3df-9103-4cc8-a508-560f6504a2d1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ClearDataQueue_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d72a782f-3fee-0055-ced6-0e57470253f9">
      <Execution id="3517ac61-c3e2-479f-a17d-3192098a8ca5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ClearDataQueue_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenDisabled_ShouldReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f9be5f74-0f42-c1ca-06ce-4ff54de6dea3">
      <Execution id="c44acc57-6086-42fe-a4fd-f57f4c9e9eec" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_WhenDisabled_ShouldReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidOffset_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="746694ab-c99f-c4df-9347-d40ac323bdff">
      <Execution id="6fc05425-af77-49a3-9568-e9613286622b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithInvalidOffset_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithEmptyQueue_ShouldReturnFalse" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="df64e4db-ecba-2113-fb43-2182e90a55d5">
      <Execution id="0b372984-b1f9-4b6b-bd67-13cd603c083d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="TryDequeueData_WithEmptyQueue_ShouldReturnFalse" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ef67f86c-3dd1-1171-22b1-c007547c1494">
      <Execution id="366e9726-c63a-45b3-b4a5-1cb3eeb77f59" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Configure_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullServices_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ff8dcbb8-c854-51a3-cf7a-a1ca3b1fcc29">
      <Execution id="28439401-bc8b-47fb-a069-4af76f28506a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithNullServices_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e835e9bc-7afc-0744-d083-42c92aa262b2">
      <Execution id="3e233ac1-9f60-46ea-9319-8ee5122d0c79" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: None)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a17ba197-677f-433a-d98b-90b6dba91665">
      <Execution id="236c29c6-af4d-4f80-b603-bfaced854f7f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.GetDataCopy_ShouldReturnIndependentCopy" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2f010a59-e386-1c48-7a96-6eaf300b4a00">
      <Execution id="a3f52aad-dafb-4ab8-8310-166a05c0cc4d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="GetDataCopy_ShouldReturnIndependentCopy" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5a625eaf-ce33-5cc0-168a-ac8f3f113fa3">
      <Execution id="dc00f163-f17c-423a-a4c6-6c6b9d44f75b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [19, 19, 17])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cd2d846a-4253-c669-9172-ae3f3e9c95ea">
      <Execution id="980ca5f0-9ad2-457e-8b63-561c7b01dfa2" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="78f63ed6-a82b-1dc7-8333-4f5a85338c27">
      <Execution id="e55a6481-2379-479d-949d-7cd97d2b6a37" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.BroadcastTextAsync_WithNullText_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ea26ec81-d8c7-5fae-e953-a047af719c17">
      <Execution id="aa08870c-74a0-4845-9108-7e8e42100887" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="BroadcastTextAsync_WithNullText_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToWrite_WhenNotConnected_ShouldReturnZero" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d864081d-48a6-ba7d-caf4-2925abe987a3">
      <Execution id="f1459385-fa3b-43a1-a29b-9ab0fcc604c9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="GetBytesToWrite_WhenNotConnected_ShouldReturnZero" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: -1, maxAttempts: 0, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="33597ab1-9d34-65c5-b0a1-f1a694190e93">
      <Execution id="a0471f0c-bcf7-4216-b789-2a2763cf8982" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtMaxCapacity_ShouldHandleCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4e85762c-ae91-2b9e-76be-257c688fb9fa">
      <Execution id="66a06abc-2e9d-44fa-b53c-2eaeaf78a76d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtMaxCapacity_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cf59dfeb-fc64-c423-a638-29288238b6c4">
      <Execution id="dcc65334-d407-4ef1-9398-ad9da5ac40a4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithXonAfterXoff_ShouldResumeFlow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithoutLogging_ShouldStillWork" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0a76b3a1-8066-016c-7b64-ba09c55edde9">
      <Execution id="7a92ebcf-8e2c-41e1-89c3-1f6ff4865c41" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithoutLogging_ShouldStillWork" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 9600, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d">
      <Execution id="7166a037-ae8d-40a1-946f-a9bfa7f2a40f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 1000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a0d6040b-1605-8c04-788d-3eef62fbd2d3">
      <Execution id="ff135a22-b5e4-4120-85a2-4125d045f933" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="6c69255c-6ff2-34a3-9fdf-65012918c8be">
      <Execution id="f483fa16-2005-45e1-b2c5-60249c894262" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_UnderHighLoad_ShouldProvideAccurateData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f5c5b75e-4e7c-c2d4-8d83-637641048e9e">
      <Execution id="f5298454-bf7c-4649-905e-b6a59620193d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="GetStatistics_UnderHighLoad_ShouldProvideAccurateData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 17, 19])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="74f91c1b-75f8-0352-e93b-da086f3f05ba">
      <Execution id="123b1d91-9bc8-40df-8ed7-a743152d9c8d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c46d3460-b4d6-d480-98b9-89cb5e5d84d1">
      <Execution id="7f8c7092-e2cf-4a70-ae7c-24391b81c0c6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullMessage_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c4268a3e-dbd0-ce2a-3f14-43313d1251f3">
      <Execution id="f31d4da9-ff2e-4327-8bab-3e593a205bb3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithNullMessage_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="700480ab-f9a3-c14c-3862-c09cc3b0f4a7">
      <Execution id="156d39ce-902e-4d57-87e9-5a2b3eb75bb6" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WhenNearCapacity_ShouldTriggerWarningEvent" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0d5b3495-7932-d5af-331e-630ce28c78dd">
      <Execution id="69b78690-82a5-40c5-8a31-f5208c2de4e0" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithNullService_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="94919b74-043c-ac5d-87f1-0587b8f46ef7">
      <Execution id="9cc44abf-80b9-4f94-8f9a-426622018fb7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="AddSerialPort_WithNullService_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="023e96e9-d51b-a358-64ce-ba11d0feba6d">
      <Execution id="eebb5d20-dbf6-4e55-a8e1-28cced0f71c3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;INVALID&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4797f9c7-926d-5ab1-d952-801179405c81">
      <Execution id="85463075-0783-4067-b047-526e86f270a0" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_InvalidConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d8f02cf7-433b-61ec-c449-566982f978ff">
      <Execution id="116872a1-322e-4713-b696-e46e173933be" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithNullByteArray_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_NullAndEmptyParameters_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="eb395603-826c-2aec-bdbc-d35ee9d4654e">
      <Execution id="df9d9e58-fdc9-481c-b136-202e243aa8db" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_NullAndEmptyParameters_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 4096)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ca5223c1-4903-c112-ec50-72cd8a818e1b">
      <Execution id="0e86de02-514a-4c74-b00d-b91e2cf04993" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 921600)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="40dc612e-fcad-d21a-6d8f-c2c13cc86897">
      <Execution id="b6f91c14-4f53-420c-9389-249b03d93677" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 0)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7356f68e-ad7b-97c4-f64c-2536928bb3c9">
      <Execution id="e2ec51a3-e283-4b88-85ed-271039ff6355" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_TinyDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithEmptyData_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7f56e2f6-e85d-2d67-fab2-f0a6cfe57e1f">
      <Execution id="9cedcbe3-c06a-4b9c-8712-04448c82ace4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithEmptyData_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.GlobalReconnectOptions_ShouldHaveCorrectDefaults" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="dca00415-9a9b-5e9a-5d7c-36ae82b195dc">
      <Execution id="f9d77b38-a326-4db9-86dc-948a6a9b68c8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="GlobalReconnectOptions_ShouldHaveCorrectDefaults" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="06f82529-1ce7-16b1-604c-9f0392f7d160">
      <Execution id="2e8b7ba4-c25b-486a-97aa-6cb2ce1ae5e2" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_InvalidConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2cc2a28b-0583-c385-8580-90889ea0fa8d">
      <Execution id="a034f87e-9513-4865-99ea-7007653f5432" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.GetStatistics_ShouldReturnCorrectInformation" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e">
      <Execution id="2135a337-7707-4004-ad78-b62937102a94" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="GetStatistics_ShouldReturnCorrectInformation" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 6, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0acc92da-f9c6-47d9-c5a2-760ab204265e">
      <Execution id="01325f81-d45b-4639-9e8d-93b06d3f7c39" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithValidParameters_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fcf1ca95-bb0c-f451-5094-8b054adedf81">
      <Execution id="48704f38-56e5-48fb-89bd-da0d86b6575f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithValidParameters_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\ud83d\ude80\ud83d\udd25\ud83d\udcbb&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8d9dd9a9-c289-1130-7c61-b67125a5dbe3">
      <Execution id="d5f59d92-725d-483e-85d2-8c66dde92f40" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;\&quot;'\\&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5de9e3f2-f5fe-88a8-b3fb-d72657ad04d9">
      <Execution id="4fe0a3fe-f465-400b-93e0-d987f77ff978" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1000000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f9ae6db7-303a-9c26-b311-d84f4bd95208">
      <Execution id="cb230eff-db4e-43ab-a358-a3195c93bb82" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SendRateLimit_ExtremeValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterServicesWithCorrectLifetime" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e95c7136-7996-a786-e59e-522991193040">
      <Execution id="64df28b5-af1f-4dae-beb0-dbddbda3fd53" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_ShouldRegisterServicesWithCorrectLifetime" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 4096)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4e851d5d-05a7-2eac-92f0-8bfc94b85dc6">
      <Execution id="8262aa92-33a9-47f9-add1-9b357b999643" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="902ef6cf-b62e-58ea-9022-da49556e41c5">
      <Execution id="5bee4dc3-b042-4780-a2c3-03558bd1df59" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="03bac210-5121-4552-6b4a-8ad9c8a4430a">
      <Execution id="a13c6255-bd64-438a-908d-917eef4346d3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="Constructor_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ceb4348a-7f39-5209-f0c5-47a92f0ccf52">
      <Execution id="33203fdd-8290-4e18-8316-d739cb25f474" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithMultipleItems_ShouldReturnCorrectCount" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToString_ShouldReturnFormattedString" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a">
      <Execution id="594f8804-5ffa-41ce-a24f-3705fc620b1a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToString_ShouldReturnFormattedString" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithNegativeLength_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e39f649e-7c27-616e-c761-ed717edf3ede">
      <Execution id="08fec686-f47f-4e92-a861-da18eaa21a04" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="RecordSend_WithNegativeLength_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.TryDequeueData_WithDataInQueue_ShouldReturnData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a70006d6-2422-b7b2-466f-65fda27fd805">
      <Execution id="bab80e04-23e1-4d34-b316-21d378dc088f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="TryDequeueData_WithDataInQueue_ShouldReturnData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 1200)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b531b8c7-1b6c-8c36-b996-64028f4f0324">
      <Execution id="c1caab48-ab2e-4ad8-819e-bb07f642eee7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly(sequence: [17, 19, 17])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4a656b7f-67e3-8f8d-6a98-8264a3231c8f">
      <Execution id="4fe9fac6-b171-41db-b2fd-55150a4436e8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_ComplexSequences_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="57f89734-de5a-0489-86e3-f364df300ca2">
      <Execution id="15677d44-7fa2-4608-bbed-273fe4a276d7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b777246c-f407-ff52-0009-26e9cffd3bc2">
      <Execution id="b874fd75-c40f-4171-bf4f-cc3e0203f906" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConnectionExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f60c8033-5a87-0f3a-3088-48468fd9301b">
      <Execution id="cb954fd6-e22b-4e21-9114-335e8d0fc307" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortManager_BasicOperations_ShouldWorkCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2f29d2d9-c8a3-7a41-59a4-b408ec3f6dd9">
      <Execution id="b4b837ab-c5b9-4992-88b1-8971f07c2337" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortManager_BasicOperations_ShouldWorkCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithByteArray_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f543d2e9-4ced-9d03-4307-68823ad67ec5">
      <Execution id="d82d7e64-ddd0-451c-be61-e57b887e29e7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithByteArray_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;ASCII&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1a863009-7253-b831-45ba-4599991a1e23">
      <Execution id="cb6076f8-7296-47ca-94ef-38396ad9c6ce" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithInvalidHex_ShouldThrowException(invalidHex: &quot;GG&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0e388063-0d1d-fee8-e535-f6ea426ca6a0">
      <Execution id="ceeae204-bfe1-473f-a87b-b78491b735ae" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithInvalidHex_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly(strategy: DropNewest)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ccb6e43b-ed87-41fb-aacf-8532bac21ec1">
      <Execution id="3b98bd82-8f26-4ecd-b8d6-9bea56c0164e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithDifferentOverflowStrategies_ShouldBehaveCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_SpecialCharacters_ShouldHandleCorrectly(testText: &quot;中文测试&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b8418e21-906c-bab7-f26a-6263d8be1dd9">
      <Execution id="1b4cfb1d-7d13-4b0f-a92b-06efb77e6640" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_SpecialCharacters_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_MixedWithRegularData_ShouldExtractControlCharsCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="35ae527b-65f4-e761-c5e9-42b376ef79c1">
      <Execution id="4de4d1d4-c3c5-4d3e-87d0-89b61329cce5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_MixedWithRegularData_ShouldExtractControlCharsCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 5000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="520ee81f-3aa8-ca94-4ff6-979e424be6fe">
      <Execution id="1eaf5a49-ead2-4577-9091-12dabb3b9e1d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="62c90d57-0073-79e7-453d-174a3cf7a9c9">
      <Execution id="839ebac8-74fe-4233-ae16-09704387a8c1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="CreateSerialPort_WithValidConfiguration_ShouldCreateAndAddPort" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_HighFrequencyOperations_ShouldMaintainPerformance" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="66753aa0-12f4-9a94-0af2-e1d64e8dae4a">
      <Execution id="856a7197-a784-4d05-9482-d438fd6d48ca" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_HighFrequencyOperations_ShouldMaintainPerformance" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 100)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="330fed7e-e885-a7aa-44dc-e8f169ebddaf">
      <Execution id="f9eb8fff-1db1-46b7-8ce6-02a963f7c8c1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: null, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5772937f-dba3-ddd7-fc6d-303fd2ade221">
      <Execution id="38c4ab3e-6cd1-49e4-b8da-f2500b76fa14" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithLargeDataVolume_ShouldHandleMemoryPressure" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="eddea9f3-c4cb-08bd-7c5f-ec33540ba04f">
      <Execution id="e913fa99-79a2-42cc-a265-f665a32aec07" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithLargeDataVolume_ShouldHandleMemoryPressure" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings(timeoutMs: 100)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="db95ccd0-c126-7f48-a28b-a633c62602b2">
      <Execution id="fd487815-0c6c-4623-ade8-aa2bd6049305" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_TimeoutHandling_ShouldRespectTimeoutSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="09ce9de4-34a5-edec-6b47-7870a0ac8152">
      <Execution id="59715640-d613-4669-9d1a-02aaa3f4ad02" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Dispose_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f4fbaad3-6781-c8aa-cfe1-bc48e5a2111b">
      <Execution id="745578c4-57a7-44ce-9f64-cd69ecc95c30" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Dispose_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 8, shouldTriggerWarning: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2b64ca9d-2218-a1bd-b07d-cbcc8dde3d16">
      <Execution id="21a971ed-ba0a-457e-a36b-64a0fceabdd1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549">
      <Execution id="a3e99d5d-f5dd-4dd6-9499-4ffb83d90be7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithNullOrWhiteSpace_ShouldReturnEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_CalledMultipleTimes_ShouldNotDuplicateServices" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="387aec6f-b8f3-0719-a44f-14efd8e3f6b0">
      <Execution id="03e8e3ff-b244-4b38-85c3-0a488c20cc8b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_CalledMultipleTimes_ShouldNotDuplicateServices" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 1000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3da8ccb5-36b2-5b31-1537-436bec0c05cc">
      <Execution id="004da334-5d0c-4ed3-8716-7d28a8a368fb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d554c12b-5ed2-23ff-fe49-9271ab3c4030">
      <Execution id="d4f3d44d-0ddd-4126-b8d1-c19ba4be7075" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7498a7db-5111-4360-9445-2e4763e8cd4e">
      <Execution id="ec149ca5-f104-4c27-8a64-3f5b4f3fac87" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithPortNameAndMessage_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentBufferOperations_WithEventHandlers_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4f9d435a-f0d2-5d93-e815-ec19ec4acd79">
      <Execution id="71c4dece-95bd-4771-b82b-7421f15e4100" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ConcurrentBufferOperations_WithEventHandlers_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SetRtsFlowControl_ConcurrentOperations_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="28df04bd-39fc-1cce-6c87-14cb33ebc5ed">
      <Execution id="2398c596-00b2-4a2c-818e-573fa8fb4361" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SetRtsFlowControl_ConcurrentOperations_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_RapidConnectDisconnect_ShouldHandleStably" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="97ad4d87-64e8-3d1f-2000-33d34d4e461d">
      <Execution id="3d4f7dd1-9ad2-498c-ae6a-a291aed1d1fb" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_RapidConnectDisconnect_ShouldHandleStably" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.QueueUsagePercentage_ShouldCalculateCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="011894a4-ce24-7e54-f1d5-7f0ac286b44b">
      <Execution id="7b5f71ae-9eb4-4c66-ada1-dc705ac90313" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="QueueUsagePercentage_ShouldCalculateCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_InvalidConfiguration_ShouldThrowException(portName: &quot;COM999&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c828ecfa-33bf-6434-f923-b9cfd77032bc">
      <Execution id="cbbc7a44-7da1-4587-b383-863e172349f4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_InvalidConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 2147483647)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c4f03241-cdba-e70d-96b7-1a09d3cf8bfb">
      <Execution id="aa86879e-2c67-4336-81a3-8bd578a73f8f" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SendRateLimit_ExtremeValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [72, 101, 108, 108, 111], expectedHex: &quot;48 65 6C 6C 6F&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ed73d661-31f4-564a-3bca-e7bd1da12bc7">
      <Execution id="f10df243-2e9a-4378-a3e1-f256099c8356" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 30000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a094d57c-350b-c7d9-7ef4-ab145f0776e7">
      <Execution id="e73b1314-780e-4ea9-ba76-585b4371e840" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="de18f2eb-3df5-540f-0a3a-47950916d770">
      <Execution id="96aba62f-8818-47c5-87c3-90c4887632e1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ad145a9e-531c-61f2-55a9-e48b910a979e">
      <Execution id="3e137971-669a-44c1-b858-b19d0344f52d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Constructor_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: 115200, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="456b7034-8cd0-175d-c38d-784450059613">
      <Execution id="002c796d-f469-45d5-9194-8f49a9f9d9ff" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithZeroLengthData_ShouldHandleCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3a22456d-b134-6108-c30d-835c62639107">
      <Execution id="5420184d-4913-407a-ad79-910cb3f27f68" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithZeroLengthData_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex(bytes: [72, 101, 108], separator: &quot;&quot;, uppercase: True, expectedHex: &quot;48656C&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fcb4374f-81fa-4153-7c7a-49168b68b53b">
      <Execution id="0ab4eff7-c64c-4c96-a69e-c30050869cc9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithCustomSeparatorAndCase_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConcurrentAccess_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3154c378-cb14-2e6f-72c2-17b34c4e8da5">
      <Execution id="b42c2cf0-b170-4f25-9e86-4b0e360f5495" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_ConcurrentAccess_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.GetStatistics_ShouldReturnValidStatistics" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bac682fb-bc9a-5ee1-6f8f-e6c1b404c020">
      <Execution id="faf204e2-d0b2-4967-aec7-7f5b126f8326" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="GetStatistics_ShouldReturnValidStatistics" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4910faa5-5592-c319-7394-2de698b2c0c7">
      <Execution id="40e7a617-4afd-4fe8-8ae4-81f79ff6a6e1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.BufferManagement_WhenAdvancedBufferingDisabled_ShouldReturnDefaults" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="259912bc-7987-fbbd-0222-56d9037315ca">
      <Execution id="854e87df-ad4f-47c8-9734-35ad6faadb6e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="BufferManagement_WhenAdvancedBufferingDisabled_ShouldReturnDefaults" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WithRateLimit_ShouldEnforceTimingAccurately" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4d4c87b9-bd77-56b0-acd7-7c0c49cbc08f">
      <Execution id="b2b38f9d-082b-45f1-9a2c-83932420a109" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_WithRateLimit_ShouldEnforceTimingAccurately" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithValidService_ShouldAddToCollection" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3d3291e3-98dd-e9e2-de72-914857c1022d">
      <Execution id="3524b761-a242-43f7-aa7b-431e2b114664" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="AddSerialPort_WithValidService_ShouldAddToCollection" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.Constructor_WithNullServiceProvider_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e0e718ff-4c78-ae1f-949d-1594d0430302">
      <Execution id="f38269ab-ce5b-4a19-8379-12e2fd5f7a63" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="Constructor_WithNullServiceProvider_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.CanSend_WhenEnabled_ShouldReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e3460a9b-1799-f6b6-2f2c-a356970be8b9">
      <Execution id="f41ea628-190b-45f5-804f-648770491ee4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="CanSend_WhenEnabled_ShouldReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_LargeDataPackets_ShouldHandleCorrectly(dataSize: 65536)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="02b715c1-0002-be47-2b1c-d64152d8f12d">
      <Execution id="8ed6e52a-ba87-4b97-a840-3453858381a7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_LargeDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Disable_ShouldSetIsEnabledToFalse" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="374deec9-2627-2caa-434b-b1767297fddb">
      <Execution id="94d7a81d-42e3-41a2-a338-ee366d18cf5c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Disable_ShouldSetIsEnabledToFalse" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithInvalidCount_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f758c623-141e-3413-11d2-f683cfe5a47c">
      <Execution id="48ec0c7c-e89f-4517-a05e-f4404800f234" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithInvalidCount_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex(bytes: [255, 0, 171], expectedHex: &quot;FF 00 AB&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="54219804-c561-b035-0838-e2a41e644606">
      <Execution id="cd002d67-472f-496e-865f-0c4cbb4ab0ca" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToHexString_WithDefaultSeparator_ShouldReturnCorrectHex" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.AddSerialPort_WithDuplicatePortName_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="69868bb1-a9df-9b47-1d6a-43a9bb280b16">
      <Execution id="6ecbbd78-5a7a-4784-8cb6-1fc5015a6a23" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="AddSerialPort_WithDuplicatePortName_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldReturnServiceCollection" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="9510bd6c-2b16-38c6-12a7-a2ae238d0e20">
      <Execution id="cc465c13-7644-4691-8df3-15d540c8a1d8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_ShouldReturnServiceCollection" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 9600)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d99254fa-0691-194c-a6ad-0465dd755790">
      <Execution id="bfe8f9d8-bf46-4c5a-b1bc-b6fd445a81ae" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 2)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="08289273-2527-c675-4a6a-0bcbc2ae148c">
      <Execution id="ea985ef5-c935-4ffc-839e-df416cf26f85" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_TinyDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_ConnectionLost_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4593fd1a-0307-349e-dd30-e0a7cd2c3a3b">
      <Execution id="7a6db02b-97f4-480b-ab3f-de2d6337c076" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_ConnectionLost_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="65c4cae6-02b9-cdd9-6a8b-d3eb8e0242f1">
      <Execution id="c0dd221e-c429-4ca9-8bb2-29a5ae4fd146" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Constructor_WithValidConfiguration_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_AdvancedBuffering_ShouldConfigureCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5bacd699-c1c3-a30f-c688-788c48f5fa12">
      <Execution id="435b4082-692b-469f-9e74-8e6076616f58" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_AdvancedBuffering_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithExistingPort_ShouldRemoveAndReturnTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5bb814e9-4a25-51d9-60b7-6112270973bf">
      <Execution id="a65cd216-e109-4577-82cf-e939c8bfffff" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="RemoveSerialPort_WithExistingPort_ShouldRemoveAndReturnTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithXoffData_ShouldPauseFlow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="89904fc7-f945-148c-427f-28dd272d907e">
      <Execution id="f13d4551-8316-4083-8e89-17e52b888c34" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithXoffData_ShouldPauseFlow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_ShouldRegisterRequiredServices" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3d8895f0-2b0a-046b-ed9b-3a2696b478f8">
      <Execution id="2deea3dc-49a2-4144-9155-2a3a6e020abf" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_ShouldRegisterRequiredServices" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_RepeatedOperations_ShouldNotLeakMemory" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b25ea050-dc5b-ed4b-30ec-a1476f14ecc6">
      <Execution id="e8afc1d1-c70c-410f-9150-52ad02030166" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_RepeatedOperations_ShouldNotLeakMemory" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 8, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d095da48-d2a1-b869-306b-68ec74fb6a86">
      <Execution id="321c0449-aa58-4c80-a701-e06c6781de51" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly(baudRate: 115200)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="48f760e6-1514-1162-1f47-005a4a4b8f9d">
      <Execution id="5685f135-9271-4cd9-8d27-76409933fbb5" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeBaudRates_ShouldConfigureCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithText_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="a33546b2-5014-1109-76f2-708037dcb465">
      <Execution id="2b75dc1b-2c4d-4040-af5b-0b3fd120f53b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithText_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNullOrEmptyPortName_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="be06675f-3c25-5bc8-de68-febd1ec325bb">
      <Execution id="8d32a697-f63a-4e4c-a378-07e8fbdfacdf" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="RemoveSerialPort_WithNullOrEmptyPortName_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WithNullData_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="29cc443b-5843-75d0-329d-fdd89478ba0f">
      <Execution id="65858a03-ba53-4f09-8434-1a3683e6aa77" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WithNullData_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_WithCancellation_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="66354075-61e8-8db6-93a7-d9e160dce397">
      <Execution id="62484ed8-a61c-4519-8158-70fcbee2c35d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ConcurrentOperations_WithCancellation_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="9bd2b2f8-4184-1ac4-6817-df59f52f3eae">
      <Execution id="0b1e9da5-5ece-410c-a077-9ce5764114e3" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Dispose_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithReconnectSettings_ShouldReturnExpectedResult(interval: 0, maxAttempts: 0, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b">
      <Execution id="5d9e7d07-daf3-4d7a-b099-5c5cd25c8751" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithReconnectSettings_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="11d85114-8736-9f44-b948-699605b5a326">
      <Execution id="7fede1e0-bf93-4149-b3d7-652d3e317010" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2660b25c-0ab3-7226-1818-3de3d93f7e8b">
      <Execution id="0361fda0-200a-4482-9cf3-d7791051e25a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.SerialPortException_ShouldInheritFromException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="53690872-c918-73be-c68f-532030de60cd">
      <Execution id="a7bef6a0-e63e-49d0-baf3-2877d351c46e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="SerialPortException_ShouldInheritFromException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_BufferOverflow_ShouldHandleGracefully" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c18a85de-caa6-f92b-dd42-c491eddd8d5a">
      <Execution id="22242042-b953-42b6-a003-211b8e2dc127" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_BufferOverflow_ShouldHandleGracefully" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;&quot;, expectedText: &quot;&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7c790003-2d63-4b93-840e-28312682c36d">
      <Execution id="e379bf2f-5dc1-41ab-9a8a-82ef24e023a7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: Both)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cffe2f0d-7498-e2dd-fb74-c52cfbb78304">
      <Execution id="81d44f91-05de-46bc-ac9d-8e0765c01b00" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.RecordSend_WithValidData_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0ba56fbe-43b1-2a63-5322-5cc9943a2bc2">
      <Execution id="12b23232-17d9-4999-a956-52600de5ab32" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="RecordSend_WithValidData_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_Default_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0">
      <Execution id="1953f434-f65b-4a5b-90ad-2cefdbaca545" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_Default_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d0e94aff-761a-b752-8ea1-b05e263e6baa">
      <Execution id="ac795a2a-df3d-4b0d-8451-876a99143895" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithMoreRequestedThanAvailable_ShouldReturnAllAvailable" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithNullData_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fc11fc4c-e912-c68a-17af-aad770dde13c">
      <Execution id="3d162973-bc99-4a7d-9baf-5fd6d98f8714" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithNullData_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.ReadAsync_WithBufferOverflow_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d3314a3a-784e-50f7-5f7e-7145efc09018">
      <Execution id="596ed513-f9f2-4941-a47d-1ae1917a4898" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="ReadAsync_WithBufferOverflow_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: -1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c8b6c16e-78a6-4e53-1410-df1171a01492">
      <Execution id="682cbfe2-6ba2-4a28-93b2-0d10cf70fa4c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_TinyDataPackets_ShouldHandleCorrectly(dataSize: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2a2a8bb1-f3b5-0d08-191a-eec8cd12af85">
      <Execution id="bcaa04c6-4416-44be-b279-9047882a5b59" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_TinyDataPackets_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.ConcurrentOperations_ResourceContention_ShouldNotDeadlock" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bdd00514-c674-7fbe-6276-f883dc5e0af9">
      <Execution id="57cbd7dd-8302-4419-8168-f1c44c28f98c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="ConcurrentOperations_ResourceContention_ShouldNotDeadlock" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_BasicLifecycle_ShouldWorkCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b5d0270e-cd4b-dce1-3f1c-951f7852f9a7">
      <Execution id="5f72bc09-0928-40f7-82ec-7c0e78bfe51d" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_BasicLifecycle_ShouldWorkCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Dispose_CalledMultipleTimes_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="6ff352e2-9dcf-35a2-90fb-231d5f1f5bba">
      <Execution id="43b8a1ae-ea2f-4a63-97f9-177f13be5a36" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Dispose_CalledMultipleTimes_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBufferSizes_ShouldReturnExpectedResult(bufferSize: 4096, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987">
      <Execution id="a4555635-a85f-412b-9e10-077685d6537c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBufferSizes_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.ToText_WithUTF8Encoding_ShouldReturnCorrectText(originalText: &quot;你好&quot;, expectedText: &quot;你好&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ae04b4e0-cf08-4697-35c4-f48e7c5131c5">
      <Execution id="dbd57971-76ce-43ac-80f7-aae087abc314" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="ToText_WithUTF8Encoding_ShouldReturnCorrectText" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithDataBits_ShouldReturnExpectedResult(dataBits: 5, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8c638f55-8f9a-3b1d-dff8-8169a30d369f">
      <Execution id="88ef615a-dfb4-4113-b398-143a01ba4419" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithDataBits_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="befe512a-b39c-f7de-f215-29ab09232596">
      <Execution id="b86d42a9-56c5-46a6-a7d2-606fcea76d22" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly(bufferSize: 512)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="80209ebf-9964-6643-698c-8cc7c39137a7">
      <Execution id="2841655f-c4bf-4603-a8e3-2da35a914f82" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_BufferSizeBoundaries_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-8&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2b557e92-a648-4516-f450-d8ea7736c72f">
      <Execution id="95e9f56d-aa8c-49de-a0fd-944792f53ca1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithTimeouts_ShouldReturnExpectedResult(timeout: 1000, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="44298843-f215-2386-e136-f2a25ffedf0e">
      <Execution id="f46d47a0-f22a-4aa8-beed-cb98a489d1d7" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithTimeouts_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 1024)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="90a5f1aa-f90c-1c7c-8cfb-a59b5defb741">
      <Execution id="69e1b7ce-3be8-4c06-bf14-f3de9eeec5de" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.SerialPortService_ConcurrentOperations_ShouldHandleParallelRequests" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="14978f86-a584-09a6-4ff2-659b440bf384">
      <Execution id="13fb118d-878d-4ff9-8a2d-d6cb32cc65ab" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="SerialPortService_ConcurrentOperations_ShouldHandleParallelRequests" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 100000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ed1c132d-d00f-bdaf-6b94-61ad7a3557e9">
      <Execution id="b803f819-7453-492b-9bb2-a33756d49939" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_HighLoadOperations_ShouldMaintainReasonableCpuUsage" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5cd769ba-e210-24e0-902c-6f839ac6a5e1">
      <Execution id="6750ba6b-d1d8-421b-bf13-3df008f5dc5b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="BufferManager_HighLoadOperations_ShouldMaintainReasonableCpuUsage" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_DifferentEncodings_ShouldHandleCorrectly(encodingName: &quot;UTF-32&quot;)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8043c734-3566-ddb7-1d4a-07a44a746bbb">
      <Execution id="de3ffdc6-9302-447a-ae87-fca11e606ea2" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_DifferentEncodings_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests.DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity(dataSize: 100)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="c6821c0d-34ef-41c3-52bd-004793cc5855">
      <Execution id="b3638f59-b174-4f17-ac35-931184296751" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BasicIntegrationTests" name="DataTransmission_WithDifferentSizes_ShouldMaintainIntegrity" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.SerialPortServiceOptions_ShouldHaveCorrectDefaults" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e5f24540-0924-e65b-6f16-4ab793a71012">
      <Execution id="a8d95230-93c6-4a7d-8e70-169769baa4de" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="SerialPortServiceOptions_ShouldHaveCorrectDefaults" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48-65-6C-6C-6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5">
      <Execution id="cd317320-8601-42e2-bf47-66d7310b229b" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly(flowControlType: XonXoff)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b1444560-e97c-3484-c5b5-7415dcdaf44b">
      <Execution id="e9cfbda9-4706-4621-90b9-f3c6e9695a82" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Constructor_WithDifferentFlowControlTypes_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetBytesToRead_WhenNotConnected_ShouldReturnZero" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="7255bb30-60f6-a3b4-e259-af25a7d0a5b7">
      <Execution id="7ff8156d-54f7-40b6-b3ed-e7861812cac9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="GetBytesToRead_WhenNotConnected_ShouldReturnZero" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_ContinuousOverflow_ShouldMaintainStability" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="dee417d5-57ab-c1ce-a824-beeb5380368e">
      <Execution id="168099a9-6e39-4818-bec3-9375f0acdfe0" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_ContinuousOverflow_ShouldMaintainStability" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="2e50dc24-0e76-792e-ccef-ba135fad6d20">
      <Execution id="951a4cfe-92d0-486d-8d2b-14affd768105" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetAllStatus_WithMultiplePorts_ShouldReturnAllStatuses" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="5fcd1008-efe6-9593-2351-b4703f8be1f5">
      <Execution id="5aee73b7-257b-4698-b3a9-217bfd9e4803" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_WhenQueueFull_ShouldTriggerOverflowEvent" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests.SerialPort_DeviceRemoved_ShouldDetectAndRecover" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="e33e1140-ec5a-c360-ef7c-351a69dc550b">
      <Execution id="d21a69d5-82cf-4bad-bfcf-49a239d2113c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.ErrorRecoveryTests" name="SerialPort_DeviceRemoved_ShouldDetectAndRecover" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly(requestCount: 0)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d205cea9-26a0-cb1a-deb8-bd301be91a7a">
      <Execution id="e901b97f-cac2-413a-8687-e9c48ea02430" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="DequeueBatch_WithBoundaryValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests.SerialPort_ExtremeTimeouts_ShouldRespectSettings(timeoutMs: 2147483647)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="11e2ecbb-54ae-ecc5-771b-56b899b2be70">
      <Execution id="b5c49b4e-1c8e-47b5-b435-19572f80b3a2" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Integration.BoundaryConditionTests" name="SerialPort_ExtremeTimeouts_ShouldRespectSettings" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.GetAvailablePorts_ShouldReturnPortArray" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="8b2e14a6-d10e-1c5b-c949-007826a28517">
      <Execution id="c1573627-16c7-432f-8c6b-33f0841cbed9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="GetAvailablePorts_ShouldReturnPortArray" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.Constructor_Default_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ee05eb54-5290-18f9-2d55-e832c1a8888d">
      <Execution id="5ddb6b77-7b75-42ee-ba2e-2203aa85dd75" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="Constructor_Default_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueDequeue_ConcurrentOperations_ShouldBeThreadSafe" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f7c5e95f-be71-08d0-bb43-352b994d7d2c">
      <Execution id="54107aa8-b055-40f6-9943-450babf218b8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueDequeue_ConcurrentOperations_ShouldBeThreadSafe" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.SendRateLimit_ExtremeValues_ShouldHandleCorrectly(rateLimit: 1)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="19c767b9-00f8-d409-00da-eeea99ad6661">
      <Execution id="90852709-4922-4b8e-8502-633e7fb1c280" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="SendRateLimit_ExtremeValues_ShouldHandleCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetAvailablePorts_ShouldReturnPortArray" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="cac85d36-7607-899b-f9db-0914e3049caf">
      <Execution id="06f62de8-f7fa-49fb-9175-e18d27d99abc" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetAvailablePorts_ShouldReturnPortArray" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithBaudRate_ShouldReturnExpectedResult(baudRate: -1, expected: False)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="10ecc65a-1d98-44c2-5793-97f0d54a583d">
      <Execution id="309e1113-bc06-44d2-93ac-8d360107a624" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithBaudRate_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="55695b9c-8fa7-75b9-c4de-aa77143fb3c0">
      <Execution id="9a4b964d-9098-46eb-80ca-3f61b7739556" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="RemoveSerialPort_WithNonExistentPort_ShouldReturnFalse" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithNonExistentPort_ShouldReturnNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="06b280c7-f3e9-b8e7-377e-97f90aa891c1">
      <Execution id="7971baf9-36eb-4180-949e-3435cbc38b32" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetSerialPort_WithNonExistentPort_ShouldReturnNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Configure_WithValidConfiguration_ShouldUpdateConfiguration" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="186e3f0a-894f-97e9-d204-455dc212b0c8">
      <Execution id="e9b3d25c-7a5f-48ed-b2e2-345ced6350a1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Configure_WithValidConfiguration_ShouldUpdateConfiguration" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithDuplicatePortName_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f7c3f6f4-f082-0f10-2964-f125578d9325">
      <Execution id="0ab0e4ae-c583-4b71-b001-86205ec10814" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="CreateSerialPort_WithDuplicatePortName_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently(dataSize: 10000)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="0110b712-a0ab-bd1c-e53f-1f5be5b0e0fa">
      <Execution id="fe4b6b6d-bee5-445e-8e8b-61f43bce57db" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_LargeBatchProcessing_ShouldHandleEfficiently" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.Enable_ShouldSetIsEnabledToTrue" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b3f503b4-beb7-1162-dc16-c72eb6d05b08">
      <Execution id="2c40d0d5-44a6-420e-ad3f-66168307d7a8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="Enable_ShouldSetIsEnabledToTrue" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests.SerialPortConfigurationException_ShouldInheritFromSerialPortException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="ac78eccc-7e0e-a33e-3154-a1d2a9e455e3">
      <Execution id="71c524bd-8402-4ef0-bfda-b250c344a5f4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortConfigurationExceptionTests" name="SerialPortConfigurationException_ShouldInheritFromSerialPortException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Dispose_ShouldNotThrow" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="3fede858-ef69-906c-1781-83551e8174e8">
      <Execution id="41f649f7-0d77-49a3-b648-1de2c5f4ad21" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Dispose_ShouldNotThrow" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.Constructor_WithValidConfiguration_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="1110dce6-cdc2-edbd-83e1-76acc885583d">
      <Execution id="8a07da2e-c89c-4aee-bf09-43e6d0b7fee8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="Constructor_WithValidConfiguration_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.Constructor_WithNullText_ShouldInitializeWithEmptyData" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="86618079-abf9-30c9-2492-2a4b2efcc170">
      <Execution id="f4942b0c-890d-4cac-a648-9ab751950f15" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="Constructor_WithNullText_ShouldInitializeWithEmptyData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests.Constructor_WithPortName_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="eec8e6e9-5b71-12c7-7998-04c7b1e88c20">
      <Execution id="66e70a07-ed21-45ec-b70a-57ba03fb632a" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortDataExceptionTests" name="Constructor_WithPortName_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithNullPortName_ShouldAllowNull" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="fc3b21c3-0f8b-a760-763b-ba136c1f3355">
      <Execution id="40919078-a161-43af-a6d4-658ca7f7a722" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithNullPortName_ShouldAllowNull" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="aa267d8a-b773-ddc4-6cfe-031c9347f3d1">
      <Execution id="59560606-8719-4f19-a61e-e1b044afe6cd" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithPortNameMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.GetSerialPort_WithExistingPort_ShouldReturnService" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="81eb87c9-cb57-f039-ec3e-433c7ad89d3b">
      <Execution id="3f3cfffa-799e-4115-9d4c-0c4d05d8898c" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="GetSerialPort_WithExistingPort_ShouldReturnService" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.BufferManager_LongRunningOperations_ShouldNotLeakMemory" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="4f700146-33f1-ec69-f82a-0bacec577d1e">
      <Execution id="456c9b97-59fe-403c-8146-b5675e6f85a1" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="BufferManager_LongRunningOperations_ShouldNotLeakMemory" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortDataTests.FromHexString_WithValidHex_ShouldReturnCorrectData(hexString: &quot;48:65:6C:6C:6F&quot;, expectedBytes: [72, 101, 108, 108, 111])" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="faacff99-6157-5e13-4b0f-4d4468903363">
      <Execution id="154122c8-8007-4e45-bb9b-3415fbc287f9" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortDataTests" name="FromHexString_WithValidHex_ShouldReturnCorrectData" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortServiceTests.Constructor_WithNullLogger_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d2dd8c1b-a46c-c63e-2bb9-e11f3f0fe385">
      <Execution id="a6a58689-7796-40f3-92de-857f273496ac" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortServiceTests" name="Constructor_WithNullLogger_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests.Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="b8844c7c-ca09-4104-11ef-326b5bcd433f">
      <Execution id="81c9296d-c5c0-4f41-9878-bfca472ba128" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Exceptions.SerialPortExceptionTests" name="Constructor_WithMessageAndInnerException_ShouldInitializeCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.SerialPortManagerTests.CreateSerialPort_WithNullConfiguration_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="43a8f9d1-3c2f-114d-d7c3-2fa82d3ccdfd">
      <Execution id="b1fd1d0e-b51e-40cf-90a0-036e12b513fe" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.SerialPortManagerTests" name="CreateSerialPort_WithNullConfiguration_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests.AddAlicresSerialPort_WithNullConfigureOptions_ShouldThrowException" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="bed1de91-05bf-0085-34af-7bee2cd33ea7">
      <Execution id="c37643f8-d529-436b-bc97-011fe05c58c8" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Extensions.ServiceCollectionExtensionsTests" name="AddAlicresSerialPort_WithNullConfigureOptions_ShouldThrowException" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests.EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly(itemCount: 9, shouldTriggerWarning: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="57a78de4-cb96-8d86-813e-ab82acf74480">
      <Execution id="b1087388-e3e4-4c77-b34e-653603d6524e" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.AdvancedBufferManagerTests" name="EnqueueData_AtWarningThresholdBoundary_ShouldTriggerWarningCorrectly" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests.IsValid_WithPortName_ShouldReturnExpectedResult(portName: &quot;COM2&quot;, expected: True)" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="d42f65dc-b483-693d-547e-03998b4ce142">
      <Execution id="0218deab-57bc-4b46-a68a-722b0a753224" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Models.SerialPortConfigurationTests" name="IsValid_WithPortName_ShouldReturnExpectedResult" />
    </UnitTest>
    <UnitTest name="Alicres.SerialPort.Tests.Services.FlowControlManagerTests.ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff" storage="g:\alicres\tests\alicres.serialport.tests\bin\debug\net8.0\alicres.serialport.tests.dll" id="f4bd691f-6e1a-eb6a-8014-5eb6cc5c57a1">
      <Execution id="df76f3ad-03c5-46fe-b2f0-64b9bd9786c4" />
      <TestMethod codeBase="G:\Alicres\tests\Alicres.SerialPort.Tests\bin\Debug\net8.0\Alicres.SerialPort.Tests.dll" adapterTypeName="executor://xunit/VsTestRunner2/netcoreapp" className="Alicres.SerialPort.Tests.Services.FlowControlManagerTests" name="ProcessFlowControlData_WithRtsCtsType_ShouldNotProcessXonXoff" />
    </UnitTest>
  </TestDefinitions>
  <TestEntries>
    <TestEntry testId="2e50dc24-0e76-792e-ccef-ba135fad6d20" executionId="951a4cfe-92d0-486d-8d2b-14affd768105" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="19c767b9-00f8-d409-00da-eeea99ad6661" executionId="90852709-4922-4b8e-8502-633e7fb1c280" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6c69255c-6ff2-34a3-9fdf-65012918c8be" executionId="f483fa16-2005-45e1-b2c5-60249c894262" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5dbf9991-d128-405c-e119-2c5c8fb571d8" executionId="5ff61378-6a9b-4588-a8fe-6fdc4e791607" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="44298843-f215-2386-e136-f2a25ffedf0e" executionId="f46d47a0-f22a-4aa8-beed-cb98a489d1d7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="82d8705e-825e-feba-4da5-acda164c6f53" executionId="add52d9c-4e63-44e6-bbd4-ac5115bf5d0b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="023e96e9-d51b-a358-64ce-ba11d0feba6d" executionId="eebb5d20-dbf6-4e55-a8e1-28cced0f71c3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0acc92da-f9c6-47d9-c5a2-760ab204265e" executionId="01325f81-d45b-4639-9e8d-93b06d3f7c39" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f4fbaad3-6781-c8aa-cfe1-bc48e5a2111b" executionId="745578c4-57a7-44ce-9f64-cd69ecc95c30" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="de18f2eb-3df5-540f-0a3a-47950916d770" executionId="96aba62f-8818-47c5-87c3-90c4887632e1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e5f24540-0924-e65b-6f16-4ab793a71012" executionId="a8d95230-93c6-4a7d-8e70-169769baa4de" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="81eb87c9-cb57-f039-ec3e-433c7ad89d3b" executionId="3f3cfffa-799e-4115-9d4c-0c4d05d8898c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="90a5f1aa-f90c-1c7c-8cfb-a59b5defb741" executionId="69e1b7ce-3be8-4c06-bf14-f3de9eeec5de" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2cc2a28b-0583-c385-8580-90889ea0fa8d" executionId="a034f87e-9513-4865-99ea-7007653f5432" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cffe2f0d-7498-e2dd-fb74-c52cfbb78304" executionId="81d44f91-05de-46bc-ac9d-8e0765c01b00" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b1444560-e97c-3484-c5b5-7415dcdaf44b" executionId="e9cfbda9-4706-4621-90b9-f3c6e9695a82" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7255bb30-60f6-a3b4-e259-af25a7d0a5b7" executionId="7ff8156d-54f7-40b6-b3ed-e7861812cac9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4797f9c7-926d-5ab1-d952-801179405c81" executionId="85463075-0783-4067-b047-526e86f270a0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3f1e1745-b426-f5fa-fc4d-7c36c8ed8987" executionId="a4555635-a85f-412b-9e10-077685d6537c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="586a2248-3398-cdf2-2aaf-e284666f9fc1" executionId="ea998325-802c-4092-ac73-1d7366faa88c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0110b712-a0ab-bd1c-e53f-1f5be5b0e0fa" executionId="fe4b6b6d-bee5-445e-8e8b-61f43bce57db" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ccb6e43b-ed87-41fb-aacf-8532bac21ec1" executionId="3b98bd82-8f26-4ecd-b8d6-9bea56c0164e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bfdab111-1736-9094-751d-b681895868a8" executionId="348f548d-6db4-4946-b3e8-97c5ebc09019" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="35ae527b-65f4-e761-c5e9-42b376ef79c1" executionId="4de4d1d4-c3c5-4d3e-87d0-89b61329cce5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eb395603-826c-2aec-bdbc-d35ee9d4654e" executionId="df9d9e58-fdc9-481c-b136-202e243aa8db" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1fc3a25a-3320-5e0f-9e3e-b9761c8f4549" executionId="a3e99d5d-f5dd-4dd6-9499-4ffb83d90be7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="53a79683-6e18-bdcc-2942-0ae631602802" executionId="dc4fa79d-97af-4d71-9f7a-bd4fad276943" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="54caa2f7-9288-7523-076d-6fecd3569986" executionId="b8e27a8d-7e6f-4095-a619-9e6d69403772" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="de7a5bdb-03df-5bef-5375-0d9de7fb33cd" executionId="147c43d5-1fa4-4f03-8854-fc8b62b6a2c6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0e388063-0d1d-fee8-e535-f6ea426ca6a0" executionId="ceeae204-bfe1-473f-a87b-b78491b735ae" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c18a85de-caa6-f92b-dd42-c491eddd8d5a" executionId="22242042-b953-42b6-a003-211b8e2dc127" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3d3291e3-98dd-e9e2-de72-914857c1022d" executionId="3524b761-a242-43f7-aa7b-431e2b114664" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="05265970-e28e-f5df-e7d7-9126a86d207f" executionId="dbe9734a-4d67-4839-9f9f-e0ee4fc6aa52" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3b43f857-4c69-124a-f1e5-4526c8f72ed1" executionId="672746ff-99ba-4793-93da-3ee85364ee58" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="97ad4d87-64e8-3d1f-2000-33d34d4e461d" executionId="3d4f7dd1-9ad2-498c-ae6a-a291aed1d1fb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c4b6fa98-2242-c1ba-efc6-ec174c1be4e2" executionId="63ecf14e-772d-41b1-aee2-74073bbd57b8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c34da281-7d78-d48e-fd36-b17e7d8c79e7" executionId="7064f45b-fe9d-48ae-88b9-b5c3e37e5fdf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="08d3fc81-b996-b7a6-e1cd-78cabc053881" executionId="b46962e9-2c63-452d-8239-a5efddd62907" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="57f89734-de5a-0489-86e3-f364df300ca2" executionId="15677d44-7fa2-4608-bbed-273fe4a276d7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="acca298d-8d66-e074-22dd-fdf90888635f" executionId="f9bcea77-6c5e-4ea7-aab9-eb4ddbde4863" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d99254fa-0691-194c-a6ad-0465dd755790" executionId="bfe8f9d8-bf46-4c5a-b1bc-b6fd445a81ae" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="28df04bd-39fc-1cce-6c87-14cb33ebc5ed" executionId="2398c596-00b2-4a2c-818e-573fa8fb4361" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="57a78de4-cb96-8d86-813e-ab82acf74480" executionId="b1087388-e3e4-4c77-b34e-653603d6524e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="06f82529-1ce7-16b1-604c-9f0392f7d160" executionId="2e8b7ba4-c25b-486a-97aa-6cb2ce1ae5e2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b5d0270e-cd4b-dce1-3f1c-951f7852f9a7" executionId="5f72bc09-0928-40f7-82ec-7c0e78bfe51d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ee4c97f9-aba3-7fd3-4eff-c0ae32af980a" executionId="594f8804-5ffa-41ce-a24f-3705fc620b1a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="26c01f72-2f55-6301-3660-bbcccfc220bf" executionId="43e19725-a803-445e-8465-8a99aa11c356" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="80209ebf-9964-6643-698c-8cc7c39137a7" executionId="2841655f-c4bf-4603-a8e3-2da35a914f82" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0d5b3495-7932-d5af-331e-630ce28c78dd" executionId="69b78690-82a5-40c5-8a31-f5208c2de4e0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="38e1a48a-8126-3cc7-e8ac-60f91e67cb3d" executionId="7166a037-ae8d-40a1-946f-a9bfa7f2a40f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="15548509-32f5-1f17-61af-b5d469801c2e" executionId="d9608f4e-6585-410d-98a5-4fccb6321341" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d095da48-d2a1-b869-306b-68ec74fb6a86" executionId="321c0449-aa58-4c80-a701-e06c6781de51" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1110dce6-cdc2-edbd-83e1-76acc885583d" executionId="8a07da2e-c89c-4aee-bf09-43e6d0b7fee8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="54e12c3f-6b43-5687-a842-a3d35e6a7a19" executionId="86431e36-fdf8-470f-92b0-193de547cf03" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="48f760e6-1514-1162-1f47-005a4a4b8f9d" executionId="5685f135-9271-4cd9-8d27-76409933fbb5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2f29d2d9-c8a3-7a41-59a4-b408ec3f6dd9" executionId="b4b837ab-c5b9-4992-88b1-8971f07c2337" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="746694ab-c99f-c4df-9347-d40ac323bdff" executionId="6fc05425-af77-49a3-9568-e9613286622b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="aa267d8a-b773-ddc4-6cfe-031c9347f3d1" executionId="59560606-8719-4f19-a61e-e1b044afe6cd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f3202fa7-6b71-5d20-4401-3d8bc9668755" executionId="afe412f9-f55e-4489-abd0-56061771ac02" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="abc2d729-2b39-bae1-e7d9-85d1bd974a73" executionId="8bc32029-ca05-4803-ab10-9a719e7a0c4c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d030f382-42f0-6302-7045-2cfd01c44112" executionId="a9c81fd1-7c7d-4477-8189-e5b34eb1097b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="db95ccd0-c126-7f48-a28b-a633c62602b2" executionId="fd487815-0c6c-4623-ade8-aa2bd6049305" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="6ff352e2-9dcf-35a2-90fb-231d5f1f5bba" executionId="43b8a1ae-ea2f-4a63-97f9-177f13be5a36" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f26d999c-5247-a0ac-b5ab-848e393d4022" executionId="6c1458e6-4f42-421e-a453-d182049b2cc6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ad145a9e-531c-61f2-55a9-e48b910a979e" executionId="3e137971-669a-44c1-b858-b19d0344f52d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bed1de91-05bf-0085-34af-7bee2cd33ea7" executionId="c37643f8-d529-436b-bc97-011fe05c58c8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9bd2b2f8-4184-1ac4-6817-df59f52f3eae" executionId="0b1e9da5-5ece-410c-a077-9ce5764114e3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2a46be50-f303-bb70-1536-236543d44d59" executionId="33dff329-eefd-40af-976f-1d7c9e051786" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5168b9bd-ef9a-5e12-e4e4-606e2d42d0c0" executionId="1953f434-f65b-4a5b-90ad-2cefdbaca545" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fa202986-794c-673e-db28-2db703cc0fc9" executionId="9b4317ef-7d09-4146-a35c-aa19092d1902" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="78f63ed6-a82b-1dc7-8333-4f5a85338c27" executionId="e55a6481-2379-479d-949d-7cd97d2b6a37" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4910faa5-5592-c319-7394-2de698b2c0c7" executionId="40e7a617-4afd-4fe8-8ae4-81f79ff6a6e1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e0e718ff-4c78-ae1f-949d-1594d0430302" executionId="f38269ab-ce5b-4a19-8379-12e2fd5f7a63" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ff8dcbb8-c854-51a3-cf7a-a1ca3b1fcc29" executionId="28439401-bc8b-47fb-a069-4af76f28506a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ceb4348a-7f39-5209-f0c5-47a92f0ccf52" executionId="33203fdd-8290-4e18-8316-d739cb25f474" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1e4bdd15-076b-9af7-45c3-530c1fecf022" executionId="01b7f89e-5363-4bbb-ae6c-907d5a235f44" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="37b241c9-a28c-3630-63d5-22833c81cd74" executionId="fc8712a6-ba7f-4562-a4d8-3bb7bac7fe3b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="02b715c1-0002-be47-2b1c-d64152d8f12d" executionId="8ed6e52a-ba87-4b97-a840-3453858381a7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0ba56fbe-43b1-2a63-5322-5cc9943a2bc2" executionId="12b23232-17d9-4999-a956-52600de5ab32" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4e85762c-ae91-2b9e-76be-257c688fb9fa" executionId="66a06abc-2e9d-44fa-b53c-2eaeaf78a76d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="df64e4db-ecba-2113-fb43-2182e90a55d5" executionId="0b372984-b1f9-4b6b-bd67-13cd603c083d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f758c623-141e-3413-11d2-f683cfe5a47c" executionId="48ec0c7c-e89f-4517-a05e-f4404800f234" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f0dd822e-110a-9423-89f0-2a6bc0510f84" executionId="a0c4c48e-a845-454a-ba8e-4ceafdc85dc1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4593fd1a-0307-349e-dd30-e0a7cd2c3a3b" executionId="7a6db02b-97f4-480b-ab3f-de2d6337c076" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f9be5f74-0f42-c1ca-06ce-4ff54de6dea3" executionId="c44acc57-6086-42fe-a4fd-f57f4c9e9eec" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d2dd8c1b-a46c-c63e-2bb9-e11f3f0fe385" executionId="a6a58689-7796-40f3-92de-857f273496ac" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3fede858-ef69-906c-1781-83551e8174e8" executionId="41f649f7-0d77-49a3-b648-1de2c5f4ad21" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cf59dfeb-fc64-c423-a638-29288238b6c4" executionId="dcc65334-d407-4ef1-9398-ad9da5ac40a4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c46d3460-b4d6-d480-98b9-89cb5e5d84d1" executionId="7f8c7092-e2cf-4a70-ae7c-24391b81c0c6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ea26ec81-d8c7-5fae-e953-a047af719c17" executionId="aa08870c-74a0-4845-9108-7e8e42100887" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fcf1ca95-bb0c-f451-5094-8b054adedf81" executionId="48704f38-56e5-48fb-89bd-da0d86b6575f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="faacff99-6157-5e13-4b0f-4d4468903363" executionId="154122c8-8007-4e45-bb9b-3415fbc287f9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cd2d846a-4253-c669-9172-ae3f3e9c95ea" executionId="980ca5f0-9ad2-457e-8b63-561c7b01dfa2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="700480ab-f9a3-c14c-3862-c09cc3b0f4a7" executionId="156d39ce-902e-4d57-87e9-5a2b3eb75bb6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b25ea050-dc5b-ed4b-30ec-a1476f14ecc6" executionId="e8afc1d1-c70c-410f-9150-52ad02030166" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bfffdd04-e7b6-b0a1-e537-a650f2f9df27" executionId="3b181b05-9fba-4552-84e4-d60c3651c8d2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b3f503b4-beb7-1162-dc16-c72eb6d05b08" executionId="2c40d0d5-44a6-420e-ad3f-66168307d7a8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5bb814e9-4a25-51d9-60b7-6112270973bf" executionId="a65cd216-e109-4577-82cf-e939c8bfffff" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9510bd6c-2b16-38c6-12a7-a2ae238d0e20" executionId="cc465c13-7644-4691-8df3-15d540c8a1d8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1a863009-7253-b831-45ba-4599991a1e23" executionId="cb6076f8-7296-47ca-94ef-38396ad9c6ce" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2bba4be0-2bc3-6003-e0d8-d93e1c6880cb" executionId="2fa9b714-292d-4634-b91e-b67ef1b9e17c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b1ab4e5a-1abe-d4fc-3bce-7b8f40d0cefc" executionId="98018e43-c3f3-4d7f-946b-50d165e2025e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3da8ccb5-36b2-5b31-1537-436bec0c05cc" executionId="004da334-5d0c-4ed3-8716-7d28a8a368fb" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4d4c87b9-bd77-56b0-acd7-7c0c49cbc08f" executionId="b2b38f9d-082b-45f1-9a2c-83932420a109" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ac6b9c02-26c7-4e3d-e30f-fe45601d8bad" executionId="a8ad11e8-7cea-484b-a88a-165d59a61012" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="beab0b90-bded-b34e-a1b0-0889a62c7bc7" executionId="4882b1f4-0ac8-42f3-90ef-d5ac08e393fd" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2660b25c-0ab3-7226-1818-3de3d93f7e8b" executionId="0361fda0-200a-4482-9cf3-d7791051e25a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d796aa36-66a9-9e1e-199e-e7f8579e7312" executionId="6b2046c0-85a2-4643-b100-66b095c332a0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5a625eaf-ce33-5cc0-168a-ac8f3f113fa3" executionId="dc00f163-f17c-423a-a4c6-6c6b9d44f75b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cac85d36-7607-899b-f9db-0914e3049caf" executionId="06f62de8-f7fa-49fb-9175-e18d27d99abc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7c790003-2d63-4b93-840e-28312682c36d" executionId="e379bf2f-5dc1-41ab-9a8a-82ef24e023a7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="db250e33-9c97-4756-436a-16d6e8cf414f" executionId="1716f3df-9103-4cc8-a508-560f6504a2d1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="06b280c7-f3e9-b8e7-377e-97f90aa891c1" executionId="7971baf9-36eb-4180-949e-3435cbc38b32" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1b9f49aa-b9ff-fd05-f03b-43ccb23d82e6" executionId="8ae0a0f5-6735-4f26-9d0d-aaa93a2d2f33" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="14978f86-a584-09a6-4ff2-659b440bf384" executionId="13fb118d-878d-4ff9-8a2d-d6cb32cc65ab" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="66753aa0-12f4-9a94-0af2-e1d64e8dae4a" executionId="856a7197-a784-4d05-9482-d438fd6d48ca" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4e851d5d-05a7-2eac-92f0-8bfc94b85dc6" executionId="8262aa92-33a9-47f9-add1-9b357b999643" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="89904fc7-f945-148c-427f-28dd272d907e" executionId="f13d4551-8316-4083-8e89-17e52b888c34" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b777246c-f407-ff52-0009-26e9cffd3bc2" executionId="b874fd75-c40f-4171-bf4f-cc3e0203f906" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a17ba197-677f-433a-d98b-90b6dba91665" executionId="236c29c6-af4d-4f80-b603-bfaced854f7f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8caa8b5b-b84c-d1d7-e2b2-05bff9553bb9" executionId="54ac636a-76ea-4aa8-ac62-59ca6295cdfc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8d9dd9a9-c289-1130-7c61-b67125a5dbe3" executionId="d5f59d92-725d-483e-85d2-8c66dde92f40" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5bacd699-c1c3-a30f-c688-788c48f5fa12" executionId="435b4082-692b-469f-9e74-8e6076616f58" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="29cc443b-5843-75d0-329d-fdd89478ba0f" executionId="65858a03-ba53-4f09-8434-1a3683e6aa77" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ac78eccc-7e0e-a33e-3154-a1d2a9e455e3" executionId="71c524bd-8402-4ef0-bfda-b250c344a5f4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f60c8033-5a87-0f3a-3088-48468fd9301b" executionId="cb954fd6-e22b-4e21-9114-335e8d0fc307" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="edf58ff9-246f-e14c-f25f-39068185472a" executionId="a9f8007a-3938-499a-b9e4-adf6f220cf50" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f7c5e95f-be71-08d0-bb43-352b994d7d2c" executionId="54107aa8-b055-40f6-9943-450babf218b8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c828ecfa-33bf-6434-f923-b9cfd77032bc" executionId="cbbc7a44-7da1-4587-b383-863e172349f4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a70006d6-2422-b7b2-466f-65fda27fd805" executionId="bab80e04-23e1-4d34-b316-21d378dc088f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cb5b6170-9791-0b05-807b-fb298b58c5d8" executionId="ab6d0263-d868-4d43-b673-075fb2f37bb3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="acb3181c-ee8d-5c46-618c-09ac65f019a4" executionId="a1f7083d-4f9a-4a60-83e9-864d6ba466d1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="74f91c1b-75f8-0352-e93b-da086f3f05ba" executionId="123b1d91-9bc8-40df-8ed7-a743152d9c8d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a0d6040b-1605-8c04-788d-3eef62fbd2d3" executionId="ff135a22-b5e4-4120-85a2-4125d045f933" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c92fb4d1-98fd-ec97-499d-551eebba0847" executionId="a167a5e3-547c-4190-8c17-908649edfc45" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="03bac210-5121-4552-6b4a-8ad9c8a4430a" executionId="a13c6255-bd64-438a-908d-917eef4346d3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2f010a59-e386-1c48-7a96-6eaf300b4a00" executionId="a3f52aad-dafb-4ab8-8310-166a05c0cc4d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d72a782f-3fee-0055-ced6-0e57470253f9" executionId="3517ac61-c3e2-479f-a17d-3192098a8ca5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3154c378-cb14-2e6f-72c2-17b34c4e8da5" executionId="b42c2cf0-b170-4f25-9e86-4b0e360f5495" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2b64ca9d-2218-a1bd-b07d-cbcc8dde3d16" executionId="21a971ed-ba0a-457e-a36b-64a0fceabdd1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="befe512a-b39c-f7de-f215-29ab09232596" executionId="b86d42a9-56c5-46a6-a7d2-606fcea76d22" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bac682fb-bc9a-5ee1-6f8f-e6c1b404c020" executionId="faf204e2-d0b2-4967-aec7-7f5b126f8326" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="cf6e81ed-2d36-4cb5-7d19-3207ec4a1e6b" executionId="5d9e7d07-daf3-4d7a-b099-5c5cd25c8751" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7356f68e-ad7b-97c4-f64c-2536928bb3c9" executionId="e2ec51a3-e283-4b88-85ed-271039ff6355" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="55695b9c-8fa7-75b9-c4de-aa77143fb3c0" executionId="9a4b964d-9098-46eb-80ca-3f61b7739556" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4ba3dac7-6a62-cb49-022a-20a7d4d37087" executionId="ea304460-f273-4447-8cc0-ccd625a0ff50" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ed73d661-31f4-564a-3bca-e7bd1da12bc7" executionId="f10df243-2e9a-4378-a3e1-f256099c8356" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="65c4cae6-02b9-cdd9-6a8b-d3eb8e0242f1" executionId="c0dd221e-c429-4ca9-8bb2-29a5ae4fd146" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d3314a3a-784e-50f7-5f7e-7145efc09018" executionId="596ed513-f9f2-4941-a47d-1ae1917a4898" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e33e1140-ec5a-c360-ef7c-351a69dc550b" executionId="d21a69d5-82cf-4bad-bfcf-49a239d2113c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8c638f55-8f9a-3b1d-dff8-8169a30d369f" executionId="88ef615a-dfb4-4113-b398-143a01ba4419" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a33546b2-5014-1109-76f2-708037dcb465" executionId="2b75dc1b-2c4d-4040-af5b-0b3fd120f53b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2b557e92-a648-4516-f450-d8ea7736c72f" executionId="95e9f56d-aa8c-49de-a0fd-944792f53ca1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="902ef6cf-b62e-58ea-9022-da49556e41c5" executionId="5bee4dc3-b042-4780-a2c3-03558bd1df59" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e3460a9b-1799-f6b6-2f2c-a356970be8b9" executionId="f41ea628-190b-45f5-804f-648770491ee4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8043c734-3566-ddb7-1d4a-07a44a746bbb" executionId="de3ffdc6-9302-447a-ae87-fca11e606ea2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ae04b4e0-cf08-4697-35c4-f48e7c5131c5" executionId="dbd57971-76ce-43ac-80f7-aae087abc314" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e39f649e-7c27-616e-c761-ed717edf3ede" executionId="08fec686-f47f-4e92-a861-da18eaa21a04" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d0e94aff-761a-b752-8ea1-b05e263e6baa" executionId="ac795a2a-df3d-4b0d-8451-876a99143895" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8b2e14a6-d10e-1c5b-c949-007826a28517" executionId="c1573627-16c7-432f-8c6b-33f0841cbed9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bc61f3c1-ffcd-b602-c10b-5bb52b1c6806" executionId="277a5674-6ab2-472b-a810-ee8c942c5000" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="456b7034-8cd0-175d-c38d-784450059613" executionId="002c796d-f469-45d5-9194-8f49a9f9d9ff" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7498a7db-5111-4360-9445-2e4763e8cd4e" executionId="ec149ca5-f104-4c27-8a64-3f5b4f3fac87" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="43a8f9d1-3c2f-114d-d7c3-2fa82d3ccdfd" executionId="b1fd1d0e-b51e-40cf-90a0-036e12b513fe" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4f9d435a-f0d2-5d93-e815-ec19ec4acd79" executionId="71c4dece-95bd-4771-b82b-7421f15e4100" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fc11fc4c-e912-c68a-17af-aad770dde13c" executionId="3d162973-bc99-4a7d-9baf-5fd6d98f8714" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="65132280-9071-90ad-f1d3-b2bec385e31a" executionId="053b2eb4-41e0-4329-b10f-f3ed2409f2f7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fc3b21c3-0f8b-a760-763b-ba136c1f3355" executionId="40919078-a161-43af-a6d4-658ca7f7a722" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="dee417d5-57ab-c1ce-a824-beeb5380368e" executionId="168099a9-6e39-4818-bec3-9375f0acdfe0" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eddea9f3-c4cb-08bd-7c5f-ec33540ba04f" executionId="e913fa99-79a2-42cc-a265-f665a32aec07" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c53401be-8da1-5d73-868a-6420974db0e4" executionId="69503e35-8d0c-410c-9790-a62ced883f0a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="86618079-abf9-30c9-2492-2a4b2efcc170" executionId="f4942b0c-890d-4cac-a648-9ab751950f15" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="09ce9de4-34a5-edec-6b47-7870a0ac8152" executionId="59715640-d613-4669-9d1a-02aaa3f4ad02" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5fcd1008-efe6-9593-2351-b4703f8be1f5" executionId="5aee73b7-257b-4698-b3a9-217bfd9e4803" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c6821c0d-34ef-41c3-52bd-004793cc5855" executionId="b3638f59-b174-4f17-ac35-931184296751" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5772937f-dba3-ddd7-fc6d-303fd2ade221" executionId="38c4ab3e-6cd1-49e4-b8da-f2500b76fa14" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="40f0c48a-0bc6-7691-1bcb-bf225bd03b25" executionId="79312642-ebe0-4557-afee-293aba75d3a4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c029ac10-2236-0f3e-f243-4fb9689c404b" executionId="9887d580-9162-4e56-8989-75cb8b86c18a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b8418e21-906c-bab7-f26a-6263d8be1dd9" executionId="1b4cfb1d-7d13-4b0f-a92b-06efb77e6640" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3d8895f0-2b0a-046b-ed9b-3a2696b478f8" executionId="2deea3dc-49a2-4144-9155-2a3a6e020abf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c783c277-08d3-0e65-e123-287a21c8b95a" executionId="5679179c-ed63-494e-966b-92bea6dae946" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="68fa167d-968d-2375-d00a-3a724af909d8" executionId="2f3a54d3-b0b3-49ab-b535-b62a8450e606" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="186e3f0a-894f-97e9-d204-455dc212b0c8" executionId="e9b3d25c-7a5f-48ed-b2e2-345ced6350a1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4a656b7f-67e3-8f8d-6a98-8264a3231c8f" executionId="4fe9fac6-b171-41db-b2fd-55150a4436e8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="374deec9-2627-2caa-434b-b1767297fddb" executionId="94d7a81d-42e3-41a2-a338-ee366d18cf5c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8b38b793-1bda-2cf1-fc52-ca85a8e00e41" executionId="4223c3e6-e9f9-4c16-a9a9-ba4229d74a8a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c4f03241-cdba-e70d-96b7-1a09d3cf8bfb" executionId="aa86879e-2c67-4336-81a3-8bd578a73f8f" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="69868bb1-a9df-9b47-1d6a-43a9bb280b16" executionId="6ecbbd78-5a7a-4784-8cb6-1fc5015a6a23" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8927fc38-5352-5a92-ac17-c0b53db402c5" executionId="cc38378f-31dd-4efc-9f67-ad132e5c489b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5de9e3f2-f5fe-88a8-b3fb-d72657ad04d9" executionId="4fe0a3fe-f465-400b-93e0-d987f77ff978" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="7f56e2f6-e85d-2d67-fab2-f0a6cfe57e1f" executionId="9cedcbe3-c06a-4b9c-8712-04448c82ace4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2e5792b5-5830-a1a1-6907-f9e6eeb32c23" executionId="a1f017c2-bc37-42b1-b653-62dffdd5950e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="bdd00514-c674-7fbe-6276-f883dc5e0af9" executionId="57cbd7dd-8302-4419-8168-f1c44c28f98c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="54219804-c561-b035-0838-e2a41e644606" executionId="cd002d67-472f-496e-865f-0c4cbb4ab0ca" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a094d57c-350b-c7d9-7ef4-ab145f0776e7" executionId="e73b1314-780e-4ea9-ba76-585b4371e840" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="11d85114-8736-9f44-b948-699605b5a326" executionId="7fede1e0-bf93-4149-b3d7-652d3e317010" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="147438f0-3f17-1c20-cfc2-e5a56643d7cc" executionId="5ad454bf-094f-4664-a0fa-e0b55245716d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="53690872-c918-73be-c68f-532030de60cd" executionId="a7bef6a0-e63e-49d0-baf3-2877d351c46e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="11e2ecbb-54ae-ecc5-771b-56b899b2be70" executionId="b5c49b4e-1c8e-47b5-b435-19572f80b3a2" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f7c3f6f4-f082-0f10-2964-f125578d9325" executionId="0ab0e4ae-c583-4b71-b001-86205ec10814" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="be06675f-3c25-5bc8-de68-febd1ec325bb" executionId="8d32a697-f63a-4e4c-a378-07e8fbdfacdf" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ef67f86c-3dd1-1171-22b1-c007547c1494" executionId="366e9726-c63a-45b3-b4a5-1cb3eeb77f59" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="1ec54028-be89-592f-c08c-8fb797e62772" executionId="21cf7d0f-1286-4d2c-af70-d35d0548cb18" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="969900f1-f9c4-1336-3ebc-d975003306b5" executionId="24051586-8a7f-46d4-925a-01f37e5efe6b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="14c24c6f-1bd1-8a0b-0fdb-8dccb26c718e" executionId="2135a337-7707-4004-ad78-b62937102a94" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="9c726bc3-a469-3d93-f1bf-fce1b2658615" executionId="14d9a404-6723-4106-b773-2cb40cd2b5cc" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f4bd691f-6e1a-eb6a-8014-5eb6cc5c57a1" executionId="df76f3ad-03c5-46fe-b2f0-64b9bd9786c4" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="31b4aa6a-561a-ec27-c6d2-f1426e253b35" executionId="942e1254-937e-45fe-9117-d2ec39c3b0ae" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="3a22456d-b134-6108-c30d-835c62639107" executionId="5420184d-4913-407a-ad79-910cb3f27f68" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="8e0d6f2f-fb0e-4668-6fa9-8f21c6f9155a" executionId="3fb62bde-3ee4-4331-8019-ab2371c6ec41" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f5c5b75e-4e7c-c2d4-8d83-637641048e9e" executionId="f5298454-bf7c-4649-905e-b6a59620193d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c4268a3e-dbd0-ce2a-3f14-43313d1251f3" executionId="f31d4da9-ff2e-4327-8bab-3e593a205bb3" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ed1c132d-d00f-bdaf-6b94-61ad7a3557e9" executionId="b803f819-7453-492b-9bb2-a33756d49939" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fcb4374f-81fa-4153-7c7a-49168b68b53b" executionId="0ab4eff7-c64c-4c96-a69e-c30050869cc9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d42f65dc-b483-693d-547e-03998b4ce142" executionId="0218deab-57bc-4b46-a68a-722b0a753224" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ca5223c1-4903-c112-ec50-72cd8a818e1b" executionId="0e86de02-514a-4c74-b00d-b91e2cf04993" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4f700146-33f1-ec69-f82a-0bacec577d1e" executionId="456c9b97-59fe-403c-8146-b5675e6f85a1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="4fb7a94f-b5ad-a202-33ad-c14f2afe94a5" executionId="cd317320-8601-42e2-bf47-66d7310b229b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ee05eb54-5290-18f9-2d55-e832c1a8888d" executionId="5ddb6b77-7b75-42ee-ba2e-2203aa85dd75" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="387aec6f-b8f3-0719-a44f-14efd8e3f6b0" executionId="03e8e3ff-b244-4b38-85c3-0a488c20cc8b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e7971ba4-b797-4f77-bdc8-05995b20ff35" executionId="d5676689-d1fa-4acf-afbf-43e1c86b23a6" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="0a76b3a1-8066-016c-7b64-ba09c55edde9" executionId="7a92ebcf-8e2c-41e1-89c3-1f6ff4865c41" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="62c90d57-0073-79e7-453d-174a3cf7a9c9" executionId="839ebac8-74fe-4233-ae16-09704387a8c1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="fc0f03d5-65c5-740b-47d9-a78e15ea1d9c" executionId="a90549ac-6ab1-4c20-913d-6197aea08c55" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d864081d-48a6-ba7d-caf4-2925abe987a3" executionId="f1459385-fa3b-43a1-a29b-9ab0fcc604c9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f543d2e9-4ced-9d03-4307-68823ad67ec5" executionId="d82d7e64-ddd0-451c-be61-e57b887e29e7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="330fed7e-e885-a7aa-44dc-e8f169ebddaf" executionId="f9eb8fff-1db1-46b7-8ce6-02a963f7c8c1" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e835e9bc-7afc-0744-d083-42c92aa262b2" executionId="3e233ac1-9f60-46ea-9319-8ee5122d0c79" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b8844c7c-ca09-4104-11ef-326b5bcd433f" executionId="81c9296d-c5c0-4f41-9878-bfca472ba128" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="33597ab1-9d34-65c5-b0a1-f1a694190e93" executionId="a0471f0c-bcf7-4216-b789-2a2763cf8982" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="e95c7136-7996-a786-e59e-522991193040" executionId="64df28b5-af1f-4dae-beb0-dbddbda3fd53" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="011894a4-ce24-7e54-f1d5-7f0ac286b44b" executionId="7b5f71ae-9eb4-4c66-ada1-dc705ac90313" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="35f2d5bf-cf12-732f-0809-1a84cafae57a" executionId="c1942e08-bf50-40fb-91cb-97ae5191a149" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="5cd769ba-e210-24e0-902c-6f839ac6a5e1" executionId="6750ba6b-d1d8-421b-bf13-3df008f5dc5b" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="08289273-2527-c675-4a6a-0bcbc2ae148c" executionId="ea985ef5-c935-4ffc-839e-df416cf26f85" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a81de412-4838-6623-61fa-67ddc3188238" executionId="3562bc63-5af3-4a74-a605-10d4e8dce297" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="99219d16-4665-2168-0835-d11459e2febf" executionId="87014817-6c8d-413f-93bd-5c93d2357ee5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="10ecc65a-1d98-44c2-5793-97f0d54a583d" executionId="309e1113-bc06-44d2-93ac-8d360107a624" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d554c12b-5ed2-23ff-fe49-9271ab3c4030" executionId="d4f3d44d-0ddd-4126-b8d1-c19ba4be7075" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="259912bc-7987-fbbd-0222-56d9037315ca" executionId="854e87df-ad4f-47c8-9734-35ad6faadb6e" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="eec8e6e9-5b71-12c7-7998-04c7b1e88c20" executionId="66e70a07-ed21-45ec-b70a-57ba03fb632a" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="ad521c37-2834-6634-6bda-bfb366ff0094" executionId="e74c2ad3-dcbe-4219-9465-2375063ab271" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="dca00415-9a9b-5e9a-5d7c-36ae82b195dc" executionId="f9d77b38-a326-4db9-86dc-948a6a9b68c8" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="66354075-61e8-8db6-93a7-d9e160dce397" executionId="62484ed8-a61c-4519-8158-70fcbee2c35d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2b3d1d5d-a9e9-c463-03be-c93fc127b2b3" executionId="2ad8ed61-f6f2-4517-abb4-6ed6e0ef9fad" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="92f29400-6caf-fff2-71d7-c6b032343516" executionId="8b311a84-a207-458a-b5a3-77fd55d0dec9" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c199ae11-7eed-7c3a-2d66-da1a2b9b2453" executionId="6d590fb8-4e45-49f6-b6d6-8afaeb3bef10" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d8f02cf7-433b-61ec-c449-566982f978ff" executionId="116872a1-322e-4713-b696-e46e173933be" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="40dc612e-fcad-d21a-6d8f-c2c13cc86897" executionId="b6f91c14-4f53-420c-9389-249b03d93677" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="94919b74-043c-ac5d-87f1-0587b8f46ef7" executionId="9cc44abf-80b9-4f94-8f9a-426622018fb7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="a2862178-237f-01ca-e3a5-f47ceb7b9484" executionId="b35c16a4-bc05-4057-bf8b-53b8b1a5f50d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="b531b8c7-1b6c-8c36-b996-64028f4f0324" executionId="c1caab48-ab2e-4ad8-819e-bb07f642eee7" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2a2a8bb1-f3b5-0d08-191a-eec8cd12af85" executionId="bcaa04c6-4416-44be-b279-9047882a5b59" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="03e7ae61-8ff6-c0f3-9702-96401e863ad1" executionId="e8cfdeba-d733-4811-9181-db61d7ae2706" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d915a464-2e21-829b-f426-6772ddf55055" executionId="d3135893-3855-418d-a8c0-e94ffe6d14ce" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="c8b6c16e-78a6-4e53-1410-df1171a01492" executionId="682cbfe2-6ba2-4a28-93b2-0d10cf70fa4c" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="d205cea9-26a0-cb1a-deb8-bd301be91a7a" executionId="e901b97f-cac2-413a-8687-e9c48ea02430" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="520ee81f-3aa8-ca94-4ff6-979e424be6fe" executionId="1eaf5a49-ead2-4577-9091-12dabb3b9e1d" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="f9ae6db7-303a-9c26-b311-d84f4bd95208" executionId="cb230eff-db4e-43ab-a358-a3195c93bb82" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestEntry testId="2759dc48-2d09-a5a8-b7c3-335908e8d07b" executionId="a09e4035-40d5-4596-a7f6-c789b39b17a5" testListId="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
  </TestEntries>
  <TestLists>
    <TestList name="列表中未列出的结果" id="8c84fa94-04c1-424b-9868-57a2d4851a1d" />
    <TestList name="所有已加载的结果" id="19431567-8539-422a-85d7-44ee4e166bda" />
  </TestLists>
  <ResultSummary outcome="Completed">
    <Counters total="260" executed="260" passed="260" failed="0" error="0" timeout="0" aborted="0" inconclusive="0" passedButRunAborted="0" notRunnable="0" notExecuted="0" disconnected="0" warning="0" completed="0" inProgress="0" pending="0" />
    <Output>
      <StdOut>[xUnit.net 00:00:00.00] xUnit.net VSTest Adapter v2.4.5+1caef2f33e (64-bit .NET 8.0.17)&#xD;
[xUnit.net 00:00:00.32]   Discovering: Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.36]   Discovered:  Alicres.SerialPort.Tests&#xD;
[xUnit.net 00:00:00.37]   Starting:    Alicres.SerialPort.Tests&#xD;
�����ݴ���: 100,000 �ֽ�, ��ʱ: 0ms&#xD;
�����ݴ���: 10,000 �ֽ�, ��ʱ: 0ms&#xD;
�����ݴ���: 1,000 �ֽ�, ��ʱ: 0ms&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ��������: 4096 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 13 �ֽ�&#xD;
fail: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ��������: Frame&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_4096 �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Error -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DISCONNECT_TEST �رճɹ�&#xD;
CanSend���ܲ���: 10000 �ε���, ƽ�� 0.0003ms/��&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ��������: 2048 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 48 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_100 �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 19 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ��������: 1024 �ֽ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DATA_1024 �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ��������: 26 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ENCODING_TEST �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      ����Mock���ڷ���: COM_MGR_1&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      ����Mock���ڷ���: COM_MGR_2&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ��������: 131072 �ֽ�&#xD;
=== ���ܱ���: �����ݰ�������� (128KB) ===&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �\udabe\udeb3ɹ�&#xD;
���ݴ�С: 131,072 �ֽ�&#xD;
����ʱ��: 0.11 ����&#xD;
������: 1167159394.48 �ֽ�/�� (1139804.10 KB/s)&#xD;
========================&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����\udabe\udeb4�����ɣ��ɹ�: 2/2&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ��������: 262144 �ֽ�&#xD;
=== ���ܱ���: �����ݰ�������� (256KB) ===&#xD;
���ݴ�С: 262,144 �ֽ�&#xD;
����ʱ��: 0.17 ����&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ��������: 22 �ֽ�&#xD;
������: 1565973715.65 �ֽ�/�� (1529271.21 KB/s)&#xD;
========================&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ��������: 22 �ֽ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �㲥������ɣ��ɹ�: 2/2, ���ݳ���: 22&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ��������: 65536 �ֽ�&#xD;
=== ���ܱ���: �����ݰ�������� (64KB) ===&#xD;
���ݴ�С: 65,536 �ֽ�&#xD;
����ʱ��: 0.06 ����&#xD;
������: 1009799691.83 �ֽ�/�� (986132.51 KB/s)&#xD;
========================&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_LARGE_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_2 �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �Ƴ����ڷ���: COM_MGR_2&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����رմ�����ɣ��ɹ�: 1/1&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
warn: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �Ѿ��ر�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����رմ�����ɣ��ɹ�: 1/1&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
warn: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MGR_1 �Ѿ��ر�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      �����رմ�����ɣ��ɹ�: 1/1&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortManager[0]&#xD;
      Mock���ڹ��������ͷ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ��������: 50 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NORMAL_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 12 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 3 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ��������: 3 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_SPECIAL_CHARS �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ��������: 20 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ��������: 2048 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_ADVANCED �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BAUD_RATE_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ��������: 36 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BASIC_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���������Ѹ���: COM_CONFIG_UPDATE&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONFIG_UPDATE �رճɹ�&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_EXTREME �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TINY_DATA �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 4096 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 4097 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 512 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 513 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 65536 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 1 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ��������: 2 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_BOUNDARY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ��������: 10 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_RAPID_CYCLE �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_NULL_EMPTY �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_BUFFER_OVERFLOW �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM999 �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� INVALID ״̬�仯: Disconnected -&gt; Connecting&#xD;
fail: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      ��Mock���� INVALID ʧ��: �޷����ʴ��� INVALID&#xD;
      System.UnauthorizedAccessException: �޷����ʴ��� INVALID&#xD;
         at Alicres.SerialPort.Tests.TestHelpers.MockSerialPort.Open() in G:\Alicres\tests\Alicres.SerialPort.Tests\TestHelpers\MockSerialPort.cs:line 115&#xD;
         at Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService.&lt;OpenAsync&gt;b__26_0() in G:\Alicres\tests\Alicres.SerialPort.Tests\TestHelpers\MockSerialPortService.cs:line 142&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� INVALID ״̬�仯: Error -&gt; Disconnected&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_TIMEOUT_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ��������: 1024 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_MEMORY_TEST �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_DEVICE_REMOVED �رճɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Disconnected -&gt; Connecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Connecting -&gt; Connected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE �\udabe\udeb3ɹ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��������: 100 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ��ȡ����: 0 �ֽ�&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Connected -&gt; Disconnecting&#xD;
dbug: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE ״̬�仯: Disconnecting -&gt; Disconnected&#xD;
info: Alicres.SerialPort.Tests.TestHelpers.MockSerialPortService[0]&#xD;
      Mock���� COM_CONCURRENT_SAFE �رճɹ�&#xD;
�߸��ز��Խ��: 462092 ����/��, ���г���: 10&#xD;
���ܲ��Խ��: 971638 ����/��, �ɹ���: 100.00%&#xD;
ͳ����Ϣ����: 1000 �ε���, ƽ�� 0.001ms/��&#xD;
�ڴ�ʹ�����: ��ʼ 3,110,904 �ֽ�, ���� 34,343,608 �ֽ�, ���� 31,232,704 �ֽ�&#xD;
[xUnit.net 00:00:02.32]   Finished:    Alicres.SerialPort.Tests&#xD;
</StdOut>
    </Output>
    <CollectorDataEntries>
      <Collector agentName="JD-ITA028088-PC" uri="datacollector://microsoft/CoverletCodeCoverage/1.0" collectorDisplayName="XPlat code coverage">
        <UriAttachments>
          <UriAttachment>
            <A href="JD-ITA028088-PC\coverage.cobertura.xml"></A>
          </UriAttachment>
        </UriAttachments>
      </Collector>
    </CollectorDataEntries>
  </ResultSummary>
</TestRun>