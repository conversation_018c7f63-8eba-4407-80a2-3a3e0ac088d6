using System.Collections.Concurrent;
using Alicres.SerialPort.Models;
using Microsoft.Extensions.Logging;

namespace Alicres.SerialPort.Services;

/// <summary>
/// 流控制管理器，负责管理串口的流控制功能
/// </summary>
public class FlowControlManager : IDisposable
{
    private readonly SerialPortConfiguration _configuration;
    private readonly ILogger? _logger;
    private readonly object _lockObject = new();
    private readonly Timer _monitorTimer;
    private readonly ConcurrentQueue<DateTime> _sendTimestamps;
    
    private bool _disposed;
    private FlowControlStatus _currentStatus;
    private bool _isXoffReceived;
    private bool _isRtsPaused;
    private int _sendRateLimit;
    private DateTime _lastSendTime;
    private long _totalBytesSent;
    private long _totalBytesReceived;

    /// <summary>
    /// 当前流控制状态
    /// </summary>
    public FlowControlStatus CurrentStatus => _currentStatus;

    /// <summary>
    /// 是否启用流控制
    /// </summary>
    public bool IsEnabled { get; set; }

    /// <summary>
    /// 流控制类型
    /// </summary>
    public FlowControlType FlowControlType { get; set; }

    /// <summary>
    /// 发送速率限制（字节/秒）
    /// </summary>
    public int SendRateLimit 
    { 
        get => _sendRateLimit; 
        set => _sendRateLimit = Math.Max(0, value); 
    }

    /// <summary>
    /// 拥塞控制阈值（缓冲区使用率百分比）
    /// </summary>
    public int CongestionThreshold { get; set; } = 75;

    /// <summary>
    /// 总发送字节数
    /// </summary>
    public long TotalBytesSent => _totalBytesSent;

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived => _totalBytesReceived;

    /// <summary>
    /// 流控制状态变化事件
    /// </summary>
    public event EventHandler<FlowControlStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 拥塞检测事件
    /// </summary>
#pragma warning disable CS0067 // 事件从不使用
    public event EventHandler<CongestionDetectedEventArgs>? CongestionDetected;
#pragma warning restore CS0067

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <param name="logger">日志记录器</param>
    public FlowControlManager(SerialPortConfiguration configuration, ILogger? logger = null)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger;
        _sendTimestamps = new ConcurrentQueue<DateTime>();
        _currentStatus = FlowControlStatus.Normal;
        _sendRateLimit = 0; // 0 表示无限制
        
        // 创建监控定时器，每秒检查一次流控制状态
        _monitorTimer = new Timer(MonitorFlowControl, null, TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
        
        _logger?.LogDebug("流控制管理器已创建");
    }

    /// <summary>
    /// 检查是否可以发送数据
    /// </summary>
    /// <param name="dataLength">要发送的数据长度</param>
    /// <returns>是否可以发送</returns>
    public bool CanSend(int dataLength)
    {
        if (!IsEnabled || _disposed)
            return true;

        lock (_lockObject)
        {
            // 检查流控制状态
            if (_currentStatus == FlowControlStatus.Paused || _currentStatus == FlowControlStatus.Error)
                return false;

            // 检查软件流控制
            if ((FlowControlType == FlowControlType.XonXoff || FlowControlType == FlowControlType.Both) && _isXoffReceived)
                return false;

            // 检查硬件流控制
            if ((FlowControlType == FlowControlType.RtsCts || FlowControlType == FlowControlType.Both) && _isRtsPaused)
                return false;

            // 检查发送速率限制
            if (_sendRateLimit > 0)
            {
                var now = DateTime.Now;
                var timeSinceLastSend = now - _lastSendTime;
                var minInterval = TimeSpan.FromMilliseconds(1000.0 * dataLength / _sendRateLimit);
                
                if (timeSinceLastSend < minInterval)
                {
                    _logger?.LogTrace("发送速率受限，需要等待 {Delay}ms", 
                        (minInterval - timeSinceLastSend).TotalMilliseconds);
                    return false;
                }
            }

            return true;
        }
    }

    /// <summary>
    /// 记录数据发送
    /// </summary>
    /// <param name="dataLength">发送的数据长度</param>
    public void RecordSend(int dataLength)
    {
        if (!IsEnabled || _disposed)
            return;

        lock (_lockObject)
        {
            _lastSendTime = DateTime.Now;
            _sendTimestamps.Enqueue(_lastSendTime);
            Interlocked.Add(ref _totalBytesSent, dataLength);
            
            _logger?.LogTrace("记录数据发送: {Length} 字节", dataLength);
        }
    }

    /// <summary>
    /// 记录数据接收
    /// </summary>
    /// <param name="dataLength">接收的数据长度</param>
    public void RecordReceive(int dataLength)
    {
        if (!IsEnabled || _disposed)
            return;

        Interlocked.Add(ref _totalBytesReceived, dataLength);
        _logger?.LogTrace("记录数据接收: {Length} 字节", dataLength);
    }

    /// <summary>
    /// 处理接收到的流控制字符
    /// </summary>
    /// <param name="data">接收到的数据</param>
    public void ProcessFlowControlData(byte[] data)
    {
        if (!IsEnabled || _disposed || data == null || data.Length == 0)
            return;

        if (FlowControlType != FlowControlType.XonXoff && FlowControlType != FlowControlType.Both)
            return;

        lock (_lockObject)
        {
            foreach (var b in data)
            {
                if (b == 0x11) // XON (DC1)
                {
                    if (_isXoffReceived)
                    {
                        _isXoffReceived = false;
                        _logger?.LogDebug("收到 XON，恢复数据发送");
                        UpdateStatus(FlowControlStatus.Normal);
                    }
                }
                else if (b == 0x13) // XOFF (DC3)
                {
                    if (!_isXoffReceived)
                    {
                        _isXoffReceived = true;
                        _logger?.LogDebug("收到 XOFF，暂停数据发送");
                        UpdateStatus(FlowControlStatus.Paused);
                    }
                }
            }
        }
    }

    /// <summary>
    /// 设置 RTS 流控制状态
    /// </summary>
    /// <param name="isPaused">是否暂停</param>
    public void SetRtsFlowControl(bool isPaused)
    {
        if (!IsEnabled || _disposed)
            return;

        if (FlowControlType != FlowControlType.RtsCts && FlowControlType != FlowControlType.Both)
            return;

        lock (_lockObject)
        {
            if (_isRtsPaused != isPaused)
            {
                _isRtsPaused = isPaused;
                _logger?.LogDebug("RTS 流控制状态变更: {Status}", isPaused ? "暂停" : "恢复");
                UpdateStatus(isPaused ? FlowControlStatus.Paused : FlowControlStatus.Normal);
            }
        }
    }

    /// <summary>
    /// 发送 XON 字符
    /// </summary>
    /// <returns>XON 字符的字节数组</returns>
    public byte[] SendXon()
    {
        _logger?.LogDebug("发送 XON 字符");
        return new byte[] { 0x11 }; // DC1
    }

    /// <summary>
    /// 发送 XOFF 字符
    /// </summary>
    /// <returns>XOFF 字符的字节数组</returns>
    public byte[] SendXoff()
    {
        _logger?.LogDebug("发送 XOFF 字符");
        return new byte[] { 0x13 }; // DC3
    }

    /// <summary>
    /// 获取当前发送速率（字节/秒）
    /// </summary>
    /// <returns>发送速率</returns>
    public double GetCurrentSendRate()
    {
        if (!IsEnabled || _disposed)
            return 0;

        lock (_lockObject)
        {
            var now = DateTime.Now;
            var oneSecondAgo = now.AddSeconds(-1);
            
            // 清理过期的时间戳
            while (_sendTimestamps.TryPeek(out var timestamp) && timestamp < oneSecondAgo)
            {
                _sendTimestamps.TryDequeue(out _);
            }
            
            return _sendTimestamps.Count;
        }
    }

    /// <summary>
    /// 获取流控制统计信息
    /// </summary>
    /// <returns>流控制统计信息</returns>
    public FlowControlStatistics GetStatistics()
    {
        return new FlowControlStatistics
        {
            CurrentStatus = _currentStatus,
            FlowControlType = FlowControlType,
            IsEnabled = IsEnabled,
            SendRateLimit = _sendRateLimit,
            CurrentSendRate = GetCurrentSendRate(),
            TotalBytesSent = _totalBytesSent,
            TotalBytesReceived = _totalBytesReceived,
            IsXoffReceived = _isXoffReceived,
            IsRtsPaused = _isRtsPaused,
            CongestionThreshold = CongestionThreshold
        };
    }

    /// <summary>
    /// 监控流控制状态
    /// </summary>
    /// <param name="state">状态对象</param>
    private void MonitorFlowControl(object? state)
    {
        if (_disposed)
            return;

        try
        {
            // 这里可以添加更多的监控逻辑
            // 比如检测拥塞、调整发送速率等
            
            var currentRate = GetCurrentSendRate();
            if (_sendRateLimit > 0 && currentRate > _sendRateLimit * 1.1) // 超过限制10%
            {
                _logger?.LogWarning("发送速率 {CurrentRate} 超过限制 {Limit}", currentRate, _sendRateLimit);
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "监控流控制状态时发生错误");
        }
    }

    /// <summary>
    /// 更新流控制状态
    /// </summary>
    /// <param name="newStatus">新状态</param>
    private void UpdateStatus(FlowControlStatus newStatus)
    {
        if (_currentStatus != newStatus)
        {
            var oldStatus = _currentStatus;
            _currentStatus = newStatus;
            
            _logger?.LogDebug("流控制状态变更: {OldStatus} -> {NewStatus}", oldStatus, newStatus);
            
            StatusChanged?.Invoke(this, new FlowControlStatusChangedEventArgs(oldStatus, newStatus));
        }
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            _monitorTimer?.Dispose();
            _disposed = true;
            _logger?.LogDebug("流控制管理器已释放");
        }
    }
}
