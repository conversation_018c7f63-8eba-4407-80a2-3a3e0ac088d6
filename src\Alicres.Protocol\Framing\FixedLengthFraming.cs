using Alicres.Protocol.Interfaces;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Framing;

/// <summary>
/// 固定长度帧处理器，按照固定长度分割消息帧
/// </summary>
public class FixedLengthFraming : AbstractMessageFraming
{
    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public override string FramingName => "FixedLengthFraming";

    /// <summary>
    /// 帧模式
    /// </summary>
    public override FramingMode Mode => FramingMode.FixedLength;

    /// <summary>
    /// 固定帧长度
    /// </summary>
    public int FrameLength { get; set; }

    /// <summary>
    /// 填充字节（当消息长度不足时使用）
    /// </summary>
    public byte PaddingByte { get; set; } = 0x00;

    /// <summary>
    /// 是否自动填充不足长度的消息
    /// </summary>
    public bool AutoPadding { get; set; } = true;

    /// <summary>
    /// 是否自动截断超长的消息
    /// </summary>
    public bool AutoTruncate { get; set; } = true;

    /// <summary>
    /// 是否自动移除填充字节
    /// </summary>
    public bool AutoTrimPadding { get; set; } = true;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="frameLength">固定帧长度</param>
    /// <param name="logger">日志记录器</param>
    public FixedLengthFraming(int frameLength, ILogger? logger = null) : base(logger)
    {
        if (frameLength <= 0)
            throw new ArgumentException("帧长度必须大于0", nameof(frameLength));

        FrameLength = frameLength;
    }

    /// <summary>
    /// 检查数据是否包含完整的帧
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果包含完整帧返回 true，否则返回 false</returns>
    public override bool HasCompleteFrame(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);
        return data.Length >= FrameLength;
    }

    /// <summary>
    /// 获取帧的预期长度
    /// </summary>
    /// <param name="data">帧数据的开始部分</param>
    /// <returns>帧的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedFrameLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);
        
        // 固定长度帧总是返回配置的帧长度
        return data.Length >= FrameLength ? FrameLength : -1;
    }

    /// <summary>
    /// 为消息添加帧信息的具体实现
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <returns>添加帧信息后的数据</returns>
    protected override byte[] AddFrameToMessage(byte[] message)
    {
        ArgumentNullException.ThrowIfNull(message);

        if (message.Length == FrameLength)
        {
            // 长度正好匹配，直接返回
            Logger?.LogTrace("消息长度正好匹配帧长度 {Length}", FrameLength);
            return message;
        }
        else if (message.Length > FrameLength)
        {
            // 消息太长，截断或抛出异常
            if (AutoTruncate)
            {
                Logger?.LogWarning("消息长度 {MessageLength} 超过帧长度 {FrameLength}，将被截断",
                    message.Length, FrameLength);

                var truncatedMessage = new byte[FrameLength];
                Array.Copy(message, truncatedMessage, FrameLength);
                return truncatedMessage;
            }
            else
            {
                throw new ArgumentException($"消息长度 {message.Length} 超过帧长度 {FrameLength}");
            }
        }
        else
        {
            // 消息太短，填充
            if (AutoPadding)
            {
                var paddedMessage = new byte[FrameLength];
                Array.Copy(message, paddedMessage, message.Length);
                
                // 用填充字节填充剩余空间
                for (int i = message.Length; i < FrameLength; i++)
                {
                    paddedMessage[i] = PaddingByte;
                }

                Logger?.LogTrace("消息长度 {MessageLength} 不足帧长度 {FrameLength}，已填充", 
                    message.Length, FrameLength);
                
                return paddedMessage;
            }
            else
            {
                throw new ArgumentException($"消息长度 {message.Length} 小于帧长度 {FrameLength}");
            }
        }
    }

    /// <summary>
    /// 从帧数据中移除帧信息的具体实现
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>移除帧信息后的原始消息</returns>
    protected override byte[] RemoveFrameFromMessage(byte[] framedData)
    {
        ArgumentNullException.ThrowIfNull(framedData);

        if (framedData.Length != FrameLength)
        {
            Logger?.LogWarning("帧数据长度 {DataLength} 与预期帧长度 {FrameLength} 不匹配", 
                framedData.Length, FrameLength);
        }

        if (!AutoTrimPadding)
        {
            // 不自动移除填充，直接返回
            return framedData;
        }

        // 自动移除尾部的填充字节
        int actualLength = framedData.Length;
        for (int i = framedData.Length - 1; i >= 0; i--)
        {
            if (framedData[i] != PaddingByte)
            {
                actualLength = i + 1;
                break;
            }
        }

        if (actualLength == framedData.Length)
        {
            // 没有填充字节
            return framedData;
        }

        // 创建移除填充后的数组
        var message = new byte[actualLength];
        Array.Copy(framedData, message, actualLength);

        Logger?.LogTrace("移除了 {PaddingCount} 个填充字节，消息长度: {Length}", 
            framedData.Length - actualLength, actualLength);

        return message;
    }

    /// <summary>
    /// 设置填充配置
    /// </summary>
    /// <param name="paddingByte">填充字节</param>
    /// <param name="autoPadding">是否自动填充</param>
    /// <param name="autoTruncate">是否自动截断</param>
    /// <param name="autoTrimPadding">是否自动移除填充</param>
    public void SetPaddingConfiguration(byte paddingByte, bool autoPadding, bool autoTruncate, bool autoTrimPadding)
    {
        PaddingByte = paddingByte;
        AutoPadding = autoPadding;
        AutoTruncate = autoTruncate;
        AutoTrimPadding = autoTrimPadding;

        Logger?.LogDebug("填充配置已更新: 填充字节=0x{PaddingByte:X2}, 自动填充={AutoPadding}, 自动截断={AutoTruncate}, 自动移除={AutoTrimPadding}",
            paddingByte, autoPadding, autoTruncate, autoTrimPadding);
    }

    /// <summary>
    /// 获取配置信息的字符串表示
    /// </summary>
    /// <returns>配置信息</returns>
    public override string ToString()
    {
        return $"{FramingName} (帧长度: {FrameLength}, 填充字节: 0x{PaddingByte:X2}, 自动填充: {AutoPadding}, 自动截断: {AutoTruncate}, 自动移除填充: {AutoTrimPadding})";
    }
}
