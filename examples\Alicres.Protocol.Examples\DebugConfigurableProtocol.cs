using Alicres.Protocol.Configuration;
using Alicres.Protocol.Parsers;
using Alicres.Protocol.Protocols.Custom;

namespace Alicres.Protocol.Examples;

/// <summary>
/// 调试可配置协议的问题
/// </summary>
public static class DebugConfigurableProtocol
{
    /// <summary>
    /// 调试多字节帧头问题
    /// </summary>
    public static async Task DebugMultiByteHeader()
    {
        Console.WriteLine("=== 调试多字节帧头问题 ===");

        try
        {
            // 创建多字节帧头配置
            var config = CustomProtocolConfiguration.CreateMultiByteHeaderConfiguration("AA55BB");
            Console.WriteLine($"配置创建成功: {config.Name}");
            Console.WriteLine($"字段数量: {config.Fields.Count}");
            
            foreach (var field in config.Fields)
            {
                Console.WriteLine($"  字段: {field}");
            }

            var parser = new ConfigurableProtocolParser(config);
            Console.WriteLine("解析器创建成功");

            // 创建测试消息
            var frameHeaderLength = config.GetFrameHeaderField()?.FixedValue?.Length ?? 0;
            Console.WriteLine($"帧头长度: {frameHeaderLength}");
            
            var totalLength = frameHeaderLength + 3 + 1 + 1 + 2; // 帧头 + 源地址 + 目标地址 + 命令码 + 长度字段 + 数据(1字节) + CRC16(2字节)
            Console.WriteLine($"计算的总长度: {totalLength}");
            
            var message = parser.CreateMessage(
                ("SourceAddress", (byte)0x10),
                ("TargetAddress", (byte)0x20),
                ("CommandCode", (byte)0x30),
                ("DataLength", (byte)totalLength),
                ("DataField", new byte[] { 0x99 })
            );

            Console.WriteLine("消息创建成功");
            Console.WriteLine($"消息字段值:");
            foreach (var field in message.GetAllFieldValues())
            {
                Console.WriteLine($"  {field.Key}: {FormatValue(field.Value)}");
            }

            // 检查验证
            Console.WriteLine($"消息验证结果: {message.IsValid}");

            if (message.IsValid)
            {
                var serialized = await parser.SerializeAsync(message);
                var hexString = BitConverter.ToString(serialized).Replace("-", " ");
                Console.WriteLine($"序列化成功: {hexString}");
            }
            else
            {
                Console.WriteLine("消息验证失败，无法序列化");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"调试过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 调试标准配置
    /// </summary>
    public static async Task DebugStandardConfiguration()
    {
        Console.WriteLine("=== 调试标准配置 ===");

        try
        {
            var config = CustomProtocolConfiguration.CreateStandardConfiguration();
            Console.WriteLine($"标准配置: {config.Name}");
            Console.WriteLine($"字段数量: {config.Fields.Count}");
            
            foreach (var field in config.Fields)
            {
                Console.WriteLine($"  字段: {field}");
            }

            var parser = new ConfigurableProtocolParser(config);

            // 解析示例数据
            var message = await parser.ParseFromHexStringAsync("7E F1 05 55 0A 00 00 10 F5 82");
            if (message != null)
            {
                Console.WriteLine("解析成功");
                Console.WriteLine($"消息验证结果: {message.IsValid}");
                Console.WriteLine($"消息字段值:");
                foreach (var field in message.GetAllFieldValues())
                {
                    Console.WriteLine($"  {field.Key}: {FormatValue(field.Value)}");
                }
            }
            else
            {
                Console.WriteLine("解析失败");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"调试过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 格式化值用于显示
    /// </summary>
    private static string FormatValue(object? value)
    {
        return value switch
        {
            null => "null",
            byte b => $"0x{b:X2}",
            ushort us => $"0x{us:X4}",
            uint ui => $"0x{ui:X8}",
            byte[] ba => BitConverter.ToString(ba).Replace("-", " "),
            _ => value.ToString() ?? "null"
        };
    }

    /// <summary>
    /// 测试简化配置
    /// </summary>
    public static async Task TestSimplifiedConfiguration()
    {
        Console.WriteLine("=== 测试简化配置 ===");

        try
        {
            // 创建最简单的配置
            var config = new ProtocolConfiguration
            {
                Name = "SimpleTest",
                Version = "1.0.0",
                MinFrameLength = 4,
                MaxFrameLength = 10
            };

            // 只添加必要字段
            config.AddField(ProtocolFieldDescriptor.CreateFixedValue("Header", new byte[] { 0xAA, 0x55 }, "帧头"))
                  .AddField(ProtocolFieldDescriptor.CreateByte("Data", "数据字节"));

            config.SetFrameHeader("Header");

            Console.WriteLine($"简化配置: {config.Name}");
            Console.WriteLine($"字段数量: {config.Fields.Count}");
            
            var (isValid, errors) = config.Validate();
            Console.WriteLine($"配置验证: {(isValid ? "通过" : "失败")}");
            if (!isValid)
            {
                foreach (var error in errors)
                {
                    Console.WriteLine($"  错误: {error}");
                }
                return;
            }

            var parser = new ConfigurableProtocolParser(config);
            var message = parser.CreateMessage(("Data", (byte)0x99));

            Console.WriteLine("消息创建成功");
            Console.WriteLine($"消息验证结果: {message.IsValid}");
            Console.WriteLine($"消息字段值:");
            foreach (var field in message.GetAllFieldValues())
            {
                Console.WriteLine($"  {field.Key}: {FormatValue(field.Value)}");
            }

            if (message.IsValid)
            {
                var serialized = await parser.SerializeAsync(message);
                var hexString = BitConverter.ToString(serialized).Replace("-", " ");
                Console.WriteLine($"序列化成功: {hexString}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"测试过程中发生错误: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }

        Console.WriteLine();
    }
}
