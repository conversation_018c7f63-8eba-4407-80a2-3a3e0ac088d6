namespace Alicres.Protocol.Interfaces;

/// <summary>
/// 协议数据校验器接口
/// </summary>
public interface IProtocolValidator
{
    /// <summary>
    /// 校验器名称
    /// </summary>
    string ValidatorName { get; }

    /// <summary>
    /// 验证数据完整性
    /// </summary>
    /// <param name="data">待验证的数据</param>
    /// <returns>如果数据有效返回 true，否则返回 false</returns>
    bool Validate(byte[] data);

    /// <summary>
    /// 验证数据完整性（异步）
    /// </summary>
    /// <param name="data">待验证的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果数据有效返回 true，否则返回 false</returns>
    Task<bool> ValidateAsync(byte[] data, CancellationToken cancellationToken = default);

    /// <summary>
    /// 计算数据的校验值
    /// </summary>
    /// <param name="data">待计算的数据</param>
    /// <returns>校验值</returns>
    byte[] CalculateChecksum(byte[] data);

    /// <summary>
    /// 为数据添加校验信息
    /// </summary>
    /// <param name="data">原始数据</param>
    /// <returns>包含校验信息的数据</returns>
    byte[] AddChecksum(byte[] data);

    /// <summary>
    /// 从数据中移除校验信息
    /// </summary>
    /// <param name="data">包含校验信息的数据</param>
    /// <returns>移除校验信息后的原始数据</returns>
    byte[] RemoveChecksum(byte[] data);
}
