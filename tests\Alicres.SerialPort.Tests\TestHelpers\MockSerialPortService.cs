using Alicres.SerialPort.Interfaces;
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Exceptions;
using Microsoft.Extensions.Logging;
using System.Text;

namespace Alicres.SerialPort.Tests.TestHelpers;

/// <summary>
/// Mock串口服务实现，用于测试环境
/// </summary>
public class MockSerialPortService : ISerialPortService
{
    private readonly MockSerialPort _mockSerialPort;
    private readonly ILogger<MockSerialPortService> _logger;
    private readonly object _lockObject;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="configuration">串口配置</param>
    /// <param name="logger">日志记录器</param>
    public MockSerialPortService(SerialPortConfiguration configuration, ILogger<MockSerialPortService> logger)
    {
        Configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _lockObject = new object();

        _mockSerialPort = new MockSerialPort(configuration.PortName, configuration.BaudRate)
        {
            DataBits = configuration.DataBits,
            StopBits = configuration.StopBits,
            Parity = configuration.Parity,
            ReadTimeout = configuration.ReadTimeout,
            WriteTimeout = configuration.WriteTimeout
        };

        Status = new SerialPortStatus
        {
            PortName = configuration.PortName,
            ConnectionState = SerialPortConnectionState.Disconnected,
            LastConnectedTime = null,
            LastDisconnectedTime = DateTime.UtcNow,
            BytesSent = 0,
            BytesReceived = 0,
            ErrorCount = 0,
            ReconnectAttempts = 0
        };

        // 订阅Mock串口事件
        _mockSerialPort.DataReceived += OnMockDataReceived;
        _mockSerialPort.ErrorReceived += OnMockErrorReceived;
    }

    /// <summary>
    /// 当前串口配置
    /// </summary>
    public SerialPortConfiguration Configuration { get; private set; }

    /// <summary>
    /// 当前串口状态
    /// </summary>
    public SerialPortStatus Status { get; private set; }

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected => _mockSerialPort.IsOpen;

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    /// <summary>
    /// 连接状态变化事件
    /// </summary>
    public event EventHandler<SerialPortStatusChangedEventArgs>? StatusChanged;

    /// <summary>
    /// 错误发生事件
    /// </summary>
    public event EventHandler<SerialPortErrorEventArgs>? ErrorOccurred;

    /// <summary>
    /// 流控制状态变化事件
    /// </summary>
#pragma warning disable CS0067 // 事件从不使用
    public event EventHandler<FlowControlStatusChangedEventArgs>? FlowControlStatusChanged;
#pragma warning restore CS0067

    /// <summary>
    /// 获取系统中可用的串口列表
    /// </summary>
    /// <returns>可用串口名称数组</returns>
    public string[] GetAvailablePorts()
    {
        return new[] { "COM1", "COM2", "COM3", "COM_TEST", "COM_LOOPBACK" };
    }

    /// <summary>
    /// 配置串口参数
    /// </summary>
    /// <param name="configuration">串口配置</param>
    public void Configure(SerialPortConfiguration configuration)
    {
        ArgumentNullException.ThrowIfNull(configuration);
        ThrowIfDisposed();

        // 验证端口名称
        if (string.IsNullOrWhiteSpace(configuration.PortName))
            throw new ArgumentException("端口名称不能为空", nameof(configuration));

        lock (_lockObject)
        {
            if (IsConnected)
                throw new SerialPortConfigurationException("无法在连接状态下修改配置", configuration.PortName);

            Configuration = configuration;
            Status.PortName = configuration.PortName;

            _logger.LogInformation("Mock串口配置已更新: {PortName}", configuration.PortName);
        }
    }

    /// <summary>
    /// 异步打开串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功打开返回 true，否则返回 false</returns>
    public async Task<bool> OpenAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        return await Task.Run(() =>
        {
            lock (_lockObject)
            {
                if (IsConnected)
                {
                    _logger.LogWarning("Mock串口 {PortName} 已经打开", Configuration.PortName);
                    return true;
                }

                try
                {
                    UpdateConnectionState(SerialPortConnectionState.Connecting);
                    _mockSerialPort.Open();
                    UpdateConnectionState(SerialPortConnectionState.Connected);

                    Status.LastConnectedTime = DateTime.UtcNow;
                    Status.ResetReconnectAttempts();

                    _logger.LogInformation("Mock串口 {PortName} 打开成功", Configuration.PortName);
                    return true;
                }
                catch (Exception ex)
                {
                    var errorMessage = $"打开Mock串口 {Configuration.PortName} 失败: {ex.Message}";
                    _logger.LogError(ex, errorMessage);

                    Status.RecordError(errorMessage);
                    OnErrorOccurred(new SerialPortConnectionException(errorMessage, Configuration.PortName, ex));
                    UpdateConnectionState(SerialPortConnectionState.Disconnected);

                    return false;
                }
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 异步关闭串口连接
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>如果成功关闭返回 true，否则返回 false</returns>
    public async Task<bool> CloseAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        return await Task.Run(() =>
        {
            lock (_lockObject)
            {
                if (!IsConnected)
                {
                    _logger.LogWarning("Mock串口 {PortName} 已经关闭", Configuration.PortName);
                    return true;
                }

                try
                {
                    UpdateConnectionState(SerialPortConnectionState.Disconnecting);
                    _mockSerialPort.Close();
                    UpdateConnectionState(SerialPortConnectionState.Disconnected);

                    Status.LastDisconnectedTime = DateTime.UtcNow;

                    _logger.LogInformation("Mock串口 {PortName} 关闭成功", Configuration.PortName);
                    return true;
                }
                catch (Exception ex)
                {
                    var errorMessage = $"关闭Mock串口 {Configuration.PortName} 失败: {ex.Message}";
                    _logger.LogError(ex, errorMessage);

                    Status.RecordError(errorMessage);
                    OnErrorOccurred(new SerialPortConnectionException(errorMessage, Configuration.PortName, ex));

                    return false;
                }
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 异步发送数据
    /// </summary>
    /// <param name="data">要发送的数据</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际发送的字节数</returns>
    public async Task<int> SendAsync(byte[] data, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(data);
        ThrowIfDisposed();

        if (data.Length == 0)
            throw new ArgumentNullException(nameof(data), "数据不能为空");

        if (!IsConnected)
            throw new SerialPortDataException("端口未打开");

        return await Task.Run(() =>
        {
            try
            {
                _mockSerialPort.Write(data, 0, data.Length);
                Status.BytesSent += data.Length;

                _logger.LogDebug("Mock串口 {PortName} 发送数据: {Length} 字节", Configuration.PortName, data.Length);
                return data.Length;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Mock串口 {Configuration.PortName} 发送数据失败: {ex.Message}";
                _logger.LogError(ex, errorMessage);

                Status.RecordError(errorMessage);
                throw new SerialPortDataException(errorMessage, ex);
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 异步发送文本数据
    /// </summary>
    /// <param name="text">要发送的文本</param>
    /// <param name="encoding">文本编码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际发送的字节数</returns>
    public async Task<int> SendTextAsync(string text, Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(text);

        if (string.IsNullOrEmpty(text))
            throw new ArgumentNullException(nameof(text), "文本不能为空");

        encoding ??= Encoding.UTF8;

        var data = encoding.GetBytes(text);
        return await SendAsync(data, cancellationToken);
    }

    /// <summary>
    /// 异步读取数据
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">缓冲区偏移量</param>
    /// <param name="count">要读取的字节数</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际读取的字节数</returns>
    public async Task<int> ReadAsync(byte[] buffer, int offset, int count, CancellationToken cancellationToken = default)
    {
        ArgumentNullException.ThrowIfNull(buffer);
        ThrowIfDisposed();

        if (!IsConnected)
            throw new SerialPortDataException("端口未打开");

        if (offset < 0 || offset >= buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(offset));
        if (count < 0 || offset + count > buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(count));

        return await Task.Run(() =>
        {
            try
            {
                var bytesRead = _mockSerialPort.Read(buffer, offset, count);
                Status.BytesReceived += bytesRead;

                _logger.LogDebug("Mock串口 {PortName} 读取数据: {Length} 字节", Configuration.PortName, bytesRead);
                return bytesRead;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Mock串口 {Configuration.PortName} 读取数据失败: {ex.Message}";
                _logger.LogError(ex, errorMessage);

                Status.RecordError(errorMessage);
                throw new SerialPortDataException(errorMessage, ex);
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 异步读取数据（重载方法）
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>实际读取的字节数</returns>
    public async Task<int> ReadAsync(byte[] buffer, CancellationToken cancellationToken = default)
    {
        return await ReadAsync(buffer, 0, buffer.Length, cancellationToken);
    }

    /// <summary>
    /// 异步读取所有可用数据
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取到的数据</returns>
    public async Task<byte[]> ReadAllAvailableAsync(CancellationToken cancellationToken = default)
    {
        ThrowIfDisposed();

        if (!IsConnected)
            throw new SerialPortDataException("端口未打开");

        return await Task.Run(() =>
        {
            try
            {
                var availableBytes = _mockSerialPort.BytesToRead;
                if (availableBytes == 0)
                    return Array.Empty<byte>();

                var buffer = new byte[availableBytes];
                var bytesRead = _mockSerialPort.Read(buffer, 0, availableBytes);

                if (bytesRead < availableBytes)
                {
                    var actualData = new byte[bytesRead];
                    Array.Copy(buffer, actualData, bytesRead);
                    buffer = actualData;
                }

                Status.BytesReceived += bytesRead;
                _logger.LogDebug("Mock串口 {PortName} 读取所有可用数据: {Length} 字节", Configuration.PortName, bytesRead);

                return buffer;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Mock串口 {Configuration.PortName} 读取所有可用数据失败: {ex.Message}";
                _logger.LogError(ex, errorMessage);

                Status.RecordError(errorMessage);
                throw new SerialPortDataException(errorMessage, ex);
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 异步读取文本数据
    /// </summary>
    /// <param name="encoding">文本编码</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>读取的文本内容</returns>
    public async Task<string> ReadTextAsync(Encoding? encoding = null, CancellationToken cancellationToken = default)
    {
        encoding ??= Encoding.UTF8;
        ThrowIfDisposed();

        if (!IsConnected)
            throw new SerialPortDataException("端口未打开");

        return await Task.Run(() =>
        {
            try
            {
                var text = _mockSerialPort.ReadExisting();
                Status.BytesReceived += encoding.GetByteCount(text);

                _logger.LogDebug("Mock串口 {PortName} 读取文本: {Length} 字符", Configuration.PortName, text.Length);
                return text;
            }
            catch (Exception ex)
            {
                var errorMessage = $"Mock串口 {Configuration.PortName} 读取文本失败: {ex.Message}";
                _logger.LogError(ex, errorMessage);

                Status.RecordError(errorMessage);
                throw new SerialPortDataException(errorMessage, ex);
            }
        }, cancellationToken);
    }

    /// <summary>
    /// 清空接收缓冲区
    /// </summary>
    public void ClearReceiveBuffer()
    {
        ThrowIfDisposed();

        if (!IsConnected)
            return;

        try
        {
            // 清空Mock串口的接收队列
            while (_mockSerialPort.BytesToRead > 0)
            {
                var buffer = new byte[_mockSerialPort.BytesToRead];
                _mockSerialPort.Read(buffer, 0, buffer.Length);
            }

            _logger.LogDebug("Mock串口 {PortName} 接收缓冲区已清空", Configuration.PortName);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Mock串口 {Configuration.PortName} 清空接收缓冲区失败: {ex.Message}";
            _logger.LogError(ex, errorMessage);
            Status.RecordError(errorMessage);
        }
    }

    /// <summary>
    /// 清空发送缓冲区
    /// </summary>
    public void ClearSendBuffer()
    {
        ThrowIfDisposed();

        if (!IsConnected)
            return;

        try
        {
            // 清空Mock串口的发送队列
            _mockSerialPort.ClearSentData();

            _logger.LogDebug("Mock串口 {PortName} 发送缓冲区已清空", Configuration.PortName);
        }
        catch (Exception ex)
        {
            var errorMessage = $"Mock串口 {Configuration.PortName} 清空发送缓冲区失败: {ex.Message}";
            _logger.LogError(ex, errorMessage);
            Status.RecordError(errorMessage);
        }
    }

    /// <summary>
    /// 获取接收缓冲区中的字节数
    /// </summary>
    /// <returns>缓冲区中的字节数</returns>
    public int GetBytesToRead()
    {
        ThrowIfDisposed();

        if (!IsConnected)
            return 0;

        return _mockSerialPort.BytesToRead;
    }

    /// <summary>
    /// 获取发送缓冲区中的字节数
    /// </summary>
    /// <returns>缓冲区中的字节数</returns>
    public int GetBytesToWrite()
    {
        ThrowIfDisposed();

        if (!IsConnected)
            return 0;

        // Mock实现：返回已发送数据的总数
        return _mockSerialPort.GetSentData().Sum(data => data.Length);
    }

    /// <summary>
    /// 获取流控制统计信息
    /// </summary>
    /// <returns>流控制统计信息，如果未启用流控制则返回 null</returns>
    public FlowControlStatistics? GetFlowControlStatistics()
    {
        // Mock 实现：返回模拟的流控制统计信息
        return new FlowControlStatistics
        {
            CurrentStatus = FlowControlStatus.Normal,
            FlowControlType = FlowControlType.None,
            IsEnabled = false,
            SendRateLimit = 0,
            CurrentSendRate = 0,
            TotalBytesSent = Status.BytesSent,
            TotalBytesReceived = Status.BytesReceived,
            IsXoffReceived = false,
            IsRtsPaused = false,
            CongestionThreshold = 75
        };
    }

    /// <summary>
    /// 设置发送速率限制
    /// </summary>
    /// <param name="rateLimit">速率限制（字节/秒），0 表示无限制</param>
    public void SetSendRateLimit(int rateLimit)
    {
        // Mock 实现：记录日志但不实际限制
        _logger.LogDebug("Mock串口 {PortName} 设置发送速率限制: {Limit} 字节/秒", Configuration.PortName, rateLimit);
    }

    /// <summary>
    /// 发送 XON 字符（恢复发送）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送的字节数</returns>
    public async Task<int> SendXonAsync(CancellationToken cancellationToken = default)
    {
        // Mock 实现：发送 XON 字符
        var xonData = new byte[] { 0x11 }; // DC1
        return await SendAsync(xonData, cancellationToken);
    }

    /// <summary>
    /// 发送 XOFF 字符（暂停发送）
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>发送的字节数</returns>
    public async Task<int> SendXoffAsync(CancellationToken cancellationToken = default)
    {
        // Mock 实现：发送 XOFF 字符
        var xoffData = new byte[] { 0x13 }; // DC3
        return await SendAsync(xoffData, cancellationToken);
    }

    /// <summary>
    /// 设置 RTS 流控制状态
    /// </summary>
    /// <param name="isPaused">是否暂停</param>
    public void SetRtsFlowControl(bool isPaused)
    {
        // Mock 实现：记录日志但不实际控制
        _logger.LogDebug("Mock串口 {PortName} 设置 RTS 流控制: {Status}", Configuration.PortName, isPaused ? "暂停" : "恢复");
    }

    /// <summary>
    /// 获取当前发送速率
    /// </summary>
    /// <returns>发送速率（字节/秒）</returns>
    public double GetCurrentSendRate()
    {
        // Mock 实现：返回模拟的发送速率
        return 0.0;
    }

    /// <summary>
    /// 获取Mock串口实例（仅用于测试）
    /// </summary>
    /// <returns>Mock串口实例</returns>
    public MockSerialPort GetMockSerialPort() => _mockSerialPort;

    /// <summary>
    /// 更新连接状态
    /// </summary>
    /// <param name="newState">新状态</param>
    private void UpdateConnectionState(SerialPortConnectionState newState)
    {
        var oldState = Status.ConnectionState;
        Status.ConnectionState = newState;

        if (oldState != newState)
        {
            var args = new SerialPortStatusChangedEventArgs(Configuration.PortName, oldState, newState);
            StatusChanged?.Invoke(this, args);

            _logger.LogDebug("Mock串口 {PortName} 状态变化: {OldState} -> {NewState}",
                Configuration.PortName, oldState, newState);
        }
    }

    /// <summary>
    /// 处理Mock数据接收事件
    /// </summary>
    private void OnMockDataReceived(object sender, System.IO.Ports.SerialDataReceivedEventArgs e)
    {
        try
        {
            var availableBytes = _mockSerialPort.BytesToRead;
            if (availableBytes > 0)
            {
                var buffer = new byte[availableBytes];
                var bytesRead = _mockSerialPort.Read(buffer, 0, buffer.Length);
                
                if (bytesRead > 0)
                {
                    var actualData = new byte[bytesRead];
                    Array.Copy(buffer, actualData, bytesRead);

                    Status.BytesReceived += bytesRead;

                    var serialPortData = new SerialPortData(actualData, Configuration.PortName, SerialPortDataDirection.Received);
                    var args = new SerialPortDataReceivedEventArgs(serialPortData);
                    DataReceived?.Invoke(this, args);

                    _logger.LogDebug("Mock串口 {PortName} 接收数据: {Length} 字节", Configuration.PortName, bytesRead);
                }
            }
        }
        catch (Exception ex)
        {
            var errorMessage = $"Mock串口 {Configuration.PortName} 处理接收数据时发生错误: {ex.Message}";
            _logger.LogError(ex, errorMessage);

            Status.RecordError(errorMessage);
            OnErrorOccurred(new SerialPortDataException(errorMessage, ex));
        }
    }

    /// <summary>
    /// 处理Mock错误接收事件
    /// </summary>
    private void OnMockErrorReceived(object sender, System.IO.Ports.SerialErrorReceivedEventArgs e)
    {
        var errorMessage = $"Mock串口 {Configuration.PortName} 发生错误: {e.EventType}";
        _logger.LogError(errorMessage);

        Status.RecordError(errorMessage);
        OnErrorOccurred(new SerialPortException(errorMessage));
    }

    /// <summary>
    /// 触发错误事件
    /// </summary>
    /// <param name="exception">异常信息</param>
    private void OnErrorOccurred(Exception exception)
    {
        var args = new SerialPortErrorEventArgs(Configuration.PortName, exception);
        ErrorOccurred?.Invoke(this, args);
    }

    /// <summary>
    /// 检查是否已释放
    /// </summary>
    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MockSerialPortService));
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            try
            {
                if (IsConnected)
                {
                    CloseAsync().Wait(TimeSpan.FromSeconds(5));
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "释放Mock串口服务时发生异常");
            }
            finally
            {
                _mockSerialPort?.Dispose();
                _disposed = true;
            }
        }
    }
}
