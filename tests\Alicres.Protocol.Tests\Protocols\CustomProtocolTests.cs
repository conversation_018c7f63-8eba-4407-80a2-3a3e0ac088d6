using Alicres.Protocol.Protocols.Custom;
using Alicres.Protocol.Validators;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.Protocol.Tests.Protocols;

/// <summary>
/// 自定义协议测试
/// </summary>
public class CustomProtocolTests : IDisposable
{
    private readonly Mock<ILogger<CustomProtocolParser>> _mockLogger;
    private readonly CustomProtocolParser _parser;
    private readonly CustomCrc16Validator _validator;

    // 测试数据：7E F1 05 55 0A 00 00 10 F5 82
    private readonly byte[] _validTestData = { 0x7E, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 };

    public CustomProtocolTests()
    {
        _mockLogger = new Mock<ILogger<CustomProtocolParser>>();
        _parser = new CustomProtocolParser(_mockLogger.Object);
        _validator = new CustomCrc16Validator();
    }

    [Fact]
    public void Constructor_ShouldInitializeCorrectly()
    {
        // Assert
        _parser.ProtocolName.Should().Be("CustomProtocol");
        _parser.ProtocolVersion.Should().Be("1.0.0");
        _parser.ValidationEnabled.Should().BeTrue();
        _parser.Validator.Should().NotBeNull();
    }

    [Fact]
    public void IsCompleteMessage_WithValidData_ShouldReturnTrue()
    {
        // Act
        var result = _parser.IsCompleteMessage(_validTestData);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void IsCompleteMessage_WithInsufficientData_ShouldReturnFalse()
    {
        // Arrange
        var incompleteData = new byte[] { 0x7E, 0xF1, 0x05 };

        // Act
        var result = _parser.IsCompleteMessage(incompleteData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void IsCompleteMessage_WithInvalidHeader_ShouldReturnFalse()
    {
        // Arrange
        var invalidHeaderData = new byte[] { 0xFF, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 };

        // Act
        var result = _parser.IsCompleteMessage(invalidHeaderData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void GetExpectedMessageLength_WithValidData_ShouldReturnCorrectLength()
    {
        // Act
        var result = _parser.GetExpectedMessageLength(_validTestData);

        // Assert
        result.Should().Be(10); // 数据长度字段的值
    }

    [Fact]
    public async Task ParseAsync_WithValidData_ShouldReturnCorrectMessage()
    {
        // Act
        var result = await _parser.ParseAsync(_validTestData);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<CustomProtocolMessage>();

        var customMessage = result as CustomProtocolMessage;
        customMessage!.FrameHeader.Should().Be(0x7E);
        customMessage.SourceAddress.Should().Be(0xF1);
        customMessage.TargetAddress.Should().Be(0x05);
        customMessage.CommandCode.Should().Be(0x55);
        customMessage.DataLength.Should().Be(0x0A);
        customMessage.DataField.Should().Equal(new byte[] { 0x00, 0x00, 0x10 });
        customMessage.Crc16.Should().Be(0x82F5); // 小端字节序
    }

    [Fact]
    public async Task ParseFromHexStringAsync_WithValidHexString_ShouldReturnCorrectMessage()
    {
        // Arrange
        var hexString = "7E F1 05 55 0A 00 00 10 F5 82";

        // Act
        var result = await _parser.ParseFromHexStringAsync(hexString);

        // Assert
        result.Should().NotBeNull();
        result!.SourceAddress.Should().Be(0xF1);
        result.TargetAddress.Should().Be(0x05);
        result.CommandCode.Should().Be(0x55);
    }

    [Fact]
    public async Task SerializeAsync_WithCustomMessage_ShouldReturnCorrectBytes()
    {
        // Arrange
        var message = _parser.CreateMessage(0xF1, 0x05, 0x55, new byte[] { 0x00, 0x00, 0x10 });

        // Act
        var result = await _parser.SerializeAsync(message);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(10);
        result[0].Should().Be(0x7E); // 帧头
        result[1].Should().Be(0xF1); // 源地址
        result[2].Should().Be(0x05); // 目标地址
        result[3].Should().Be(0x55); // 命令码
        result[4].Should().Be(0x0A); // 数据长度
        result[5].Should().Be(0x00); // 数据字段
        result[6].Should().Be(0x00);
        result[7].Should().Be(0x10);
        // CRC16 字段会由序列化过程计算
    }

    [Fact]
    public void CreateMessage_WithValidParameters_ShouldCreateCorrectMessage()
    {
        // Act
        var result = _parser.CreateMessage(0x01, 0x02, 0x03, new byte[] { 0x11, 0x22 });

        // Assert
        result.Should().NotBeNull();
        result.SourceAddress.Should().Be(0x01);
        result.TargetAddress.Should().Be(0x02);
        result.CommandCode.Should().Be(0x03);
        result.DataField.Should().Equal(new byte[] { 0x11, 0x22 });
        result.DataLength.Should().Be(9); // 5 + 2 + 2 (帧头+地址+命令+长度+数据+CRC)
    }

    [Theory]
    [InlineData("")]
    [InlineData("7E")]
    [InlineData("7E F1")]
    [InlineData("FF F1 05 55 0A 00 00 10 F5 82")] // 无效帧头
    public async Task ParseFromHexStringAsync_WithInvalidData_ShouldReturnNull(string hexString)
    {
        // Act
        var result = await _parser.ParseFromHexStringAsync(hexString);

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public void CustomProtocolMessage_Constructor_WithRawData_ShouldParseCorrectly()
    {
        // Act
        var message = new CustomProtocolMessage(_validTestData);

        // Assert
        message.FrameHeader.Should().Be(0x7E);
        message.SourceAddress.Should().Be(0xF1);
        message.TargetAddress.Should().Be(0x05);
        message.CommandCode.Should().Be(0x55);
        message.DataLength.Should().Be(0x0A);
        message.DataField.Should().Equal(new byte[] { 0x00, 0x00, 0x10 });
        message.Crc16.Should().Be(0x82F5);
    }

    [Fact]
    public void CustomProtocolMessage_Validate_WithValidMessage_ShouldReturnTrue()
    {
        // Arrange
        var message = new CustomProtocolMessage(_validTestData);

        // Act
        var result = message.Validate();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void CustomProtocolMessage_ToBytes_ShouldReturnCorrectByteArray()
    {
        // Arrange
        var message = new CustomProtocolMessage(0xF1, 0x05, 0x55, new byte[] { 0x00, 0x00, 0x10 });
        message.SetCrc16(0x82F5);

        // Act
        var result = message.ToBytes();

        // Assert
        result.Should().Equal(_validTestData);
    }

    [Fact]
    public void CustomProtocolMessage_GetDataForCrcCalculation_ShouldExcludeHeaderAndCrc()
    {
        // Arrange
        var message = new CustomProtocolMessage(_validTestData);

        // Act
        var result = message.GetDataForCrcCalculation();

        // Assert
        result.Should().Equal(new byte[] { 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10 });
    }

    [Fact]
    public void CustomProtocolMessage_ToString_ShouldReturnFormattedString()
    {
        // Arrange
        var message = new CustomProtocolMessage(_validTestData);

        // Act
        var result = message.ToString();

        // Assert
        result.Should().Contain("CustomProtocol.CustomMessage");
        result.Should().Contain("源地址: 0xF1");
        result.Should().Contain("目标地址: 0x05");
        result.Should().Contain("命令码: 0x55");
        result.Should().Contain("CRC16: 0x82F5");
    }

    [Fact]
    public void CustomProtocolMessage_Constructor_WithInvalidData_ShouldThrowException()
    {
        // Arrange
        var invalidData = new byte[] { 0x7E, 0xF1 }; // 数据长度不足

        // Act & Assert
        Assert.Throws<ArgumentException>(() => new CustomProtocolMessage(invalidData));
    }

    [Fact]
    public void CustomProtocolMessage_Constructor_WithMismatchedLength_ShouldThrowException()
    {
        // Arrange
        var invalidData = new byte[] { 0x7E, 0xF1, 0x05, 0x55, 0xFF, 0x00, 0x00, 0x10, 0xF5, 0x82 }; // 长度字段不匹配

        // Act & Assert
        Assert.Throws<ArgumentException>(() => new CustomProtocolMessage(invalidData));
    }

    public void Dispose()
    {
        // 清理资源
    }
}
