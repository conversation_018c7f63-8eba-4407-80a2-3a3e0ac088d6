using Alicres.Protocol.Models.Performance;
using Alicres.Protocol.Models.EventArgs;

namespace Alicres.Protocol.Interfaces;

/// <summary>
/// 性能监控接口，定义性能数据收集和分析功能
/// </summary>
public interface IPerformanceMonitor
{
    /// <summary>
    /// 监控器名称
    /// </summary>
    string MonitorName { get; }

    /// <summary>
    /// 是否启用监控
    /// </summary>
    bool IsEnabled { get; set; }

    /// <summary>
    /// 监控间隔（毫秒）
    /// </summary>
    int MonitorInterval { get; set; }

    /// <summary>
    /// 开始监控
    /// </summary>
    void StartMonitoring();

    /// <summary>
    /// 停止监控
    /// </summary>
    void StopMonitoring();

    /// <summary>
    /// 记录数据传输事件
    /// </summary>
    /// <param name="dataLength">数据长度</param>
    /// <param name="direction">传输方向</param>
    /// <param name="timestamp">时间戳</param>
    void RecordDataTransfer(int dataLength, DataDirection direction, DateTime? timestamp = null);

    /// <summary>
    /// 记录协议解析事件
    /// </summary>
    /// <param name="protocolName">协议名称</param>
    /// <param name="parseTime">解析耗时</param>
    /// <param name="success">是否成功</param>
    /// <param name="timestamp">时间戳</param>
    void RecordProtocolParse(string protocolName, TimeSpan parseTime, bool success, DateTime? timestamp = null);

    /// <summary>
    /// 记录错误事件
    /// </summary>
    /// <param name="errorType">错误类型</param>
    /// <param name="errorMessage">错误消息</param>
    /// <param name="timestamp">时间戳</param>
    void RecordError(string errorType, string errorMessage, DateTime? timestamp = null);

    /// <summary>
    /// 获取实时性能指标
    /// </summary>
    /// <returns>实时性能指标</returns>
    PerformanceMetrics GetRealTimeMetrics();

    /// <summary>
    /// 获取历史性能数据
    /// </summary>
    /// <param name="startTime">开始时间</param>
    /// <param name="endTime">结束时间</param>
    /// <returns>历史性能数据</returns>
    PerformanceHistory GetHistoryData(DateTime startTime, DateTime endTime);

    /// <summary>
    /// 获取性能报告
    /// </summary>
    /// <param name="reportType">报告类型</param>
    /// <returns>性能报告</returns>
    PerformanceReport GenerateReport(PerformanceReportType reportType);

    /// <summary>
    /// 重置性能统计
    /// </summary>
    void ResetStatistics();

    /// <summary>
    /// 性能指标更新事件
    /// </summary>
    event EventHandler<PerformanceMetricsUpdatedEventArgs>? MetricsUpdated;

    /// <summary>
    /// 性能警告事件
    /// </summary>
    event EventHandler<PerformanceWarningEventArgs>? PerformanceWarning;
}

/// <summary>
/// 数据传输方向枚举
/// </summary>
public enum DataDirection
{
    /// <summary>
    /// 发送
    /// </summary>
    Send,

    /// <summary>
    /// 接收
    /// </summary>
    Receive
}

/// <summary>
/// 性能报告类型枚举
/// </summary>
public enum PerformanceReportType
{
    /// <summary>
    /// 实时报告
    /// </summary>
    RealTime,

    /// <summary>
    /// 小时报告
    /// </summary>
    Hourly,

    /// <summary>
    /// 日报告
    /// </summary>
    Daily,

    /// <summary>
    /// 周报告
    /// </summary>
    Weekly,

    /// <summary>
    /// 月报告
    /// </summary>
    Monthly,

    /// <summary>
    /// 自定义时间范围报告
    /// </summary>
    Custom
}
