using System.IO.Ports;
using System.Text;
using System.Reflection;

namespace Alicres.SerialPort.Tests.TestHelpers;

/// <summary>
/// Mock串口实现，用于测试环境
/// </summary>
public class MockSerialPort : IDisposable
{
    private readonly Queue<byte[]> _receivedDataQueue;
    private readonly Queue<byte[]> _sentDataQueue;
    private readonly object _lockObject;
    private bool _isOpen;
    private bool _disposed;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="portName">端口名称</param>
    /// <param name="baudRate">波特率</param>
    public MockSerialPort(string portName, int baudRate = 9600)
    {
        PortName = portName;
        BaudRate = baudRate;
        DataBits = 8;
        StopBits = StopBits.One;
        Parity = Parity.None;
        ReadTimeout = 1000;
        WriteTimeout = 1000;
        
        _receivedDataQueue = new Queue<byte[]>();
        _sentDataQueue = new Queue<byte[]>();
        _lockObject = new object();
        _isOpen = false;
    }

    /// <summary>
    /// 端口名称
    /// </summary>
    public string PortName { get; set; }

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; }

    /// <summary>
    /// 数据位
    /// </summary>
    public int DataBits { get; set; }

    /// <summary>
    /// 停止位
    /// </summary>
    public StopBits StopBits { get; set; }

    /// <summary>
    /// 奇偶校验
    /// </summary>
    public Parity Parity { get; set; }

    /// <summary>
    /// 读取超时
    /// </summary>
    public int ReadTimeout { get; set; }

    /// <summary>
    /// 写入超时
    /// </summary>
    public int WriteTimeout { get; set; }

    /// <summary>
    /// 是否已打开
    /// </summary>
    public bool IsOpen => _isOpen && !_disposed;

    /// <summary>
    /// 可读取的字节数
    /// </summary>
    public int BytesToRead
    {
        get
        {
            lock (_lockObject)
            {
                return _receivedDataQueue.Sum(data => data.Length);
            }
        }
    }

    /// <summary>
    /// 数据接收事件
    /// </summary>
    public event SerialDataReceivedEventHandler? DataReceived;

    /// <summary>
    /// 错误接收事件
    /// </summary>
    public event SerialErrorReceivedEventHandler? ErrorReceived;

    /// <summary>
    /// 打开串口
    /// </summary>
    public void Open()
    {
        ThrowIfDisposed();
        
        if (_isOpen)
            throw new InvalidOperationException($"串口 {PortName} 已经打开");

        // 模拟串口打开失败的情况
        if (PortName.Contains("INVALID") || PortName.Contains("ERROR"))
            throw new UnauthorizedAccessException($"无法访问串口 {PortName}");

        _isOpen = true;
    }

    /// <summary>
    /// 关闭串口
    /// </summary>
    public void Close()
    {
        if (_isOpen)
        {
            _isOpen = false;
            lock (_lockObject)
            {
                _receivedDataQueue.Clear();
                _sentDataQueue.Clear();
            }
        }
    }

    /// <summary>
    /// 写入数据
    /// </summary>
    /// <param name="buffer">数据缓冲区</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">字节数</param>
    public void Write(byte[] buffer, int offset, int count)
    {
        ThrowIfDisposed();
        ThrowIfNotOpen();

        if (buffer == null)
            throw new ArgumentNullException(nameof(buffer));
        if (offset < 0 || offset >= buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(offset));
        if (count < 0 || offset + count > buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(count));

        var data = new byte[count];
        Array.Copy(buffer, offset, data, 0, count);

        lock (_lockObject)
        {
            _sentDataQueue.Enqueue(data);
        }

        // 模拟回环测试：发送的数据会被接收
        if (PortName.Contains("LOOPBACK"))
        {
            SimulateDataReceived(data);
        }
    }

    /// <summary>
    /// 写入文本
    /// </summary>
    /// <param name="text">文本内容</param>
    public void Write(string text)
    {
        var data = Encoding.UTF8.GetBytes(text);
        Write(data, 0, data.Length);
    }

    /// <summary>
    /// 读取数据
    /// </summary>
    /// <param name="buffer">缓冲区</param>
    /// <param name="offset">偏移量</param>
    /// <param name="count">要读取的字节数</param>
    /// <returns>实际读取的字节数</returns>
    public int Read(byte[] buffer, int offset, int count)
    {
        ThrowIfDisposed();
        ThrowIfNotOpen();

        if (buffer == null)
            throw new ArgumentNullException(nameof(buffer));
        if (offset < 0 || offset >= buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(offset));
        if (count < 0 || offset + count > buffer.Length)
            throw new ArgumentOutOfRangeException(nameof(count));

        lock (_lockObject)
        {
            if (_receivedDataQueue.Count == 0)
                return 0;

            var totalRead = 0;
            while (_receivedDataQueue.Count > 0 && totalRead < count)
            {
                var data = _receivedDataQueue.Dequeue();
                var bytesToCopy = Math.Min(data.Length, count - totalRead);
                Array.Copy(data, 0, buffer, offset + totalRead, bytesToCopy);
                totalRead += bytesToCopy;

                // 如果数据没有完全读取，将剩余部分重新入队
                if (bytesToCopy < data.Length)
                {
                    var remaining = new byte[data.Length - bytesToCopy];
                    Array.Copy(data, bytesToCopy, remaining, 0, remaining.Length);
                    var tempQueue = new Queue<byte[]>();
                    tempQueue.Enqueue(remaining);
                    while (_receivedDataQueue.Count > 0)
                        tempQueue.Enqueue(_receivedDataQueue.Dequeue());
                    _receivedDataQueue.Clear();
                    while (tempQueue.Count > 0)
                        _receivedDataQueue.Enqueue(tempQueue.Dequeue());
                    break;
                }
            }
            return totalRead;
        }
    }

    /// <summary>
    /// 读取现有数据
    /// </summary>
    /// <returns>读取的字符串</returns>
    public string ReadExisting()
    {
        var buffer = new byte[BytesToRead];
        if (buffer.Length == 0)
            return string.Empty;

        var bytesRead = Read(buffer, 0, buffer.Length);
        return Encoding.UTF8.GetString(buffer, 0, bytesRead);
    }

    /// <summary>
    /// 模拟接收数据
    /// </summary>
    /// <param name="data">接收的数据</param>
    public void SimulateDataReceived(byte[] data)
    {
        if (!IsOpen || data == null || data.Length == 0)
            return;

        lock (_lockObject)
        {
            _receivedDataQueue.Enqueue(data);
        }

        // 触发数据接收事件
        var eventArgs = CreateSerialDataReceivedEventArgs(SerialData.Chars);
        DataReceived?.Invoke(this, eventArgs);
    }

    /// <summary>
    /// 模拟接收文本数据
    /// </summary>
    /// <param name="text">接收的文本</param>
    public void SimulateDataReceived(string text)
    {
        var data = Encoding.UTF8.GetBytes(text);
        SimulateDataReceived(data);
    }

    /// <summary>
    /// 模拟错误
    /// </summary>
    /// <param name="errorType">错误类型</param>
    public void SimulateError(SerialError errorType)
    {
        if (!IsOpen)
            return;

        var errorEventArgs = CreateSerialErrorReceivedEventArgs(errorType);
        ErrorReceived?.Invoke(this, errorEventArgs);
    }

    /// <summary>
    /// 获取已发送的数据
    /// </summary>
    /// <returns>发送的数据列表</returns>
    public List<byte[]> GetSentData()
    {
        lock (_lockObject)
        {
            return new List<byte[]>(_sentDataQueue);
        }
    }

    /// <summary>
    /// 清空发送数据队列
    /// </summary>
    public void ClearSentData()
    {
        lock (_lockObject)
        {
            _sentDataQueue.Clear();
        }
    }

    /// <summary>
    /// 检查是否已释放
    /// </summary>
    private void ThrowIfDisposed()
    {
        if (_disposed)
            throw new ObjectDisposedException(nameof(MockSerialPort));
    }

    /// <summary>
    /// 检查是否已打开
    /// </summary>
    private void ThrowIfNotOpen()
    {
        if (!IsOpen)
            throw new InvalidOperationException("串口未打开");
    }

    /// <summary>
    /// 创建SerialDataReceivedEventArgs实例
    /// </summary>
    /// <param name="eventType">事件类型</param>
    /// <returns>事件参数实例</returns>
    private static SerialDataReceivedEventArgs CreateSerialDataReceivedEventArgs(SerialData eventType)
    {
        // 使用反射创建内部构造函数的实例
        var constructor = typeof(SerialDataReceivedEventArgs).GetConstructor(
            BindingFlags.NonPublic | BindingFlags.Instance,
            null,
            new[] { typeof(SerialData) },
            null);

        if (constructor != null)
        {
            return (SerialDataReceivedEventArgs)constructor.Invoke(new object[] { eventType });
        }

        // 如果反射失败，尝试使用默认构造函数
        return (SerialDataReceivedEventArgs)Activator.CreateInstance(typeof(SerialDataReceivedEventArgs), true)!;
    }

    /// <summary>
    /// 创建SerialErrorReceivedEventArgs实例
    /// </summary>
    /// <param name="eventType">错误类型</param>
    /// <returns>事件参数实例</returns>
    private static SerialErrorReceivedEventArgs CreateSerialErrorReceivedEventArgs(SerialError eventType)
    {
        // 使用反射创建内部构造函数的实例
        var constructor = typeof(SerialErrorReceivedEventArgs).GetConstructor(
            BindingFlags.NonPublic | BindingFlags.Instance,
            null,
            new[] { typeof(SerialError) },
            null);

        if (constructor != null)
        {
            return (SerialErrorReceivedEventArgs)constructor.Invoke(new object[] { eventType });
        }

        // 如果反射失败，尝试使用默认构造函数
        return (SerialErrorReceivedEventArgs)Activator.CreateInstance(typeof(SerialErrorReceivedEventArgs), true)!;
    }

    /// <summary>
    /// 释放资源
    /// </summary>
    public void Dispose()
    {
        if (!_disposed)
        {
            Close();
            _disposed = true;
        }
    }
}
