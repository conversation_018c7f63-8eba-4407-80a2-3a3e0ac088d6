using System.Collections.Concurrent;
using Alicres.Protocol.Interfaces;
using Alicres.Protocol.Models.EventArgs;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Framing;

/// <summary>
/// 自定义帧头帧处理器，支持固定帧头 + 长度字段的协议格式
/// 适用于格式：帧头(1字节) + 源地址(1字节) + 目标地址(1字节) + 命令码(1字节) + 数据长度(1字节) + 数据字段(N字节) + CRC16(2字节)
/// </summary>
public class CustomHeaderFraming : AbstractMessageFraming
{
    /// <summary>
    /// 帧处理器名称
    /// </summary>
    public override string FramingName => "CustomHeaderFraming";

    /// <summary>
    /// 帧模式
    /// </summary>
    public override FramingMode Mode => FramingMode.Custom;

    /// <summary>
    /// 帧头字节
    /// </summary>
    public byte FrameHeader { get; set; } = 0x7E;

    /// <summary>
    /// 长度字段在帧中的位置（从0开始）
    /// </summary>
    public int LengthFieldOffset { get; set; } = 4;

    /// <summary>
    /// 长度字段表示的是整个帧的长度还是数据部分的长度
    /// </summary>
    public bool LengthIncludesHeader { get; set; } = true;

    /// <summary>
    /// 最小帧长度（防止无效数据）
    /// </summary>
    public int MinFrameLength { get; set; } = 8;

    /// <summary>
    /// 最大帧长度（防止恶意数据）
    /// </summary>
    public int MaxFrameLength { get; set; } = 256;

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="frameHeader">帧头字节，默认为 0x7E</param>
    /// <param name="logger">日志记录器</param>
    public CustomHeaderFraming(byte frameHeader = 0x7E, ILogger<CustomHeaderFraming>? logger = null) : base(logger)
    {
        FrameHeader = frameHeader;
    }

    /// <summary>
    /// 检查数据是否包含完整的帧
    /// </summary>
    /// <param name="data">待检查的数据</param>
    /// <returns>如果包含完整帧返回 true，否则返回 false</returns>
    public override bool HasCompleteFrame(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        if (data.Length < MinFrameLength)
            return false;

        // 查找帧头
        var headerIndex = FindFrameHeader(data);
        if (headerIndex == -1)
            return false;

        // 检查是否有足够的数据读取长度字段
        if (data.Length < headerIndex + LengthFieldOffset + 1)
            return false;

        // 读取长度字段
        var frameLength = data[headerIndex + LengthFieldOffset];
        
        if (frameLength < MinFrameLength || frameLength > MaxFrameLength)
        {
            Logger?.LogWarning("无效的帧长度: {Length}", frameLength);
            return false;
        }

        // 检查是否有完整的帧数据
        var expectedLength = LengthIncludesHeader ? frameLength : frameLength + LengthFieldOffset + 1;
        return data.Length >= headerIndex + expectedLength;
    }

    /// <summary>
    /// 获取帧的预期长度
    /// </summary>
    /// <param name="data">帧数据的开始部分</param>
    /// <returns>帧的预期总长度，如果无法确定返回 -1</returns>
    public override int GetExpectedFrameLength(byte[] data)
    {
        ArgumentNullException.ThrowIfNull(data);

        var headerIndex = FindFrameHeader(data);
        if (headerIndex == -1 || data.Length < headerIndex + LengthFieldOffset + 1)
            return -1;

        var frameLength = data[headerIndex + LengthFieldOffset];
        
        if (frameLength < MinFrameLength || frameLength > MaxFrameLength)
            return -1;

        return LengthIncludesHeader ? frameLength : frameLength + LengthFieldOffset + 1;
    }

    /// <summary>
    /// 查找帧头在数据中的位置
    /// </summary>
    /// <param name="data">数据</param>
    /// <returns>帧头位置，如果未找到返回 -1</returns>
    private int FindFrameHeader(byte[] data)
    {
        for (int i = 0; i < data.Length; i++)
        {
            if (data[i] == FrameHeader)
                return i;
        }
        return -1;
    }

    /// <summary>
    /// 为消息添加帧信息的具体实现
    /// </summary>
    /// <param name="message">原始消息</param>
    /// <returns>添加帧信息后的数据</returns>
    protected override byte[] AddFrameToMessage(byte[] message)
    {
        ArgumentNullException.ThrowIfNull(message);

        // 对于发送，假设消息已经包含了除帧头外的所有字段
        // 格式：源地址 + 目标地址 + 命令码 + 数据长度 + 数据字段 + CRC16
        var framedMessage = new byte[message.Length + 1];
        framedMessage[0] = FrameHeader;
        Array.Copy(message, 0, framedMessage, 1, message.Length);

        Logger?.LogTrace("为消息添加帧头 0x{Header:X2}，帧长度: {Length}", FrameHeader, framedMessage.Length);

        return framedMessage;
    }

    /// <summary>
    /// 从帧数据中移除帧信息的具体实现
    /// </summary>
    /// <param name="framedData">包含帧信息的数据</param>
    /// <returns>移除帧信息后的原始消息</returns>
    protected override byte[] RemoveFrameFromMessage(byte[] framedData)
    {
        ArgumentNullException.ThrowIfNull(framedData);

        var headerIndex = FindFrameHeader(framedData);
        if (headerIndex == -1)
        {
            Logger?.LogWarning("未找到帧头，返回原始数据");
            return framedData;
        }

        // 移除帧头，返回剩余数据
        var messageLength = framedData.Length - headerIndex - 1;
        var message = new byte[messageLength];
        Array.Copy(framedData, headerIndex + 1, message, 0, messageLength);

        Logger?.LogTrace("从帧数据中移除帧头，消息长度: {Length}", message.Length);

        return message;
    }


}
