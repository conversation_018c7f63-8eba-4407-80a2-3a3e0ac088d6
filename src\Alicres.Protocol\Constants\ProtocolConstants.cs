namespace Alicres.Protocol.Constants;

/// <summary>
/// 协议相关常量定义
/// </summary>
public static class ProtocolConstants
{
    /// <summary>
    /// Modbus 协议常量
    /// </summary>
    public static class Modbus
    {
        /// <summary>
        /// 最小消息长度（字节）
        /// </summary>
        public const int MinMessageLength = 4;

        /// <summary>
        /// 最大消息长度（字节）
        /// </summary>
        public const int MaxMessageLength = 256;

        /// <summary>
        /// CRC 长度（字节）
        /// </summary>
        public const int CrcLength = 2;

        /// <summary>
        /// 最大从站地址
        /// </summary>
        public const byte MaxSlaveAddress = 247;

        /// <summary>
        /// 广播地址
        /// </summary>
        public const byte BroadcastAddress = 0;

        /// <summary>
        /// 功能码定义
        /// </summary>
        public static class FunctionCodes
        {
            /// <summary>
            /// 读取线圈状态
            /// </summary>
            public const byte ReadCoils = 0x01;

            /// <summary>
            /// 读取离散输入状态
            /// </summary>
            public const byte ReadDiscreteInputs = 0x02;

            /// <summary>
            /// 读取保持寄存器
            /// </summary>
            public const byte ReadHoldingRegisters = 0x03;

            /// <summary>
            /// 读取输入寄存器
            /// </summary>
            public const byte ReadInputRegisters = 0x04;

            /// <summary>
            /// 写单个线圈
            /// </summary>
            public const byte WriteSingleCoil = 0x05;

            /// <summary>
            /// 写单个寄存器
            /// </summary>
            public const byte WriteSingleRegister = 0x06;

            /// <summary>
            /// 写多个线圈
            /// </summary>
            public const byte WriteMultipleCoils = 0x0F;

            /// <summary>
            /// 写多个寄存器
            /// </summary>
            public const byte WriteMultipleRegisters = 0x10;

            /// <summary>
            /// 异常响应标志位
            /// </summary>
            public const byte ExceptionFlag = 0x80;
        }

        /// <summary>
        /// 异常码定义
        /// </summary>
        public static class ExceptionCodes
        {
            /// <summary>
            /// 非法功能码
            /// </summary>
            public const byte IllegalFunction = 0x01;

            /// <summary>
            /// 非法数据地址
            /// </summary>
            public const byte IllegalDataAddress = 0x02;

            /// <summary>
            /// 非法数据值
            /// </summary>
            public const byte IllegalDataValue = 0x03;

            /// <summary>
            /// 从站设备故障
            /// </summary>
            public const byte SlaveDeviceFailure = 0x04;

            /// <summary>
            /// 确认
            /// </summary>
            public const byte Acknowledge = 0x05;

            /// <summary>
            /// 从站设备忙
            /// </summary>
            public const byte SlaveDeviceBusy = 0x06;

            /// <summary>
            /// 存储奇偶性差错
            /// </summary>
            public const byte MemoryParityError = 0x08;

            /// <summary>
            /// 不可用网关路径
            /// </summary>
            public const byte GatewayPathUnavailable = 0x0A;

            /// <summary>
            /// 网关目标设备响应失败
            /// </summary>
            public const byte GatewayTargetDeviceFailedToRespond = 0x0B;
        }

        /// <summary>
        /// 数据限制
        /// </summary>
        public static class Limits
        {
            /// <summary>
            /// 最大读取线圈数量
            /// </summary>
            public const ushort MaxReadCoils = 2000;

            /// <summary>
            /// 最大读取离散输入数量
            /// </summary>
            public const ushort MaxReadDiscreteInputs = 2000;

            /// <summary>
            /// 最大读取寄存器数量
            /// </summary>
            public const ushort MaxReadRegisters = 125;

            /// <summary>
            /// 最大写入线圈数量
            /// </summary>
            public const ushort MaxWriteCoils = 1968;

            /// <summary>
            /// 最大写入寄存器数量
            /// </summary>
            public const ushort MaxWriteRegisters = 123;
        }
    }

    /// <summary>
    /// 传输层常量
    /// </summary>
    public static class Transport
    {
        /// <summary>
        /// 串口传输类型
        /// </summary>
        public const string SerialPort = "SerialPort";

        /// <summary>
        /// TCP 传输类型
        /// </summary>
        public const string Tcp = "TCP";

        /// <summary>
        /// UDP 传输类型
        /// </summary>
        public const string Udp = "UDP";

        /// <summary>
        /// 默认接收缓冲区大小
        /// </summary>
        public const int DefaultReceiveBufferSize = 4096;

        /// <summary>
        /// 默认发送超时时间（毫秒）
        /// </summary>
        public const int DefaultSendTimeoutMs = 5000;

        /// <summary>
        /// 默认接收超时时间（毫秒）
        /// </summary>
        public const int DefaultReceiveTimeoutMs = 5000;
    }

    /// <summary>
    /// 校验相关常量
    /// </summary>
    public static class Validation
    {
        /// <summary>
        /// CRC16 多项式（Modbus）
        /// </summary>
        public const ushort Crc16Polynomial = 0xA001;

        /// <summary>
        /// CRC16 初始值
        /// </summary>
        public const ushort Crc16InitialValue = 0xFFFF;

        /// <summary>
        /// 校验和初始值
        /// </summary>
        public const byte ChecksumInitialValue = 0x00;
    }

    /// <summary>
    /// 协议解析相关常量
    /// </summary>
    public static class Parsing
    {
        /// <summary>
        /// 默认解析超时时间（毫秒）
        /// </summary>
        public const int DefaultParseTimeoutMs = 5000;

        /// <summary>
        /// 最大缓冲区大小
        /// </summary>
        public const int MaxBufferSize = 65536;

        /// <summary>
        /// 消息间隔时间（毫秒）
        /// </summary>
        public const int MessageIntervalMs = 10;
    }
}
