namespace Alicres.Protocol.Models.Performance;

/// <summary>
/// 性能历史数据
/// </summary>
public class PerformanceHistory
{
    /// <summary>
    /// 开始时间
    /// </summary>
    public DateTime StartTime { get; set; }

    /// <summary>
    /// 结束时间
    /// </summary>
    public DateTime EndTime { get; set; }

    /// <summary>
    /// 历史数据点列表
    /// </summary>
    public List<PerformanceDataPoint> DataPoints { get; set; } = new();

    /// <summary>
    /// 数据采样间隔
    /// </summary>
    public TimeSpan SampleInterval { get; set; }

    /// <summary>
    /// 总数据点数量
    /// </summary>
    public int TotalDataPoints => DataPoints.Count;

    /// <summary>
    /// 时间跨度
    /// </summary>
    public TimeSpan TimeSpan => EndTime - StartTime;

    /// <summary>
    /// 获取平均性能指标
    /// </summary>
    /// <returns>平均性能指标</returns>
    public PerformanceMetrics GetAverageMetrics()
    {
        if (DataPoints.Count == 0)
            return new PerformanceMetrics();

        var avgMetrics = new PerformanceMetrics
        {
            ThroughputBytesPerSecond = DataPoints.Average(dp => dp.ThroughputBytesPerSecond),
            SendRateBytesPerSecond = DataPoints.Average(dp => dp.SendRateBytesPerSecond),
            ReceiveRateBytesPerSecond = DataPoints.Average(dp => dp.ReceiveRateBytesPerSecond),
            MessageProcessingRate = DataPoints.Average(dp => dp.MessageProcessingRate),
            AverageLatencyMs = DataPoints.Average(dp => dp.AverageLatencyMs),
            MaxLatencyMs = DataPoints.Max(dp => dp.MaxLatencyMs),
            MinLatencyMs = DataPoints.Min(dp => dp.MinLatencyMs),
            ErrorRatePercentage = DataPoints.Average(dp => dp.ErrorRatePercentage),
            CpuUsagePercentage = DataPoints.Average(dp => dp.CpuUsagePercentage),
            MemoryUsageBytes = (long)DataPoints.Average(dp => dp.MemoryUsageBytes),
            ActiveConnections = (int)DataPoints.Average(dp => dp.ActiveConnections),
            QueueLength = (int)DataPoints.Average(dp => dp.QueueLength),
            StartTime = StartTime,
            LastUpdateTime = EndTime
        };

        // 计算总计数据
        var lastPoint = DataPoints.LastOrDefault();
        if (lastPoint != null)
        {
            avgMetrics.TotalBytesSent = lastPoint.TotalBytesSent;
            avgMetrics.TotalBytesReceived = lastPoint.TotalBytesReceived;
            avgMetrics.TotalMessages = lastPoint.TotalMessages;
            avgMetrics.TotalErrors = lastPoint.TotalErrors;
        }

        return avgMetrics;
    }

    /// <summary>
    /// 获取峰值性能指标
    /// </summary>
    /// <returns>峰值性能指标</returns>
    public PerformanceMetrics GetPeakMetrics()
    {
        if (DataPoints.Count == 0)
            return new PerformanceMetrics();

        return new PerformanceMetrics
        {
            ThroughputBytesPerSecond = DataPoints.Max(dp => dp.ThroughputBytesPerSecond),
            SendRateBytesPerSecond = DataPoints.Max(dp => dp.SendRateBytesPerSecond),
            ReceiveRateBytesPerSecond = DataPoints.Max(dp => dp.ReceiveRateBytesPerSecond),
            MessageProcessingRate = DataPoints.Max(dp => dp.MessageProcessingRate),
            AverageLatencyMs = DataPoints.Max(dp => dp.AverageLatencyMs),
            MaxLatencyMs = DataPoints.Max(dp => dp.MaxLatencyMs),
            MinLatencyMs = DataPoints.Min(dp => dp.MinLatencyMs),
            ErrorRatePercentage = DataPoints.Max(dp => dp.ErrorRatePercentage),
            CpuUsagePercentage = DataPoints.Max(dp => dp.CpuUsagePercentage),
            MemoryUsageBytes = DataPoints.Max(dp => dp.MemoryUsageBytes),
            ActiveConnections = DataPoints.Max(dp => dp.ActiveConnections),
            QueueLength = DataPoints.Max(dp => dp.QueueLength),
            StartTime = StartTime,
            LastUpdateTime = EndTime
        };
    }

    /// <summary>
    /// 获取指定时间范围内的数据点
    /// </summary>
    /// <param name="start">开始时间</param>
    /// <param name="end">结束时间</param>
    /// <returns>时间范围内的数据点</returns>
    public List<PerformanceDataPoint> GetDataPointsInRange(DateTime start, DateTime end)
    {
        return DataPoints.Where(dp => dp.Timestamp >= start && dp.Timestamp <= end).ToList();
    }

    /// <summary>
    /// 添加数据点
    /// </summary>
    /// <param name="dataPoint">数据点</param>
    public void AddDataPoint(PerformanceDataPoint dataPoint)
    {
        ArgumentNullException.ThrowIfNull(dataPoint);
        DataPoints.Add(dataPoint);
        
        // 更新时间范围
        if (DataPoints.Count == 1)
        {
            StartTime = dataPoint.Timestamp;
        }
        EndTime = dataPoint.Timestamp;
    }

    /// <summary>
    /// 清理过期数据点
    /// </summary>
    /// <param name="retentionPeriod">保留期间</param>
    public void CleanupExpiredData(TimeSpan retentionPeriod)
    {
        var cutoffTime = DateTime.Now - retentionPeriod;
        DataPoints.RemoveAll(dp => dp.Timestamp < cutoffTime);
        
        if (DataPoints.Count > 0)
        {
            StartTime = DataPoints.Min(dp => dp.Timestamp);
            EndTime = DataPoints.Max(dp => dp.Timestamp);
        }
    }
}

/// <summary>
/// 性能数据点
/// </summary>
public class PerformanceDataPoint
{
    /// <summary>
    /// 时间戳
    /// </summary>
    public DateTime Timestamp { get; set; }

    /// <summary>
    /// 数据吞吐量（字节/秒）
    /// </summary>
    public double ThroughputBytesPerSecond { get; set; }

    /// <summary>
    /// 发送速率（字节/秒）
    /// </summary>
    public double SendRateBytesPerSecond { get; set; }

    /// <summary>
    /// 接收速率（字节/秒）
    /// </summary>
    public double ReceiveRateBytesPerSecond { get; set; }

    /// <summary>
    /// 消息处理速率（消息/秒）
    /// </summary>
    public double MessageProcessingRate { get; set; }

    /// <summary>
    /// 平均延迟（毫秒）
    /// </summary>
    public double AverageLatencyMs { get; set; }

    /// <summary>
    /// 最大延迟（毫秒）
    /// </summary>
    public double MaxLatencyMs { get; set; }

    /// <summary>
    /// 最小延迟（毫秒）
    /// </summary>
    public double MinLatencyMs { get; set; }

    /// <summary>
    /// 错误率（百分比）
    /// </summary>
    public double ErrorRatePercentage { get; set; }

    /// <summary>
    /// 总发送字节数
    /// </summary>
    public long TotalBytesSent { get; set; }

    /// <summary>
    /// 总接收字节数
    /// </summary>
    public long TotalBytesReceived { get; set; }

    /// <summary>
    /// 总消息数
    /// </summary>
    public long TotalMessages { get; set; }

    /// <summary>
    /// 总错误数
    /// </summary>
    public long TotalErrors { get; set; }

    /// <summary>
    /// CPU 使用率（百分比）
    /// </summary>
    public double CpuUsagePercentage { get; set; }

    /// <summary>
    /// 内存使用量（字节）
    /// </summary>
    public long MemoryUsageBytes { get; set; }

    /// <summary>
    /// 活跃连接数
    /// </summary>
    public int ActiveConnections { get; set; }

    /// <summary>
    /// 队列长度
    /// </summary>
    public int QueueLength { get; set; }

    /// <summary>
    /// 从性能指标创建数据点
    /// </summary>
    /// <param name="metrics">性能指标</param>
    /// <returns>数据点</returns>
    public static PerformanceDataPoint FromMetrics(PerformanceMetrics metrics)
    {
        ArgumentNullException.ThrowIfNull(metrics);

        return new PerformanceDataPoint
        {
            Timestamp = metrics.LastUpdateTime,
            ThroughputBytesPerSecond = metrics.ThroughputBytesPerSecond,
            SendRateBytesPerSecond = metrics.SendRateBytesPerSecond,
            ReceiveRateBytesPerSecond = metrics.ReceiveRateBytesPerSecond,
            MessageProcessingRate = metrics.MessageProcessingRate,
            AverageLatencyMs = metrics.AverageLatencyMs,
            MaxLatencyMs = metrics.MaxLatencyMs,
            MinLatencyMs = metrics.MinLatencyMs,
            ErrorRatePercentage = metrics.ErrorRatePercentage,
            TotalBytesSent = metrics.TotalBytesSent,
            TotalBytesReceived = metrics.TotalBytesReceived,
            TotalMessages = metrics.TotalMessages,
            TotalErrors = metrics.TotalErrors,
            CpuUsagePercentage = metrics.CpuUsagePercentage,
            MemoryUsageBytes = metrics.MemoryUsageBytes,
            ActiveConnections = metrics.ActiveConnections,
            QueueLength = metrics.QueueLength
        };
    }
}
