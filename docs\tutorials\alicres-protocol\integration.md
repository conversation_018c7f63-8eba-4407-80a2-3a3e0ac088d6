# Alicres.Protocol 集成指南

本指南详细介绍如何将 Alicres.Protocol 与 Alicres.SerialPort 以及其他传输层进行深度集成，实现完整的工业通讯解决方案。

## 📋 目录

- [与 Alicres.SerialPort 集成](#与-alicresserialport-集成)
- [TCP/UDP 传输集成](#tcpudp-传输集成)
- [依赖注入集成](#依赖注入集成)
- [配置管理集成](#配置管理集成)
- [日志记录集成](#日志记录集成)
- [完整应用示例](#完整应用示例)

---

## 🔌 与 Alicres.SerialPort 集成

### 基础集成示例

```csharp
using Alicres.SerialPort.Models;
using Alicres.SerialPort.Services;
using Alicres.Protocol.Framing;
using Alicres.Protocol.Validators;
using Alicres.Protocol.Transport;

public class SerialPortProtocolIntegration
{
    private readonly SerialPortService _serialPort;
    private readonly DelimiterFraming _framing;
    private readonly Crc16Validator _validator;
    private readonly SerialPortTransportAdapter _adapter;

    public SerialPortProtocolIntegration()
    {
        // 配置串口
        var config = new SerialPortConfiguration
        {
            PortName = "COM1",
            BaudRate = 9600,
            DataBits = 8,
            StopBits = StopBits.One,
            Parity = Parity.None,
            EnableAutoReconnect = true,
            ReconnectInterval = 3000
        };

        _serialPort = new SerialPortService(config);

        // 配置协议处理
        _framing = new DelimiterFraming(0x0D, 0x0A); // CR+LF 分隔符
        _validator = new Crc16Validator();

        // 创建传输适配器
        _adapter = new SerialPortTransportAdapter(_serialPort);

        SetupEventHandlers();
    }

    private void SetupEventHandlers()
    {
        // 原始数据接收处理
        _adapter.DataReceived += OnRawDataReceived;

        // 连接状态处理
        _adapter.Connected += (sender, e) =>
        {
            Console.WriteLine("串口协议适配器已连接");
        };

        _adapter.Disconnected += (sender, e) =>
        {
            Console.WriteLine("串口协议适配器已断开");
        };
    }

    private void OnRawDataReceived(object sender, byte[] rawData)
    {
        try
        {
            // 1. 帧处理：从原始数据中提取完整帧
            var frames = _framing.ProcessIncomingData(rawData);

            foreach (var frame in frames)
            {
                Console.WriteLine($"提取到帧: {BitConverter.ToString(frame)}");

                // 2. 数据校验
                if (_validator.Validate(frame))
                {
                    var validData = _validator.RemoveChecksum(frame);
                    Console.WriteLine($"校验通过，有效数据: {BitConverter.ToString(validData)}");

                    // 3. 触发应用层处理
                    OnValidDataReceived(validData);
                }
                else
                {
                    Console.WriteLine("❌ 数据校验失败");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"数据处理错误: {ex.Message}");
        }
    }

    public async Task SendProtocolDataAsync(byte[] data)
    {
        try
        {
            // 1. 添加校验码
            var dataWithChecksum = _validator.AddChecksum(data);

            // 2. 添加帧格式
            var framedData = _framing.FrameMessage(dataWithChecksum);

            // 3. 通过串口发送
            await _adapter.SendAsync(framedData);

            Console.WriteLine($"发送协议数据: {BitConverter.ToString(framedData)}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"发送数据失败: {ex.Message}");
        }
    }

    public async Task StartAsync()
    {
        await _adapter.OpenAsync();
        Console.WriteLine("串口协议集成已启动");
    }

    public async Task StopAsync()
    {
        await _adapter.CloseAsync();
        Console.WriteLine("串口协议集成已停止");
    }

    public event EventHandler<byte[]> ValidDataReceived;

    protected virtual void OnValidDataReceived(byte[] data)
    {
        ValidDataReceived?.Invoke(this, data);
    }
}
```

### Modbus RTU over Serial 完整实现

```csharp
public class ModbusRtuSerialMaster
{
    private readonly SerialPortService _serialPort;
    private readonly ModbusRtuProtocol _modbus;
    private readonly Crc16Validator _validator;
    private readonly SemaphoreSlim _transactionLock = new(1, 1);
    private readonly Dictionary<byte, TaskCompletionSource<byte[]>> _pendingTransactions = new();

    public ModbusRtuSerialMaster(SerialPortConfiguration serialConfig)
    {
        _serialPort = new SerialPortService(serialConfig);
        _modbus = new ModbusRtuProtocol();
        _validator = new Crc16Validator();

        _serialPort.DataReceived += OnSerialDataReceived;
    }

    private void OnSerialDataReceived(object sender, SerialPortDataReceivedEventArgs e)
    {
        var data = e.Data.GetDataCopy();

        if (data.Length < 4) return; // 最小 Modbus RTU 帧长度

        // 验证 CRC
        if (_validator.Validate(data))
        {
            var slaveId = data[0];

            if (_pendingTransactions.TryGetValue(slaveId, out var tcs))
            {
                _pendingTransactions.Remove(slaveId);
                tcs.SetResult(data);
            }
        }
    }

    public async Task<ushort[]> ReadHoldingRegistersAsync(
        byte slaveId, ushort startAddress, ushort quantity,
        TimeSpan timeout = default)
    {
        if (timeout == default) timeout = TimeSpan.FromSeconds(5);

        await _transactionLock.WaitAsync();
        try
        {
            // 创建 Modbus RTU 请求
            var request = _modbus.CreateReadHoldingRegistersRequest(slaveId, startAddress, quantity);

            // 设置响应等待
            var tcs = new TaskCompletionSource<byte[]>();
            _pendingTransactions[slaveId] = tcs;

            // 发送请求
            await _serialPort.SendAsync(new SerialPortData(request));

            // 等待响应
            using var cts = new CancellationTokenSource(timeout);
            cts.Token.Register(() =>
            {
                _pendingTransactions.Remove(slaveId);
                tcs.TrySetCanceled();
            });

            var response = await tcs.Task;

            // 解析响应
            var parsedResponse = _modbus.ParseReadHoldingRegistersResponse(response);
            return parsedResponse.RegisterValues;
        }
        catch (OperationCanceledException)
        {
            throw new TimeoutException($"读取保持寄存器超时 (从站 {slaveId})");
        }
        finally
        {
            _transactionLock.Release();
        }
    }

    public async Task WriteMultipleRegistersAsync(
        byte slaveId, ushort startAddress, ushort[] values,
        TimeSpan timeout = default)
    {
        if (timeout == default) timeout = TimeSpan.FromSeconds(5);

        await _transactionLock.WaitAsync();
        try
        {
            var request = _modbus.CreateWriteMultipleRegistersRequest(slaveId, startAddress, values);

            var tcs = new TaskCompletionSource<byte[]>();
            _pendingTransactions[slaveId] = tcs;

            await _serialPort.SendAsync(new SerialPortData(request));

            using var cts = new CancellationTokenSource(timeout);
            cts.Token.Register(() =>
            {
                _pendingTransactions.Remove(slaveId);
                tcs.TrySetCanceled();
            });

            var response = await tcs.Task;

            // 验证写入响应
            var parsedResponse = _modbus.ParseWriteMultipleRegistersResponse(response);
            if (parsedResponse.StartAddress != startAddress ||
                parsedResponse.Quantity != values.Length)
            {
                throw new InvalidOperationException("写入寄存器响应不匹配");
            }
        }
        finally
        {
            _transactionLock.Release();
        }
    }

    public async Task OpenAsync()
    {
        await _serialPort.OpenAsync();
    }

    public async Task CloseAsync()
    {
        await _serialPort.CloseAsync();
    }
}
```

---

## 🌐 TCP/UDP 传输集成

### TCP Modbus 集成

```csharp
public class ModbusTcpMaster
{
    private readonly TcpClient _tcpClient;
    private readonly NetworkStream _stream;
    private readonly ModbusTcpProtocol _modbus;
    private readonly SemaphoreSlim _transactionLock = new(1, 1);
    private ushort _transactionId = 0;

    public ModbusTcpMaster(string host, int port)
    {
        _tcpClient = new TcpClient();
        _modbus = new ModbusTcpProtocol();
    }

    public async Task ConnectAsync()
    {
        await _tcpClient.ConnectAsync(Host, Port);
        _stream = _tcpClient.GetStream();

        // 启动接收循环
        _ = Task.Run(ReceiveLoop);
    }

    public async Task<ushort[]> ReadHoldingRegistersAsync(
        byte unitId, ushort startAddress, ushort quantity)
    {
        await _transactionLock.WaitAsync();
        try
        {
            var transactionId = ++_transactionId;

            // 创建 Modbus TCP 请求 (包含 MBAP 头)
            var request = _modbus.CreateReadHoldingRegistersRequest(
                transactionId, unitId, startAddress, quantity);

            // 发送请求
            await _stream.WriteAsync(request, 0, request.Length);

            // 等待响应 (在 ReceiveLoop 中处理)
            var response = await WaitForResponse(transactionId);

            // 解析响应
            var parsedResponse = _modbus.ParseReadHoldingRegistersResponse(response);
            return parsedResponse.RegisterValues;
        }
        finally
        {
            _transactionLock.Release();
        }
    }

    private readonly Dictionary<ushort, TaskCompletionSource<byte[]>> _pendingTransactions = new();

    private async Task ReceiveLoop()
    {
        var buffer = new byte[1024];

        while (_tcpClient.Connected)
        {
            try
            {
                var bytesRead = await _stream.ReadAsync(buffer, 0, buffer.Length);
                if (bytesRead > 0)
                {
                    var data = new byte[bytesRead];
                    Array.Copy(buffer, data, bytesRead);

                    ProcessReceivedData(data);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"TCP 接收错误: {ex.Message}");
                break;
            }
        }
    }

    private void ProcessReceivedData(byte[] data)
    {
        if (data.Length < 8) return; // 最小 Modbus TCP 帧长度

        // 解析 MBAP 头
        var transactionId = (ushort)((data[0] << 8) | data[1]);

        if (_pendingTransactions.TryGetValue(transactionId, out var tcs))
        {
            _pendingTransactions.Remove(transactionId);
            tcs.SetResult(data);
        }
    }

    private async Task<byte[]> WaitForResponse(ushort transactionId)
    {
        var tcs = new TaskCompletionSource<byte[]>();
        _pendingTransactions[transactionId] = tcs;

        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(10));
        cts.Token.Register(() =>
        {
            _pendingTransactions.Remove(transactionId);
            tcs.TrySetCanceled();
        });

        return await tcs.Task;
    }
}
```

---

## 💉 依赖注入集成

### 服务注册配置

```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Configuration;

public static class ServiceCollectionExtensions
{
    public static IServiceCollection AddAlicresProtocol(
        this IServiceCollection services,
        IConfiguration configuration)
    {
        // 注册配置
        services.Configure<SerialPortConfiguration>(
            configuration.GetSection("SerialPort"));
        services.Configure<ProtocolConfiguration>(
            configuration.GetSection("Protocol"));

        // 注册核心服务
        services.AddSingleton<ISerialPortService, SerialPortService>();
        services.AddSingleton<IMessageFraming, DelimiterFraming>();
        services.AddSingleton<IDataValidator, Crc16Validator>();

        // 注册传输适配器
        services.AddSingleton<ITransportAdapter, SerialPortTransportAdapter>();

        // 注册协议处理器
        services.AddSingleton<ModbusRtuProtocol>();
        services.AddSingleton<ModbusAsciiProtocol>();

        // 注册高级服务
        services.AddSingleton<IProtocolManager, ProtocolManager>();
        services.AddSingleton<IDeviceCommunicationService, DeviceCommunicationService>();

        return services;
    }
}

// 配置模型
public class ProtocolConfiguration
{
    public string FramingType { get; set; } = "Delimiter";
    public string ValidatorType { get; set; } = "CRC16";
    public byte[] FrameDelimiters { get; set; } = { 0x0D, 0x0A };
    public int MaxFrameLength { get; set; } = 1024;
    public bool EnablePerformanceMonitoring { get; set; } = true;
}
```

### 依赖注入使用示例

```csharp
public class DeviceCommunicationService : IDeviceCommunicationService
{
    private readonly ITransportAdapter _transport;
    private readonly IMessageFraming _framing;
    private readonly IDataValidator _validator;
    private readonly ModbusRtuProtocol _modbus;
    private readonly ILogger<DeviceCommunicationService> _logger;

    public DeviceCommunicationService(
        ITransportAdapter transport,
        IMessageFraming framing,
        IDataValidator validator,
        ModbusRtuProtocol modbus,
        ILogger<DeviceCommunicationService> logger)
    {
        _transport = transport;
        _framing = framing;
        _validator = validator;
        _modbus = modbus;
        _logger = logger;

        _transport.DataReceived += OnDataReceived;
    }

    public async Task<ushort[]> ReadDeviceRegistersAsync(
        byte deviceId, ushort address, ushort count)
    {
        _logger.LogInformation("读取设备 {DeviceId} 寄存器 {Address}-{EndAddress}",
            deviceId, address, address + count - 1);

        try
        {
            var request = _modbus.CreateReadHoldingRegistersRequest(deviceId, address, count);
            var framedRequest = _framing.FrameMessage(_validator.AddChecksum(request));

            await _transport.SendAsync(framedRequest);

            // 等待响应逻辑...
            var response = await WaitForResponse(deviceId);

            _logger.LogInformation("成功读取设备 {DeviceId} 寄存器", deviceId);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "读取设备 {DeviceId} 寄存器失败", deviceId);
            throw;
        }
    }

    private void OnDataReceived(object sender, byte[] data)
    {
        var frames = _framing.ProcessIncomingData(data);
        foreach (var frame in frames)
        {
            if (_validator.Validate(frame))
            {
                var validData = _validator.RemoveChecksum(frame);
                ProcessValidFrame(validData);
            }
        }
    }

    // 其他方法实现...
}

// 主机服务
public class CommunicationHostedService : BackgroundService
{
    private readonly IDeviceCommunicationService _communicationService;
    private readonly ILogger<CommunicationHostedService> _logger;

    public CommunicationHostedService(
        IDeviceCommunicationService communicationService,
        ILogger<CommunicationHostedService> logger)
    {
        _communicationService = communicationService;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("通讯服务启动");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // 定期读取设备数据
                var data = await _communicationService.ReadDeviceRegistersAsync(1, 0, 10);
                _logger.LogDebug("读取到设备数据: {Data}", string.Join(", ", data));

                await Task.Delay(5000, stoppingToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "通讯服务执行错误");
                await Task.Delay(1000, stoppingToken);
            }
        }
    }
}
```

---

## ⚙️ 配置管理集成

### appsettings.json 配置

```json
{
  "SerialPort": {
    "PortName": "COM1",
    "BaudRate": 9600,
    "DataBits": 8,
    "StopBits": "One",
    "Parity": "None",
    "ReadTimeout": 5000,
    "WriteTimeout": 3000,
    "EnableAutoReconnect": true,
    "ReconnectInterval": 3000,
    "MaxReconnectAttempts": 5
  },
  "Protocol": {
    "FramingType": "Delimiter",
    "ValidatorType": "CRC16",
    "FrameDelimiters": [13, 10],
    "MaxFrameLength": 1024,
    "EnablePerformanceMonitoring": true
  },
  "Devices": [
    {
      "Id": 1,
      "Name": "Temperature Sensor",
      "Type": "ModbusRTU",
      "Address": 1,
      "Registers": [
        { "Name": "Temperature", "Address": 0, "Type": "Float" },
        { "Name": "Humidity", "Address": 2, "Type": "Float" }
      ]
    }
  ],
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Alicres": "Debug"
    }
  }
}
```

### 配置驱动的服务

```csharp
public class ConfigurableProtocolService
{
    private readonly IOptionsMonitor<ProtocolConfiguration> _protocolOptions;
    private readonly IOptionsMonitor<SerialPortConfiguration> _serialOptions;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<ConfigurableProtocolService> _logger;

    public ConfigurableProtocolService(
        IOptionsMonitor<ProtocolConfiguration> protocolOptions,
        IOptionsMonitor<SerialPortConfiguration> serialOptions,
        IServiceProvider serviceProvider,
        ILogger<ConfigurableProtocolService> logger)
    {
        _protocolOptions = protocolOptions;
        _serialOptions = serialOptions;
        _serviceProvider = serviceProvider;
        _logger = logger;

        // 监听配置变化
        _protocolOptions.OnChange(OnProtocolConfigurationChanged);
        _serialOptions.OnChange(OnSerialConfigurationChanged);
    }

    private void OnProtocolConfigurationChanged(ProtocolConfiguration config)
    {
        _logger.LogInformation("协议配置已更新");
        // 重新初始化协议组件
        ReconfigureProtocol(config);
    }

    private void OnSerialConfigurationChanged(SerialPortConfiguration config)
    {
        _logger.LogInformation("串口配置已更新");
        // 重新配置串口
        ReconfigureSerialPort(config);
    }

    private void ReconfigureProtocol(ProtocolConfiguration config)
    {
        // 根据配置创建相应的组件
        IMessageFraming framing = config.FramingType switch
        {
            "Delimiter" => new DelimiterFraming(config.FrameDelimiters),
            "FixedLength" => new FixedLengthFraming(config.MaxFrameLength),
            _ => throw new NotSupportedException($"不支持的帧类型: {config.FramingType}")
        };

        IDataValidator validator = config.ValidatorType switch
        {
            "CRC16" => new Crc16Validator(),
            "Checksum" => new ChecksumValidator(),
            _ => throw new NotSupportedException($"不支持的校验类型: {config.ValidatorType}")
        };

        // 更新服务实例...
    }
}
```

---

## 📊 完整应用示例

### 工业设备监控应用

```csharp
public class IndustrialMonitoringApplication
{
    public static async Task Main(string[] args)
    {
        // 创建主机
        var host = Host.CreateDefaultBuilder(args)
            .ConfigureServices((context, services) =>
            {
                // 添加 Alicres 服务
                services.AddAlicresProtocol(context.Configuration);

                // 添加应用服务
                services.AddHostedService<CommunicationHostedService>();
                services.AddHostedService<DataProcessingService>();
                services.AddHostedService<AlarmMonitoringService>();
            })
            .Build();

        // 启动应用
        await host.RunAsync();
    }
}

public class DataProcessingService : BackgroundService
{
    private readonly IDeviceCommunicationService _communication;
    private readonly ILogger<DataProcessingService> _logger;

    public DataProcessingService(
        IDeviceCommunicationService communication,
        ILogger<DataProcessingService> logger)
    {
        _communication = communication;
        _logger = logger;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                // 读取多个设备数据
                var tasks = new[]
                {
                    ReadDeviceData(1, "温度传感器"),
                    ReadDeviceData(2, "压力传感器"),
                    ReadDeviceData(3, "流量计")
                };

                await Task.WhenAll(tasks);

                await Task.Delay(10000, stoppingToken); // 10秒间隔
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据处理服务错误");
                await Task.Delay(5000, stoppingToken);
            }
        }
    }

    private async Task ReadDeviceData(byte deviceId, string deviceName)
    {
        try
        {
            var data = await _communication.ReadDeviceRegistersAsync(deviceId, 0, 4);

            // 处理数据
            var temperature = ConvertToFloat(data[0], data[1]);
            var pressure = ConvertToFloat(data[2], data[3]);

            _logger.LogInformation("{DeviceName} - 温度: {Temperature}°C, 压力: {Pressure}Pa",
                deviceName, temperature, pressure);

            // 存储到数据库或发送到云端
            await StoreData(deviceId, temperature, pressure);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "读取设备 {DeviceName} 数据失败", deviceName);
        }
    }

    private float ConvertToFloat(ushort high, ushort low)
    {
        var bytes = BitConverter.GetBytes((uint)((high << 16) | low));
        return BitConverter.ToSingle(bytes, 0);
    }

    private async Task StoreData(byte deviceId, float temperature, float pressure)
    {
        // 实现数据存储逻辑
        await Task.Delay(10); // 模拟存储操作
    }
}
```

这个集成指南展示了如何将 Alicres.Protocol 与各种传输层和框架进行深度集成，创建完整的工业通讯解决方案。通过依赖注入、配置管理和日志记录的集成，可以构建出高度可配置、可维护的工业应用程序。