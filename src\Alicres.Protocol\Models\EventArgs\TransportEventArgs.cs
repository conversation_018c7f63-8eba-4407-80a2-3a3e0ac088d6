namespace Alicres.Protocol.Models.EventArgs;

/// <summary>
/// 传输数据事件参数
/// </summary>
public class TransportDataEventArgs : System.EventArgs
{
    /// <summary>
    /// 接收到的数据
    /// </summary>
    public byte[] Data { get; }

    /// <summary>
    /// 数据长度
    /// </summary>
    public int Length => Data.Length;

    /// <summary>
    /// 传输类型
    /// </summary>
    public string TransportType { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="data">接收到的数据</param>
    /// <param name="transportType">传输类型</param>
    public TransportDataEventArgs(byte[] data, string transportType)
    {
        Data = data ?? throw new ArgumentNullException(nameof(data));
        TransportType = transportType ?? throw new ArgumentNullException(nameof(transportType));
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 传输状态事件参数
/// </summary>
public class TransportStatusEventArgs : System.EventArgs
{
    /// <summary>
    /// 之前的连接状态
    /// </summary>
    public bool PreviousStatus { get; }

    /// <summary>
    /// 当前的连接状态
    /// </summary>
    public bool CurrentStatus { get; }

    /// <summary>
    /// 传输类型
    /// </summary>
    public string TransportType { get; }

    /// <summary>
    /// 状态变化描述
    /// </summary>
    public string StatusDescription { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="previousStatus">之前的连接状态</param>
    /// <param name="currentStatus">当前的连接状态</param>
    /// <param name="transportType">传输类型</param>
    /// <param name="statusDescription">状态变化描述</param>
    public TransportStatusEventArgs(bool previousStatus, bool currentStatus, string transportType, string? statusDescription = null)
    {
        PreviousStatus = previousStatus;
        CurrentStatus = currentStatus;
        TransportType = transportType ?? throw new ArgumentNullException(nameof(transportType));
        StatusDescription = statusDescription ?? (currentStatus ? "已连接" : "已断开");
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 传输错误事件参数
/// </summary>
public class TransportErrorEventArgs : System.EventArgs
{
    /// <summary>
    /// 错误异常
    /// </summary>
    public Exception Exception { get; }

    /// <summary>
    /// 传输类型
    /// </summary>
    public string TransportType { get; }

    /// <summary>
    /// 错误描述
    /// </summary>
    public string ErrorMessage { get; }

    /// <summary>
    /// 是否为致命错误
    /// </summary>
    public bool IsFatal { get; }

    /// <summary>
    /// 事件时间戳
    /// </summary>
    public DateTime Timestamp { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="exception">错误异常</param>
    /// <param name="transportType">传输类型</param>
    /// <param name="isFatal">是否为致命错误</param>
    public TransportErrorEventArgs(Exception exception, string transportType, bool isFatal = false)
    {
        Exception = exception ?? throw new ArgumentNullException(nameof(exception));
        TransportType = transportType ?? throw new ArgumentNullException(nameof(transportType));
        ErrorMessage = exception.Message;
        IsFatal = isFatal;
        Timestamp = DateTime.Now;
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="errorMessage">错误描述</param>
    /// <param name="transportType">传输类型</param>
    /// <param name="isFatal">是否为致命错误</param>
    public TransportErrorEventArgs(string errorMessage, string transportType, bool isFatal = false)
    {
        Exception = new InvalidOperationException(errorMessage);
        TransportType = transportType ?? throw new ArgumentNullException(nameof(transportType));
        ErrorMessage = errorMessage ?? throw new ArgumentNullException(nameof(errorMessage));
        IsFatal = isFatal;
        Timestamp = DateTime.Now;
    }
}
