using Alicres.Protocol.Validators;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Alicres.Protocol.Tests.Validators;

/// <summary>
/// 自定义CRC16校验器测试
/// </summary>
public class CustomCrc16ValidatorTests : IDisposable
{
    private readonly Mock<ILogger<CustomCrc16Validator>> _mockLogger;
    private readonly CustomCrc16Validator _validator;

    // 测试数据：7E F1 05 55 0A 00 00 10 F5 82
    // CRC16校验范围：F1 05 55 0A 00 00 10 (排除帧头7E和CRC16 F5 82)
    private readonly byte[] _validTestData = { 0x7E, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10, 0xF5, 0x82 };
    private readonly byte[] _dataForCrc = { 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10 };

    public CustomCrc16ValidatorTests()
    {
        _mockLogger = new Mock<ILogger<CustomCrc16Validator>>();
        _validator = new CustomCrc16Validator(_mockLogger.Object);
    }

    [Fact]
    public void Constructor_ShouldInitializeCorrectly()
    {
        // Assert
        _validator.ValidatorName.Should().Be("CustomCrc16Validator");
    }

    [Fact]
    public void Validate_WithValidData_ShouldReturnTrue()
    {
        // Act
        var result = _validator.Validate(_validTestData);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task ValidateAsync_WithValidData_ShouldReturnTrue()
    {
        // Act
        var result = await _validator.ValidateAsync(_validTestData);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void Validate_WithInsufficientData_ShouldReturnFalse()
    {
        // Arrange
        var insufficientData = new byte[] { 0x7E, 0xF1 };

        // Act
        var result = _validator.Validate(insufficientData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void Validate_WithCorruptedCrc_ShouldReturnFalse()
    {
        // Arrange
        var corruptedData = (byte[])_validTestData.Clone();
        corruptedData[^1] = 0x00; // 破坏最后一个CRC字节

        // Act
        var result = _validator.Validate(corruptedData);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void CalculateCrc16_WithKnownData_ShouldReturnExpectedValue()
    {
        // Act
        var result = _validator.CalculateCrc16(_dataForCrc);

        // Assert
        // 根据测试数据，期望的CRC16值应该是0x82F5（小端字节序）
        result.Should().Be(0x82F5);
    }

    [Fact]
    public void CalculateChecksum_WithValidData_ShouldReturnCorrectChecksum()
    {
        // Act
        var result = _validator.CalculateChecksum(_validTestData);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(2);
        // 小端字节序：F5 82
        result[0].Should().Be(0xF5);
        result[1].Should().Be(0x82);
    }

    [Fact]
    public void AddChecksum_WithData_ShouldAppendCorrectChecksum()
    {
        // Arrange
        var dataWithoutCrc = new byte[] { 0x7E, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10 };

        // Act
        var result = _validator.AddChecksum(dataWithoutCrc);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(dataWithoutCrc.Length + 2);
        
        // 验证添加的校验码是否正确
        var isValid = _validator.Validate(result);
        isValid.Should().BeTrue();
    }

    [Fact]
    public void RemoveChecksum_WithValidData_ShouldReturnDataWithoutChecksum()
    {
        // Act
        var result = _validator.RemoveChecksum(_validTestData);

        // Assert
        result.Should().NotBeNull();
        result.Length.Should().Be(_validTestData.Length - 2);
        result.Should().Equal(new byte[] { 0x7E, 0xF1, 0x05, 0x55, 0x0A, 0x00, 0x00, 0x10 });
    }

    [Fact]
    public void RemoveChecksum_WithInsufficientData_ShouldThrowException()
    {
        // Arrange
        var insufficientData = new byte[] { 0x7E };

        // Act & Assert
        Assert.Throws<ArgumentException>(() => _validator.RemoveChecksum(insufficientData));
    }

    [Fact]
    public void ValidateHexString_WithValidHexString_ShouldReturnTrue()
    {
        // Arrange
        var hexString = "7E F1 05 55 0A 00 00 10 F5 82";

        // Act
        var result = _validator.ValidateHexString(hexString);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public void ValidateHexString_WithInvalidHexString_ShouldReturnFalse()
    {
        // Arrange
        var hexString = "7E F1 05 55 0A 00 00 10 FF FF"; // 错误的CRC

        // Act
        var result = _validator.ValidateHexString(hexString);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void ValidateHexString_WithMalformedHexString_ShouldReturnFalse()
    {
        // Arrange
        var hexString = "7E F1 05 55 0A 00 00 10 GG HH"; // 无效的十六进制字符

        // Act
        var result = _validator.ValidateHexString(hexString);

        // Assert
        result.Should().BeFalse();
    }

    [Theory]
    [InlineData("")]
    [InlineData("7E")]
    [InlineData("7E F1")]
    public void ValidateHexString_WithInsufficientData_ShouldReturnFalse(string hexString)
    {
        // Act
        var result = _validator.ValidateHexString(hexString);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public void CalculateCrc16_WithEmptyData_ShouldReturnInitialValue()
    {
        // Arrange
        var emptyData = Array.Empty<byte>();

        // Act
        var result = _validator.CalculateCrc16(emptyData);

        // Assert
        result.Should().Be(0xFFFF); // CRC16的初始值
    }

    [Fact]
    public void CalculateCrc16_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _validator.CalculateCrc16(null!));
    }

    [Fact]
    public void Validate_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _validator.Validate(null!));
    }

    [Fact]
    public void CalculateChecksum_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _validator.CalculateChecksum(null!));
    }

    [Fact]
    public void AddChecksum_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _validator.AddChecksum(null!));
    }

    [Fact]
    public void RemoveChecksum_WithNullData_ShouldThrowException()
    {
        // Act & Assert
        Assert.Throws<ArgumentNullException>(() => _validator.RemoveChecksum(null!));
    }

    [Fact]
    public void Validate_ShouldLogCorrectly()
    {
        // Arrange
        var corruptedData = (byte[])_validTestData.Clone();
        corruptedData[^1] = 0x00;

        // Act
        _validator.Validate(corruptedData);

        // Assert
        // 验证日志记录（这里只是示例，实际实现可能需要更复杂的日志验证）
        _mockLogger.Verify(
            x => x.Log(
                LogLevel.Trace,
                It.IsAny<EventId>(),
                It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("CRC16校验")),
                It.IsAny<Exception>(),
                It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
            Times.Once);
    }

    [Fact]
    public void RoundTrip_AddAndRemoveChecksum_ShouldReturnOriginalData()
    {
        // Arrange
        var originalData = new byte[] { 0x7E, 0x01, 0x02, 0x03, 0x05, 0xAA, 0xBB };

        // Act
        var dataWithChecksum = _validator.AddChecksum(originalData);
        var restoredData = _validator.RemoveChecksum(dataWithChecksum);

        // Assert
        restoredData.Should().Equal(originalData);
        _validator.Validate(dataWithChecksum).Should().BeTrue();
    }

    public void Dispose()
    {
        // 清理资源
    }
}
