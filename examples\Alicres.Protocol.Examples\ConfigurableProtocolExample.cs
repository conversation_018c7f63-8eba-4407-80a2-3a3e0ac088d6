using Alicres.Protocol.Configuration;
using Alicres.Protocol.Parsers;
using Alicres.Protocol.Protocols.Custom;
using Alicres.Protocol.Validators;
using Microsoft.Extensions.Logging;

namespace Alicres.Protocol.Examples;

/// <summary>
/// 可配置协议示例
/// 演示如何使用新的可配置协议系统
/// </summary>
public static class ConfigurableProtocolExample
{
    /// <summary>
    /// 运行可配置协议示例
    /// </summary>
    public static async Task RunExample()
    {
        Console.WriteLine("\n=== 可配置协议系统示例 ===");
        Console.WriteLine("演示新的通用协议解析架构");
        Console.WriteLine();

        // 创建日志工厂
        using var loggerFactory = LoggerFactory.Create(builder =>
            builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        try
        {
            // 示例1：使用标准配置解析原始协议
            await Example1_StandardConfiguration(loggerFactory);
            
            // 示例2：创建自定义配置
            await Example2_CustomConfiguration(loggerFactory);
            
            // 示例3：多字节帧头协议
            await Example3_MultiByteHeader(loggerFactory);
            
            // 示例4：向后兼容性演示
            await Example4_BackwardCompatibility(loggerFactory);
            
            // 示例5：动态协议配置
            await Example5_DynamicConfiguration(loggerFactory);

            Console.WriteLine("✅ 可配置协议系统示例完成");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ 示例执行失败: {ex.Message}");
            Console.WriteLine($"详细错误: {ex}");
        }
    }

    /// <summary>
    /// 示例1：使用标准配置解析原始协议
    /// </summary>
    private static async Task Example1_StandardConfiguration(ILoggerFactory loggerFactory)
    {
        Console.WriteLine("📋 示例1：标准配置解析原始协议");
        Console.WriteLine("协议数据：7E F1 05 55 0A 00 00 10 F5 82");

        // 创建标准配置
        var config = CustomProtocolConfiguration.CreateStandardConfiguration();
        var parser = new ConfigurableProtocolParser(config, loggerFactory.CreateLogger<ConfigurableProtocolParser>());

        // 解析示例数据
        var message = await parser.ParseFromHexStringAsync("7E F1 05 55 0A 00 00 10 F5 82");
        if (message != null)
        {
            Console.WriteLine("✓ 解析成功！");
            Console.WriteLine($"   协议名称: {message.ProtocolName}");
            Console.WriteLine($"   帧头: 0x{message.GetFieldValue<byte[]>("FrameHeader")?[0]:X2}");
            Console.WriteLine($"   源地址: 0x{message.GetFieldValue<byte>("SourceAddress"):X2}");
            Console.WriteLine($"   目标地址: 0x{message.GetFieldValue<byte>("TargetAddress"):X2}");
            Console.WriteLine($"   命令码: 0x{message.GetFieldValue<byte>("CommandCode"):X2}");
            Console.WriteLine($"   数据长度: {message.GetFieldValue<byte>("DataLength")}");
            
            var dataField = message.GetFieldValue<byte[]>("DataField");
            Console.WriteLine($"   数据字段: {(dataField?.Length > 0 ? BitConverter.ToString(dataField) : "无")}");
            Console.WriteLine($"   CRC16: 0x{message.GetFieldValue<ushort>("CRC16"):X4}");
        }
        else
        {
            Console.WriteLine("❌ 解析失败");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 示例2：创建自定义配置
    /// </summary>
    private static async Task Example2_CustomConfiguration(ILoggerFactory loggerFactory)
    {
        Console.WriteLine("📋 示例2：自定义协议配置");

        // 创建自定义配置
        var config = new ProtocolConfiguration
        {
            Name = "MyCustomProtocol",
            Version = "2.0.0",
            Description = "我的自定义协议",
            MinFrameLength = 6,
            MaxFrameLength = 128
        };

        // 定义协议字段
        config.AddField(ProtocolFieldDescriptor.CreateFixedValue("Header", new byte[] { 0xAA, 0x55 }, "双字节帧头"))
              .AddField(ProtocolFieldDescriptor.CreateByte("DeviceId", "设备ID"))
              .AddField(ProtocolFieldDescriptor.CreateUInt16("DataLength", ByteOrder.BigEndian, "数据长度（大端）"))
              .AddField(ProtocolFieldDescriptor.CreateVariableData("Payload", "负载数据"))
              .AddField(ProtocolFieldDescriptor.CreateByte("Checksum", "简单校验和"));

        config.SetFrameHeader("Header")
              .SetLengthField("DataLength")
              .SetDataField("Payload")
              .SetChecksumField("Checksum");

        var parser = new ConfigurableProtocolParser(config, loggerFactory.CreateLogger<ConfigurableProtocolParser>());

        // 创建测试消息
        var testMessage = parser.CreateMessage(
            ("DeviceId", (byte)0x01),
            ("DataLength", (ushort)8), // 总长度：2(帧头) + 1(设备ID) + 2(长度) + 2(数据) + 1(校验) = 8
            ("Payload", new byte[] { 0x12, 0x34 }),
            ("Checksum", (byte)0xFF)
        );

        Console.WriteLine($"✓ 创建自定义消息: {testMessage}");

        // 序列化消息
        var serialized = await parser.SerializeAsync(testMessage);
        var hexString = BitConverter.ToString(serialized).Replace("-", " ");
        Console.WriteLine($"   序列化结果: {hexString}");

        Console.WriteLine();
    }

    /// <summary>
    /// 示例3：多字节帧头协议
    /// </summary>
    private static async Task Example3_MultiByteHeader(ILoggerFactory loggerFactory)
    {
        Console.WriteLine("📋 示例3：多字节帧头协议");

        // 创建多字节帧头配置
        var config = CustomProtocolConfiguration.CreateMultiByteHeaderConfiguration("AA55BB");
        var parser = new ConfigurableProtocolParser(config, loggerFactory.CreateLogger<ConfigurableProtocolParser>());

        Console.WriteLine($"✓ 创建多字节帧头协议: {config.Name}");
        Console.WriteLine($"   帧头: {BitConverter.ToString(config.GetFrameHeaderField()?.FixedValue ?? Array.Empty<byte>())}");

        // 创建测试消息
        var frameHeaderLength = config.GetFrameHeaderField()?.FixedValue?.Length ?? 0;
        var totalLength = frameHeaderLength + 3 + 1 + 1 + 2; // 帧头 + 源地址 + 目标地址 + 命令码 + 长度字段 + 数据(1字节) + CRC16(2字节)

        var message = parser.CreateMessage(
            ("SourceAddress", (byte)0x10),
            ("TargetAddress", (byte)0x20),
            ("CommandCode", (byte)0x30),
            ("DataLength", (byte)totalLength),
            ("DataField", new byte[] { 0x99 })
        );

        var serialized = await parser.SerializeAsync(message);
        var hexString = BitConverter.ToString(serialized).Replace("-", " ");
        Console.WriteLine($"   生成的消息: {hexString}");

        Console.WriteLine();
    }

    /// <summary>
    /// 示例4：向后兼容性演示
    /// </summary>
    private static async Task Example4_BackwardCompatibility(ILoggerFactory loggerFactory)
    {
        Console.WriteLine("📋 示例4：向后兼容性演示");

        // 使用 V2 版本的兼容类
        var parserV2 = new CustomProtocolParserV2(loggerFactory.CreateLogger<CustomProtocolParserV2>());

        // 解析原始协议数据
        var messageV2 = await parserV2.ParseFromHexStringAsyncV2("7E F1 05 55 0A 00 00 10 F5 82");
        if (messageV2 != null)
        {
            Console.WriteLine("✓ V2兼容解析成功！");
            Console.WriteLine($"   源地址: 0x{messageV2.SourceAddress:X2}");
            Console.WriteLine($"   目标地址: 0x{messageV2.TargetAddress:X2}");
            Console.WriteLine($"   命令码: 0x{messageV2.CommandCode:X2}");
            Console.WriteLine($"   数据: {BitConverter.ToString(messageV2.DataField)}");
            Console.WriteLine($"   CRC16: 0x{messageV2.Crc16:X4}");
        }

        // 创建新消息（使用旧接口）
        var newMessage = parserV2.CreateMessage(0x02, 0x03, 0x04, new byte[] { 0xAA, 0xBB });
        var serialized = await parserV2.SerializeAsync(newMessage);
        var hexString = BitConverter.ToString(serialized).Replace("-", " ");
        Console.WriteLine($"✓ V2兼容创建消息: {hexString}");

        Console.WriteLine();
    }

    /// <summary>
    /// 示例5：动态协议配置
    /// </summary>
    private static async Task Example5_DynamicConfiguration(ILoggerFactory loggerFactory)
    {
        Console.WriteLine("📋 示例5：动态协议配置");

        // 运行时动态创建协议配置
        var protocols = new[]
        {
            ("单字节帧头", CustomProtocolConfiguration.CreateConfigurableConfiguration(new byte[] { 0x7E })),
            ("双字节帧头", CustomProtocolConfiguration.CreateConfigurableConfiguration(new byte[] { 0xAA, 0x55 })),
            ("三字节帧头", CustomProtocolConfiguration.CreateConfigurableConfiguration(new byte[] { 0xFF, 0xFE, 0xFD }))
        };

        foreach (var (name, config) in protocols)
        {
            Console.WriteLine($"🔧 测试 {name}:");
            
            var parser = new ConfigurableProtocolParser(config, loggerFactory.CreateLogger<ConfigurableProtocolParser>());
            
            // 创建测试消息
            var frameHeader = config.GetFrameHeaderField()?.FixedValue ?? Array.Empty<byte>();
            var totalLength = frameHeader.Length + 5; // 帧头 + 源地址 + 目标地址 + 命令码 + 长度 + CRC16(2)
            
            var message = parser.CreateMessage(
                ("SourceAddress", (byte)0x11),
                ("TargetAddress", (byte)0x22),
                ("CommandCode", (byte)0x33),
                ("DataLength", (byte)totalLength),
                ("DataField", Array.Empty<byte>())
            );

            var serialized = await parser.SerializeAsync(message);
            var hexString = BitConverter.ToString(serialized).Replace("-", " ");
            Console.WriteLine($"   生成消息: {hexString}");
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 演示协议配置验证
    /// </summary>
    public static void DemonstrateConfigurationValidation()
    {
        Console.WriteLine("\n=== 协议配置验证演示 ===");

        // 测试有效配置
        var validConfig = CustomProtocolConfiguration.CreateStandardConfiguration();
        var (isValid, errors) = validConfig.Validate();
        Console.WriteLine($"✓ 有效配置验证: {(isValid ? "通过" : "失败")}");

        // 测试无效配置
        var invalidConfig = new ProtocolConfiguration(); // 空配置
        var (isInvalid, invalidErrors) = invalidConfig.Validate();
        Console.WriteLine($"❌ 无效配置验证: {(isInvalid ? "通过" : "失败")}");
        if (!isInvalid)
        {
            Console.WriteLine("   错误信息:");
            foreach (var error in invalidErrors)
            {
                Console.WriteLine($"   - {error}");
            }
        }

        Console.WriteLine();
    }

    /// <summary>
    /// 性能对比测试
    /// </summary>
    public static async Task PerformanceComparison()
    {
        Console.WriteLine("\n=== 性能对比测试 ===");

        const int testCount = 1000;
        var testData = "7E F1 05 55 0A 00 00 10 F5 82";

        // 测试新的可配置解析器
        var config = CustomProtocolConfiguration.CreateStandardConfiguration();
        var configurableParser = new ConfigurableProtocolParser(config);

        var configurableStopwatch = System.Diagnostics.Stopwatch.StartNew();
        for (int i = 0; i < testCount; i++)
        {
            await configurableParser.ParseFromHexStringAsync(testData);
        }
        configurableStopwatch.Stop();

        // 测试兼容性解析器
        var compatibleParser = new CustomProtocolParserV2();

        var compatibleStopwatch = System.Diagnostics.Stopwatch.StartNew();
        for (int i = 0; i < testCount; i++)
        {
            await compatibleParser.ParseFromHexStringAsyncV2(testData);
        }
        compatibleStopwatch.Stop();

        Console.WriteLine($"   可配置解析器: {testCount} 次解析耗时 {configurableStopwatch.ElapsedMilliseconds} ms，平均 {(double)configurableStopwatch.ElapsedMilliseconds / testCount:F2} ms/次");
        Console.WriteLine($"   兼容性解析器: {testCount} 次解析耗时 {compatibleStopwatch.ElapsedMilliseconds} ms，平均 {(double)compatibleStopwatch.ElapsedMilliseconds / testCount:F2} ms/次");

        var performanceRatio = (double)compatibleStopwatch.ElapsedMilliseconds / configurableStopwatch.ElapsedMilliseconds;
        Console.WriteLine($"   性能比率: {performanceRatio:F2}x");

        Console.WriteLine();
    }
}
